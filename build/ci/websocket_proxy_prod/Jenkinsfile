pipeline {
    agent {
        label "aliyun-slave-vg"
    }
    tools {
        go 'Go1.23.4'
    }
    environment {
        Tag = sh(returnStdout: true,script: 'git describe --abbrev=1 --tags || echo ${Tag}').trim()
    }
    stages {
        stage("Build") {
            steps {
                echo "Starting to build program"
                echo "WORKSPACE: ${WORKSPACE}"
                echo "GOROOT: ${GOROOT}"
                echo "TAG: ${Tag}"
                echo "PATH: ${PATH}"
                sh "env"
                sh 'go version'
                sh 'go env -w GOPRIVATE=gitlab.mobvista.com'
                sh 'go env'
                sh 'git config --global url."***********************:".insteadOf "http://gitlab.mobvista.com/"'
                sh 'rm -rf build/docker/websocket_proxy/websocket_proxy'
                // sh 'go mod download'
                sh 'go mod tidy'
                sh 'CGO_ENABLED=0 GOOS=linux GOARCH=amd64 go build -o build/docker/websocket_proxy/websocket_proxy cmd/websocket_proxy/websocket_proxy.go'
            }
        }
        stage("Test") {
            steps {
                echo "Starting to test program"
            }
        }
        stage("Docker build") {
            steps {
                echo 'Starting to build docker image'
                sh 'cd build/docker/websocket_proxy/'
                script {
                    docker.withServer('tcp://localhost:2375') {
                        docker.withRegistry('https://registry.cn-hongkong.aliyuncs.com/', '2ab52349-a3da-4f29-82d4-76e2d0862f88') {
                            def image = docker.build("registry.cn-hongkong.aliyuncs.com/spotmax/websocket_proxy:${env.Tag}", "--no-cache -f ./build/docker/websocket_proxy/Dockerfile ./build/docker/websocket_proxy")
                            image.push()
                        }
                    }
                }
            }
        }
        stage("RolloutJob") {
            steps {
                sh '''
                #!/bin/bash
                image="registry.cn-hongkong.aliyuncs.com/spotmax/websocket_proxy:${Tag}"
                secret="ec043e8175e0cfcdcad8385622d5ebdf"
                sign=`echo -n "${image}|${secret}" | md5sum | awk '{print $1}'`
                curl --location --request POST 'https://maxcloud-api.spotmaxtech.com/api/external/bundle/upgrade' \
                --header 'Content-Type: application/json' \
                --data '{
                    "bundle_id": 1753779673978475,
                    "resource_name": "websocket-proxy",
                    "resource_kind": "deployment",
                    "image": "'$image'",
                    "sign": "'$sign'"
                }'
                '''
            }
        }
    }
}
