pipeline {
    agent {
        label "aliyun-slave-vg"
    }
    tools {
        go 'Go1.23.4'
    }
    environment {
        Tag = sh(returnStdout: true,script: 'git describe --abbrev=1 --tags || echo ${Tag}').trim()
    }
    stages {
        stage("Build") {
            steps {
                echo "Starting to build program"
                echo "WORKSPACE: ${WORKSPACE}"
                echo "GOROOT: ${GOROOT}"
                echo "TAG: ${Tag}"
                echo "PATH: ${PATH}"
                sh "env"
                sh 'go version'
                sh 'go env -w GOPRIVATE=gitlab.mobvista.com'
                sh 'go env'
                sh 'git config --global url."***********************:".insteadOf "http://gitlab.mobvista.com/"'
                sh 'rm -rf build/docker/jarvis_api/jarvis_api'
                // sh 'go mod download'
                sh 'go mod tidy'
                sh 'CGO_ENABLED=0 GOOS=linux GOARCH=amd64 go build -o build/docker/jarvis_api/jarvis_api cmd/jarvis_api/jarvis_api.go'
            }
        }
        stage("Test") {
            steps {
                echo "Starting to test program"
            }
        }
        stage("Docker build") {
            steps {
                echo 'Starting to build docker image'
                sh 'cd build/docker/jarvis_api/'
                script {
                    docker.withServer('tcp://localhost:2375') {
                        docker.withRegistry('https://registry.cn-hongkong.aliyuncs.com/', '2ab52349-a3da-4f29-82d4-76e2d0862f88') {
                            def image = docker.build("registry.cn-hongkong.aliyuncs.com/spotmax/jarvis_api:${env.Tag}", "--no-cache -f ./build/docker/jarvis_api/Dockerfile ./build/docker/jarvis_api")
                            image.push()
                        }
                    }
                }
            }
        }

                stage("RolloutJob") {
            steps {
                sh '''
                #!/bin/bash
                image="registry.cn-hongkong.aliyuncs.com/spotmax/jarvis_api:${Tag}"
                secret="65d8596d787e2a0286248f32d7ece644"
                sign=`echo -n "${image}|${secret}" | md5sum | awk '{print $1}'`
                curl --location --request POST 'https://maxcloud-api.spotmaxtech.com/api/external/bundle/upgrade' \
                --header 'Content-Type: application/json' \
                --data '{
                    "bundle_id": 1753779440382340,
                    "resource_name": "maxagent-api",
                    "resource_kind": "deployment",
                    "image": "'$image'",
                    "sign": "'$sign'"
                }'
                '''
            }
        }
    }
}
