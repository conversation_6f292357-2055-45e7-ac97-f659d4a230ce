pipeline {
    agent {
        label "aliyun-slave-vg"
    }

    environment {
        Tag = sh(returnStdout: true,script: 'git describe --abbrev=1 --tags || echo ${Tag}').trim()
    }
    stages {
        stage("Docker build") {
            steps {
                echo 'Starting to build docker image'
                script {
                    docker.withServer('tcp://localhost:2375') {
                        docker.withRegistry('https://registry.cn-hongkong.aliyuncs.com/', '2ab52349-a3da-4f29-82d4-76e2d0862f88') {
                            def image = docker.build("registry.cn-hongkong.aliyuncs.com/spotmax/maxagent-runner-base:${env.Tag}", "--no-cache -f ./build/docker/base/Dockerfile ./")
                            image.push()
                        }
                    }
                }
            }
        }
    }
}
