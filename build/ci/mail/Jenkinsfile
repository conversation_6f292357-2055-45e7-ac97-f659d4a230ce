pipeline {
    agent {
        label "aliyun-slave-vg"
    }

    environment {
        Tag = sh(returnStdout: true,script: 'git describe --abbrev=1 --tags || echo ${Tag}').trim()
    }
    stages {
        stage("Docker build") {
            steps {
                echo 'Starting to build docker image'
                script {
                    docker.withServer('tcp://localhost:2375') {
                        docker.withRegistry('https://registry.cn-hongkong.aliyuncs.com/', '2ab52349-a3da-4f29-82d4-76e2d0862f88') {
                            def image = docker.build("registry.cn-hongkong.aliyuncs.com/spotmax/maxagent-mail-service:${env.Tag}", "--no-cache -f ./build/docker/mail/Dockerfile ./")
                            image.push()
                        }
                    }
                }
            }
        }
        stage("RolloutJob") {
            steps {
                sh '''       
                #!/bin/bash
                image="registry.cn-hongkong.aliyuncs.com/spotmax/maxagent-mail-service:${Tag}"
                secret="5c51cc115e44314f47999db9c06962c2"
                sign=`echo -n "${image}|${secret}" | md5sum | awk '{print $1}'`
                curl --location --request POST 'https://maxcloud-api.spotmaxtech.com/api/external/bundle/upgrade' \
                --header 'Content-Type: application/json' \
                --data '{
                    "bundle_id": 1745485898915655,
                    "resource_name": "maxagent-mailservice",
                    "resource_kind": "deployment",
                    "image": "'$image'",
                    "sign": "'$sign'"
                }'
                 '''
             }
         }
    }
}


