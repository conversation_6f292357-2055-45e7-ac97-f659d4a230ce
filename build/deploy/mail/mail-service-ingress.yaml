apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: maxagent-mailservice.spotmaxtech.com
  namespace: maxagent-test
  annotations:
    nginx.ingress.kubernetes.io/force-ssl-redirect: "true"
    nginx.ingress.kubernetes.io/proxy-send-timeout: "3600"
    nginx.ingress.kubernetes.io/proxy-read-timeout: "3600"
spec:
  ingressClassName: nginx
  tls:
    - hosts:
        - maxagent-mailservice.spotmaxtech.com
      secretName: spotmaxtech-secret
  rules:
  - host: maxagent-mailservice.spotmaxtech.com
    http:
      paths:
      - backend:
          service:
            name: maxagent-mailservice
            port:
              number: 80
        path: /
        pathType: Prefix