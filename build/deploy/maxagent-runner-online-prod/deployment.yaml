apiVersion: apps/v1
kind: Deployment
metadata:
  name: maxagent-runner-online
  namespace: maxagent-prod
spec:
  replicas: 1
  selector:
    matchLabels:
      app: maxagent-runner-online
  template:
    metadata:
      name: maxagent-runner-online
      labels:
        app: maxagent-runner-online
    spec:
      affinity:
        nodeAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
            nodeSelectorTerms:
              - matchExpressions:
                  - key: Name
                    operator: In
                    values:
                      - tk_max-agent
      tolerations:
        - key: "dedicated"
          operator: "Equal"
          value: "max-agent"
          effect: "NoSchedule"
      serviceAccountName: maxagent-sa
      imagePullSecrets:
        - name: ali-ecr
      containers:
        - image: registry.cn-hongkong.aliyuncs.com/spotmax/maxagent-runner:v0.0.11-6-g49aaa
          name: maxagent-runner-online
          command: ["python"]
          args: ["src/main_online.py"]
          ports:
            - containerPort: 8080
              name: httpwithmetrics
              protocol: TCP
          securityContext:
            privileged: true
          env:
          - name: GOOG<PERSON>_APPLICATION_CREDENTIALS
            value: /maxagent/config/google/google-credentials.json
          - name: DOT_PATH
            value: /maxagent/config/env/.env
          volumeMounts:
          - mountPath: /maxagent/config/google/
            name: google-credentials
            readOnly: true
          - mountPath: /maxagent/config/env/
            name: env
            readOnly: true
      volumes:
      - name: google-credentials
        secret:
          items:
          - key: google-credentials
            path: google-credentials.json
          secretName: maxagent-runner-secret
      - name: env
        secret:
          items:
          - key: .env
            path: .env
          secretName: maxagent-runner-secret
---
apiVersion: v1
kind: Service
metadata:
  name: maxagent-runner-online
  namespace: maxagent-prod
spec:
  ports:
    - port: 80
      targetPort: 8080
  selector:
    app: maxagent-runner-online
