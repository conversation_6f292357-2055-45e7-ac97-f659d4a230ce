FROM python:3.11.2

WORKDIR /maxagent

# https://googlechromelabs.github.io/chrome-for-testing/
RUN apt-get update && apt-get install -y bash gcc g++  coreutils  tzdata jq curl\
    gettext git librsvg2-bin libnss3 gconf2 && \
    wget https://www.sqlite.org/2025/sqlite-autoconf-3490100.tar.gz && \
    tar xvf sqlite-autoconf-3490100.tar.gz && \
    cd sqlite-autoconf-3490100 && \
    ./configure --prefix=/usr/local --enable-fts5 && \
    make && make install && \
    ldconfig && \
    sqlite3 --version && \
    cd .. && \
    rm -rf sqlite-autoconf-3490100 sqlite-autoconf-3490100.tar.gz && \
    wget -q -O - https://dl-ssl.google.com/linux/linux_signing_key.pub | apt-key add - && \
    rm -rf /var/cache/apk/*

# 复制上海时区
RUN cp /usr/share/zoneinfo/Asia/Shanghai /etc/localtime
# 指定为上海时区
RUN echo "Asia/Shanghai" > /etc/timezone

COPY requirements_base.txt requirements.txt



# https://pytorch.org/get-started/locally/
RUN pip install --upgrade pip && \
    pip3 install --no-cache-dir -r requirements.txt