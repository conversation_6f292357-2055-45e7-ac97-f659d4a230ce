apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  annotations:
    nginx.ingress.kubernetes.io/force-ssl-redirect: "true"
    nginx.ingress.kubernetes.io/proxy-body-size: 256m
    nginx.ingress.kubernetes.io/proxy-connect-timeout: "3600"
    nginx.ingress.kubernetes.io/proxy-next-upstream-timeout: "3600"
    nginx.ingress.kubernetes.io/proxy-read-timeout: "3600"
    nginx.ingress.kubernetes.io/proxy-send-timeout: "3600"
  name: maxagent-api.spotmaxtech.com
  namespace: maxagent-prod
spec:
  ingressClassName: nginx
  rules:
  - host: maxagent-api.spotmaxtech.com
    http:
      paths:
      - backend:
          service:
            name: maxagent-api
            port:
              number: 80
        path: /api
        pathType: Prefix
  tls:
  - hosts:
    - maxagent-api.spotmaxtech.com
    secretName: spotmaxtech-secret