apiVersion: v1
kind: ServiceAccount
metadata:
  name: maxagent-sa
  namespace: maxagent-prod

---

apiVersion: rbac.authorization.k8s.io/v1
kind: Role  # 也可以使用 ClusterRole 进行集群范围授权
metadata:
  name: maxagent-sa
  namespace: maxagent-prod
rules:
- apiGroups: [""]
  resources: ["pods", "services"]
  verbs: ["get", "list", "watch", "create", "update", "patch", "delete"]
- apiGroups: ["networking.k8s.io", "extensions"]  # 包含两种版本的 Ingress API
  resources: ["ingresses"]
  verbs: ["get", "list", "watch", "create", "update", "patch", "delete"]

---

apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: maxagent-sa
  namespace: maxagent-prod
subjects:
- kind: ServiceAccount
  name: maxagent-sa
  namespace: maxagent-prod
roleRef:
  kind: Role
  name: maxagent-sa
  apiGroup: rbac.authorization.k8s.io
