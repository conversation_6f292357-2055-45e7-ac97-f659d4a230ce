apiVersion: apps/v1
kind: Deployment
metadata:
  name: maxagent-api
  namespace: maxagent-test
spec:
  replicas: 1
  selector:
    matchLabels:
      app: maxagent-api
  template:
    metadata:
      name: maxagent-api
      labels:
        app: maxagent-api
    spec:
      affinity:
        nodeAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
            nodeSelectorTerms:
            - matchExpressions:
              - key: Name
                operator: In
                values:
                - tk_max-agent
      tolerations:
        - key: "dedicated"
          operator: "Equal"
          value: "max-agent"
          effect: "NoSchedule"
      serviceAccountName: maxagent-sa
      imagePullSecrets:
        - name: ali-ecr
      containers:
        - image: registry.cn-hongkong.aliyuncs.com/spotmax/jarvis_api:v0.0.1-97-g97a4
          name: maxagent-api
          ports:
            - containerPort: 8000
              name: httpwithmetrics
              protocol: TCP
          env:
            - name: GIN_MODE
              value: test
            - name: AWS_ACCESS_KEY_ID
              valueFrom:
                secretKeyRef:
                  name: aws-key
                  key: key
            - name: AWS_SECRET_ACCESS_KEY
              valueFrom:
                secretKeyRef:
                  name: aws-key
                  key: secret
            - name: AWS_S3_ACCESS_KEY_ID
              valueFrom:
                secretKeyRef:
                  name: aws-key
                  key: aws_s3_access_key_id
            - name: AWS_S3_SECRET_ACCESS_KEY
              valueFrom:
                secretKeyRef:
                  name: aws-key
                  key: aws_s3_secret_access_key
            - name: SSO_APP_KEY
              valueFrom:
                secretKeyRef:
                  key: sso_app_key
                  name: maxagent-secret
            - name: SSO_APP_SECRET
              valueFrom:
                secretKeyRef:
                  key: sso_app_secret
                  name: maxagent-secret
          securityContext:
              privileged: true
---
apiVersion: v1
kind: Service
metadata:
  name: maxagent-api
  namespace: maxagent-test
spec:
  ports:
    - port: 80
      targetPort: 8000
  selector:
    app: maxagent-api
