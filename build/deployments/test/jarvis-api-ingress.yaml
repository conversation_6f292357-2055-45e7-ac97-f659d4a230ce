apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: maxagent-test-api.spotmaxtech.com
  namespace: maxagent-test
  annotations:
    nginx.ingress.kubernetes.io/force-ssl-redirect: "true"
    nginx.ingress.kubernetes.io/proxy-send-timeout: "3600"
    nginx.ingress.kubernetes.io/proxy-read-timeout: "3600"
spec:
  ingressClassName: nginx
  tls:
    - hosts:
        - maxagent-test-api.spotmaxtech.com
      secretName: spotmaxtech-secret
  rules:
  - host: maxagent-test-api.spotmaxtech.com
    http:
      paths:
      - backend:
          service:
            name: maxagent-api
            port:
              number: 80
        path: /api
        pathType: Prefix