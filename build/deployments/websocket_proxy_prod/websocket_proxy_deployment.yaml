apiVersion: apps/v1
kind: Deployment
metadata:
  name: websocket-proxy
  namespace: maxagent-prod
spec:
  replicas: 1
  selector:
    matchLabels:
      app: websocket-proxy
  template:
    metadata:
      name: websocket-proxy
      labels:
        app: websocket-proxy
    spec:
      affinity:
        nodeAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
            nodeSelectorTerms:
            - matchExpressions:
              - key: Name
                operator: In
                values:
                - tk_max-agent
      tolerations:
        - key: "dedicated"
          operator: "Equal"
          value: "max-agent"
          effect: "NoSchedule"
      serviceAccountName: maxagent-sa
      imagePullSecrets:
        - name: ali-ecr
      containers:
        - image: registry.cn-hongkong.aliyuncs.com/spotmax/websocket_proxy:v0.0.1-86-g46c3
          name: websocket-proxy
          ports:
            - containerPort: 8765
              name: httpwithmetrics
              protocol: TCP
          env:
            - name: GIN_MODE
              value: release
          securityContext:
              privileged: true
---
apiVersion: v1
kind: Service
metadata:
  name: websocket-proxy
  namespace: maxagent-prod
spec:
  ports:
    - port: 80
      targetPort: 8765
  selector:
    app: websocket-proxy
