apiVersion: apps/v1
kind: Deployment
metadata:
  name: test-podid
  namespace: crazywolf
spec:
  replicas: 2
  selector:
    matchLabels:
      app: test-podid
  template:
    metadata:
      labels:
        app: test-podid
    spec:
      containers:
        - name: test-container
          image: busybox
          command: ["sh", "-c"]
          args:
            - while true; do
              echo -en '\n';
              printenv POD_ID ACS_CLIENT_SECRET;
              sleep 10;
              done;
          env:
            - name: POD_ID
              valueFrom:
                fieldRef:
                  fieldPath: metadata.uid
            - name: ACS_CLIENT_SECRET
              valueFrom:
                secretKeyRef:
                  key: acs_client_secret
                  name: maxcloud-secret
