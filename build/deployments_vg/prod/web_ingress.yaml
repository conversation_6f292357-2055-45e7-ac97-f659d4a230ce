apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  annotations:
    nginx.ingress.kubernetes.io/force-ssl-redirect: "true"
    nginx.ingress.kubernetes.io/proxy-read-timeout: "3600"
    nginx.ingress.kubernetes.io/proxy-send-timeout: "3600"
  creationTimestamp: "2025-03-26T07:12:52Z"
  generation: 1
  name: maxagent.spotmaxtech.com
  namespace: maxagent-prod
  resourceVersion: "333592981"
  uid: 643d3061-6cc5-47b7-92b1-f13f00ac79f5
spec:
  ingressClassName: nginx
  rules:
  - host: maxagent.spotmaxtech.com
    http:
      paths:
      - backend:
          service:
            name: maxagent-web
            port:
              number: 80
        path: /
        pathType: Prefix
  tls:
  - hosts:
    - maxagent.spotmaxtech.com
    secretName: spotmaxtech-secret
