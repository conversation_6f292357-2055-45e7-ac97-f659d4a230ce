apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: websocket-proxy-prod.spotmaxtech.com
  namespace: maxagent-prod
  annotations:
    nginx.ingress.kubernetes.io/force-ssl-redirect: "true"
    nginx.ingress.kubernetes.io/proxy-send-timeout: "3600"
    nginx.ingress.kubernetes.io/proxy-read-timeout: "3600"
spec:
  ingressClassName: nginx
  tls:
    - hosts:
        - maxagent-api.spotmaxtech.com
      secretName: spotmaxtech-secret
  rules:
  - host: maxagent-api.spotmaxtech.com
    http:
      paths:
      - backend:
          service:
            name: websocket-proxy
            port:
              number: 80
        path: /ws
        pathType: Prefix