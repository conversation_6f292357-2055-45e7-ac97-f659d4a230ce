apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: websocket-proxy-test.spotmaxtech.com
  namespace: maxagent-test
  annotations:
    kubernetes.io/ingress.class: alb
    alb.ingress.kubernetes.io/scheme: internet-facing
    alb.ingress.kubernetes.io/target-type: ip
    alb.ingress.kubernetes.io/listen-ports: '[{"HTTP": 80}, {"HTTPS": 443}]'
    alb.ingress.kubernetes.io/ssl-redirect: '443'
    alb.ingress.kubernetes.io/backend-protocol: HTTP
    alb.ingress.kubernetes.io/certificate-arn: arn:aws:acm:us-east-1:818539432014:certificate/459ffbe5-5cc2-4546-acac-3dd21fba9ec3
spec:
  ingressClassName: alb
  tls:
    - hosts:
        - maxagent-test-api.spotmaxtech.com
      secretName: spotmaxtech-secret
  rules:
  - host: maxagent-test-api.spotmaxtech.com
    http:
      paths:
      - backend:
          service:
            name: websocket-proxy
            port:
              number: 80
        path: /ws
        pathType: Prefix