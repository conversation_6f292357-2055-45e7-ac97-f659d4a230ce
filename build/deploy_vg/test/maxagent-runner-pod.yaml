apiVersion: v1
kind: Pod
metadata:
  name: maxagent-runner
  namespace: maxagent-test
spec:
  affinity:
    nodeAffinity:
      requiredDuringSchedulingIgnoredDuringExecution:
        nodeSelectorTerms:
        - matchExpressions:
          - key: Name
            operator: In
            values:
            - tk_max-agent
  containers:
    - name: maxagent-runner
      image: registry.cn-hongkong.aliyuncs.com/spotmax/maxagent-runner:v0.0.2-24-g4249
      imagePullPolicy: IfNotPresent
      env:
        - name: RUNNER_RECORD_ID
          value: "1"
        - name: GOOGLE_APPLICATION_CREDENTIALS
          value: /maxagent/config/google/google-credentials.json
        - name: DOT_PATH
          value: /maxagent/config/env/.env
      ports:
        - containerPort: 8765
          name: httpwithmetrics
          protocol: TCP
      
      securityContext:
        privileged: true
      
      volumeMounts:
      - mountPath: /maxagent/config/google/
        name: google-credentials
        readOnly: true
      - mountPath: /maxagent/config/env/
        name: env
        readOnly: true
  imagePullSecrets:
  - name: ali-ecr
  restartPolicy: Never
  volumes:
  - name: google-credentials
    secret:
      items:
      - key: google-credentials
        path: google-credentials.json
      secretName: maxagent-runner-secret
  - name: env
    secret:
      items:
      - key: .env
        path: .env
      secretName: maxagent-runner-secret
  
  