apiVersion: v1
kind: Pod
metadata:
  creationTimestamp: "2025-03-28T12:29:21Z"
  labels:
    app: maxagent-runner-1905598082154139648
  name: maxagent-runner-1905598082154139648
  namespace: maxagent-test
  
spec:
  affinity:
    nodeAffinity:
      requiredDuringSchedulingIgnoredDuringExecution:
        nodeSelectorTerms:
        - matchExpressions:
          - key: Name
            operator: In
            values:
            - tk_max-agent
  containers:
  - env:
    - name: DEBUG
      value: "true"
    - name: RUNNER_RECORD_ID
      value: "1905598082154139648"
    - name: GOOGLE_APPLICATION_CREDENTIALS
      value: /maxagent/config/google-credentials.json
    - name: DOT_PATH
      value: /maxagent/config/env/.env
    image: registry.cn-hongkong.aliyuncs.com/spotmax/maxagent-runner:v0.0.4
    imagePullPolicy: IfNotPresent
    name: maxagent-runner
    ports:
    - containerPort: 8765
      name: httpwithmetrics
      protocol: TCP
    resources: {}
    securityContext:
      privileged: true
    terminationMessagePath: /dev/termination-log
    terminationMessagePolicy: File
    volumeMounts:
    - mountPath: /maxagent/config/google/
      name: google-credentials
      readOnly: true
    - mountPath: /maxagent/config/env/
      name: env
      readOnly: true
    - mountPath: /var/run/secrets/kubernetes.io/serviceaccount
      name: kube-api-access-zn2rr
      readOnly: true
  dnsPolicy: ClusterFirst
  enableServiceLinks: true
  imagePullSecrets:
  - name: ali-ecr
  nodeName: ip-172-31-11-201.ap-northeast-1.compute.internal
  preemptionPolicy: PreemptLowerPriority
  priority: 0
  restartPolicy: Never
  schedulerName: default-scheduler
  securityContext: {}
  serviceAccount: maxagent-sa
  serviceAccountName: maxagent-sa
  terminationGracePeriodSeconds: 30
  tolerations:
  - effect: NoExecute
    key: node.kubernetes.io/not-ready
    operator: Exists
    tolerationSeconds: 300
  - effect: NoExecute
    key: node.kubernetes.io/unreachable
    operator: Exists
    tolerationSeconds: 300
  volumes:
  - name: google-credentials
    secret:
      defaultMode: 420
      items:
      - key: google-credentials
        path: google-credentials.json
      secretName: maxagent-runner-secret
  - name: env
    secret:
      defaultMode: 420
      items:
      - key: .env
        path: .env
      secretName: maxagent-runner-secret
  - name: kube-api-access-zn2rr
    projected:
      defaultMode: 420
      sources:
      - serviceAccountToken:
          expirationSeconds: 3607
          path: token
      - configMap:
          items:
          - key: ca.crt
            path: ca.crt
          name: kube-root-ca.crt
      - downwardAPI:
          items:
          - fieldRef:
              apiVersion: v1
              fieldPath: metadata.namespace
            path: namespace

