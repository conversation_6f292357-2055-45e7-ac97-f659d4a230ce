apiVersion: apps/v1
kind: Deployment
metadata:
  name: maxagent-mailservice
  namespace: maxagent-test
spec:
  progressDeadlineSeconds: 600
  replicas: 1
  revisionHistoryLimit: 10
  selector:
    matchLabels:
      app: maxagent-mailservice
  template:
    metadata:
      labels:
        app: maxagent-mailservice
      name: maxagent-mailservice
    spec:
      containers:
      - env:
        - name: MAIL_BOX
          valueFrom:
            secretKeyRef:
              key: mail_box
              name: mailservice-secret
        - name: MB_PASS
          valueFrom:
            secretKeyRef:
              key: mail_pass
              name: mailservice-secret
        image: registry.cn-hongkong.aliyuncs.com/spotmax/maxagent-mail-service:v0.0.8-103-gf2af
        imagePullPolicy: IfNotPresent
        name: maxagent-mailservice
        ports:
        - containerPort: 5100
          name: httpwithmetrics
          protocol: TCP
      imagePullSecrets:
      - name: ali-ecr
      restartPolicy: Always
      schedulerName: default-scheduler
      securityContext: {}
      terminationGracePeriodSeconds: 30
---
apiVersion: v1
kind: Service
metadata:
  name: maxagent-mailservice
  namespace: maxagent-test
spec:
  ports:
    - port: 80
      targetPort: 5100
  selector:
    app: maxagent-mailservice
