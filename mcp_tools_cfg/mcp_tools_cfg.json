{"tools": [{"name": "math_server", "description": "Math server for arithmetic operations", "stdio_server_parameters": {"command": "python", "args": ["mcp_tools_cfg/math_server.py"]}}, {"name": "youtube_video_summarizer", "description": "Youtube video summarizer", "stdio_server_parameters": {"command": "npx", "args": ["-y", "youtube-video-summarizer-mcp"]}}, {"name": "mtg_data_query_tool", "description": "query the programtic ads business data from database", "server_parameters": {"url": "http://mcp-database-tools.mtg-ai-agent-internal.rayjump.com/mcp/sse", "transport": "sse"}}, {"name": "mintegral_demand_portal_tool", "description": "Managing Mintegral's programmatic advertising business demand offers", "server_parameters": {"url": "http://mcp-mtg-demand.mtg-ai-agent-internal.rayjump.com/sse", "transport": "sse"}}, {"name": "mtg_moss", "description": "moss of the mtg reporting system", "server_parameters": {"url": "http://mcp-moss.mtg-ai-agent-internal.rayjump.com:8110/mcp", "transport": "streamable-http"}}, {"name": "servicego-api-mcp", "description": "组件化中台的mcp server", "server_parameters": {"url": "http://a8c3d760493a343fba332798afc841e8-440660303.us-east-1.elb.amazonaws.com:8888/streamable/mcp", "transport": "streamable-http"}}, {"name": "smartcut_tool", "description": "Perform intelligent cropping on the target image, preserving the main subject content during cropping.", "server_parameters": {"url": "https://smartcut.spotmaxtech.com/mcp", "transport": "streamable-http"}}]}