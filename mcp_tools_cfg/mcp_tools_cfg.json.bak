{"tools": [{"name": "math_server", "description": "Math server for arithmetic operations", "stdio_server_parameters": {"command": "python", "args": ["mcp_tools_cfg/math_server.py"]}}, {"name": "youtube_video_summarizer", "description": "Youtube video summarizer", "stdio_server_parameters": {"command": "npx", "args": ["-y", "youtube-video-summarizer-mcp"]}}, {"name": "mtg_data_query_tool", "description": "query the programtic ads business data from database", "sse_server_parameters": {"url": "http://mcp-alchemy-nocodb.mtg-ai-agent-internal.rayjump.com/sse"}}, {"name": "desktop-commander", "description": "Execute terminal commands and manage files with diff editing capabilities. Coding, shell and terminal, task automation", "sse_server_parameters": {"url": "http://localhost:8000/mcp1/sse"}}]}