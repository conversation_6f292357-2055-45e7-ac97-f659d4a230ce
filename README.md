
google ai gemini 无法对接，原因：香港ip无法访问
1https://aistudio.google.com/app/apikey
https://ai.google.dev/gemini-api/docs/models/gemini?hl=zh-cn


开发分支
clean_pod 清理pod
connector connector管理
websocket-proxy websocket代理
knowledge: 知识库管理
upload_file: 上传文件（创建Team 支持上传文件）
agent_knowledge: agent 知识库
memory : agent 和 task 记忆
remote_triggering: 远程触发
white_list: 白名单
copy_task_uncopy：复制task 和 agent 默认设置为不可copy
mcp_tools: agent 增加multimodal
team_planning: todo deployment prod
python_node： python node 管理
path_router 路由
create_user_mcp_tool: 创建用户自己的mcp tool
auto_create_app 自动创建app