package main

import (
	"jarvis_api/pkg/controller"
	"jarvis_api/pkg/db"
	"jarvis_api/pkg/service"
	"jarvis_api/pkg/service/k8s"
	"jarvis_api/tools"
	"log"

	"github.com/gin-gonic/gin"
	"github.com/sirupsen/logrus"
)

func main() {
	funcs := tools.ApplicationInitList{}
	funcs.AddInitFunc(func() error {
		logrus.SetLevel(logrus.DebugLevel)
		logrus.SetReportCaller(true)
		logrus.SetFormatter(&logrus.JSONFormatter{})
		return nil
	})

	// 初始化Snowflake
	funcs.AddInitFunc(tools.InitSnowflake)
	// 数据库连接
	funcs.AddInitFunc(db.InitDBManager)
	// 启动定时任务
	funcs.AddInitFunc(service.StartCron)
	// 初始化 cache hold
	// funcs.AddInitFunc(service.InitCacheHold)
	// 启动dingding服务
	// funcs.AddInitFunc(service.InitDingdingService)
	// defer service.StopDingdingService()
	// 初始化Agent tools 列表
	//funcs.AddInitFunc(service.NewAgentToolService)
	funcs.AddInitFunc(k8s.NewK8sToolService)
	// 启动 api 服务
	funcs.AddInitFunc(initApi)

	err := funcs.Run(true)
	if err != nil {
		log.Fatal("service fatal error,", err)
	}
}

func initApi() error {
	engin := gin.Default()

	// 设置请求ID
	engin.Use(controller.SetRequestId)
	// 设置跨域
	engin.Use(controller.CorsConfig())

	api := engin.Group("/api/v1")

	// api.POST("/acslogin", controller.AcsLogin)
	api.POST("/ssologin", controller.SsoLogin)

	// 用户
	user := api.Group("/user", controller.MiddlewareToken)
	{
		user.GET("/info", controller.GetUserInfo)
		user.PUT("/reset_secret_key", controller.ResetSecretKey)
		user.POST("/apply_for_use", controller.ApplyForUse)
		user.GET("/search", controller.SearchUser)
	}

	// 应用
	app := api.Group("/app", controller.MiddlewareToken)
	{
		app.GET("/", controller.ListApplication)
		app.GET("/:id", controller.GetApplicationResWithMacro)
		app.POST("/", controller.CreateApplication)
		app.PUT("/:id", controller.UpdateApplication)
		app.DELETE("/:id", controller.DeleteApplication)
		app.POST("/reset_collaboration", controller.ResetCollaboration)
		//app.POST("/:id/_copy", controller.CopyApplication)

		//app.POST("/app_chat", controller.AppChat) // 应用聊天
		//app.GET("/get_app/:app_id", controller.GetApplication)
	}

	// teams
	team := api.Group("/team", controller.MiddlewareToken)
	{
		team.GET("/", controller.ListTeam)
		team.GET("/:id", controller.GetTeamResWithMacro)
		team.POST("/", controller.CreateTeam)
		team.PUT("/:id", controller.UpdateTeam)
		team.DELETE("/:id", controller.DeleteTeam)
		team.POST("/:id/_copy", controller.CopyTeam)
		team.POST("/auto_create", controller.AutoCreateTeam)
	}

	task := api.Group("/task", controller.MiddlewareToken)
	{
		task.GET("/", controller.ListTask)
		task.GET("/:id", controller.GetTask)
		task.POST("/", controller.CreateTask)
		task.PUT("/:id", controller.UpdateTask)
		task.DELETE("/:id", controller.DeleteTask)
		task.POST("/:id/_copy", controller.CopyTask)
	}

	agent := api.Group("/agent", controller.MiddlewareToken)
	{
		agent.GET("/", controller.ListAgent)
		agent.GET("/:id", controller.GetAgent)
		agent.POST("/", controller.CreateAgent)
		agent.PUT("/:id", controller.UpdateAgent)
		agent.DELETE("/:id", controller.DeleteAgent)
		agent.POST("/:id/_copy", controller.CopyAgent)
	}

	runner := api.Group("/runner", controller.MiddlewareToken)
	{
		runner.GET("/", controller.ListRunner)
		runner.POST("/list", controller.ListRunnerV2)
		runner.GET("/:id", controller.GetRunner)
		runner.GET("/:id/status", controller.GetRunnerStatus)
		runner.POST("/", controller.CreateRunner) // 创建sql记录，并创建pod，返回jobId（规则拼接）,供getStatus查询，ready后返回webServer addr
	}

	remote := api.Group("/remote", controller.MiddlewareSign)
	{
		remote.POST("/trigger_async", controller.RemoteTriggerAsync)            // 异步请求，创建pod执行
		remote.POST("/trigger_sync", controller.RemoteTriggerSync)              // 同步请求，在线模式
		remote.POST("/trigger_sync_stream", controller.RemoteTriggerSyncStream) // 同步请求，在线模式,流式返回
		remote.POST("/trigger_sync_step", controller.RemoteTriggerSyncStep)     // 同步请求，在线模式, 返回方法执行步骤
		remote.POST("/upload", controller.UploadFile)
	}

	system := api.Group("/system", controller.MiddlewareToken)
	{
		system.GET("/config", controller.GetSysconfig)
		system.POST("/config", controller.SaveSysconfig)
	}

	tool := api.Group("/tool", controller.MiddlewareToken)
	{
		tool.GET("/", controller.ListTool)
		tool.POST("/", controller.CreateTool)
		tool.PUT("/:id", controller.UpdateTool)
	}

	llm := api.Group("/llm", controller.MiddlewareToken)
	{
		llm.GET("/", controller.ListLlm)
	}

	connector := api.Group("/connector", controller.MiddlewareToken)
	{
		connector.GET("/", controller.ListConnector)
		connector.GET("/:id", controller.GetConnector)
		connector.POST("/", controller.CreateConnector)
		connector.PUT("/:id", controller.UpdateConnector)
		connector.DELETE("/:id", controller.DeleteConnector)
	}

	python_node := api.Group("/python_node", controller.MiddlewareToken)
	{
		python_node.GET("/", controller.ListPythonNode)
		python_node.GET("/:id", controller.GetPythonNode)
		python_node.POST("/", controller.CreatePythonNode)
		python_node.PUT("/:id", controller.UpdatePythonNode)
		python_node.DELETE("/:id", controller.DeletePythonNode)
	}

	knowledge := api.Group("/knowledge", controller.MiddlewareToken)
	{
		knowledge.GET("/", controller.ListKnowledge)
		knowledge.GET("/:id", controller.GetKnowledge)
		knowledge.POST("/", controller.CreateKnowledge)
		knowledge.PUT("/:id", controller.UpdateKnowledge)
		knowledge.DELETE("/:id", controller.DeleteKnowledge)
	}

	file := api.Group("/file", controller.MiddlewareToken)
	{
		file.POST("/upload_knowledge", controller.UploadKnowledgeFile)
		file.POST("/upload_data", controller.UploadDataFile)
		file.POST("/upload", controller.UploadFile)
	}

	s3_file := api.Group("/s3_file")
	{
		// <img width="800px" height="500px" src="http://127.0.0.1:8000/api/v1/s3_file?s3_key=spotmax-maxagent/2025/04/29/wuzhishan.jpg">
		s3_file.GET("/", controller.ShowS3File)
	}

	return engin.Run(":8000")
}
