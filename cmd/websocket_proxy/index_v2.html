<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Python 输出监控</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        #output {
            width: 100%;
            height: 500px;
            border: 1px solid #ccc;
            padding: 10px;
            margin-bottom: 10px;
            overflow-y: auto;
            background-color: #1e1e1e;
            color: #dcdcdc;
            font-family: 'Courier New', monospace;
            white-space: pre-wrap;
        }
        .print-msg {
            color: #9cdcfe;
        }
        .log-msg {
            color: #ce9178;
        }
        .sys-msg {
            color: #6a9955;
            font-style: italic;
        }
        button {
            padding: 8px 16px;
            background-color: #4CAF50;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        button:hover {
            background-color: #45a049;
        }
        .connection-status {
            margin-top: 10px;
            padding: 6px;
            border-radius: 4px;
        }
        .connected {
            background-color: rgba(76, 175, 80, 0.2);
            color: #2e7d32;
        }
        .disconnected {
            background-color: rgba(244, 67, 54, 0.2);
            color: #c62828;
        }
    </style>
</head>
<body>
    <h1>Python 输出监控</h1>
    <div id="status" class="connection-status disconnected">未连接</div>
    <div id="output"></div>
    <button onclick="clearOutput()">清空输出</button>
    <button onclick="reconnect()">重新连接</button>

    <script>
        const outputDiv = document.getElementById('output');
        const statusDiv = document.getElementById('status');
        let ws;

        function connect() {
            // ws = new WebSocket('wss://maxagent-test-api.spotmaxtech.com/maxagent-runner-1907321869538549760');
            // ws = new WebSocket('ws://*************:8765/ws?runner_record_id=1907321869538549760');
            ws = new WebSocket('wss://maxagent-test-api.spotmaxtech.com/ws?runner_record_id=1907321869538549760');

            ws.onopen = function() {
                addMessage('[客户端] WebSocket 连接已建立', 'sys-msg');
                statusDiv.textContent = '已连接';
                statusDiv.className = 'connection-status connected';
            };

            ws.onmessage = function(event) {
                const message = event.data;
                if (message.startsWith('[PRINT]')) {
                    addMessage(message, 'print-msg');
                } else if (message.startsWith('[LOG]')) {
                    addMessage(message, 'log-msg');
                } else if (message.startsWith('[SYS]')) {
                    addMessage(message, 'sys-msg');
                } else {
                    addMessage(message, '');
                }
            };

            ws.onclose = function() {
                addMessage('[客户端] WebSocket 连接已关闭，5秒后尝试重新连接...', 'sys-msg');
                statusDiv.textContent = '已断开连接，尝试重连中...';
                statusDiv.className = 'connection-status disconnected';
                setTimeout(connect, 500000);
            };

            ws.onerror = function(error) {
                addMessage(`[客户端] WebSocket 错误: ${error.message}`, 'sys-msg');
                statusDiv.textContent = '连接错误';
                statusDiv.className = 'connection-status disconnected';
            };
        }

        function addMessage(message, className) {
            const messageElement = document.createElement('div');
            messageElement.textContent = message;
            messageElement.className = className;
            
            outputDiv.appendChild(messageElement);
            
            // 自动滚动到底部
            outputDiv.scrollTop = outputDiv.scrollHeight;
        }

        function clearOutput() {
            outputDiv.innerHTML = '';
            addMessage('[客户端] 输出已清空', 'sys-msg');
        }

        function reconnect() {
            if (ws) {
                ws.close();
            }
            addMessage('[客户端] 手动重新连接...', 'sys-msg');
            connect();
        }

        // 页面加载时连接
        window.addEventListener('load', () => {
            addMessage('[客户端] 页面已加载，正在连接服务器...', 'sys-msg');
            connect();
        });

        // 在页面关闭前关闭连接
        window.addEventListener('beforeunload', () => {
            if (ws) {
                ws.close();
            }
        });
    </script>
</body>
</html>
