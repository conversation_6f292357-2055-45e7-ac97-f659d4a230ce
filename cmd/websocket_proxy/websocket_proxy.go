package main

import (
	"fmt"
	"net/http"
	"net/url"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/gorilla/websocket"
)

var upgrader = websocket.Upgrader{
	CheckOrigin: func(r *http.Request) bool {
		return true // Allow all origins
	},
} // use default options

func main() {
	r := gin.Default()

	r.GET("/ws", handleWebSocket)

	r.Run(":8765") // Start the server on port 8080
}

func handleWebSocket(c *gin.Context) {
	runner_record_id := c.Query("runner_record_id")
	fmt.Println("Received GET parameter:", runner_record_id)

	runnerConn, err := connectRunner(runner_record_id)
	if err != nil || runnerConn == nil {
		fmt.Println("Failed to connect to runner:", err)
		c.JSON(http.StatusBadGateway, gin.H{
			"message": "Failed to connect to runner, please retry",
		})
		return
	}

	webConn, err := upgrader.Upgrade(c.<PERSON>, c.Request, nil)
	if err != nil {
		c.J<PERSON>(http.StatusInternalServerError, gin.H{
			"message": "websocket error, please retry",
		})
		return
	}
	go ping(runner_record_id, webConn, runnerConn)
	go forwardRunner2Web(runner_record_id, webConn, runnerConn)
	go forwardWeb2Runner(runner_record_id, webConn, runnerConn)
}

// 添加心跳配置
const (
	pingPeriod = 30 * time.Second
	pongWait   = 60 * time.Second
)

func ping(runner_record_id string, webConn, runnerConn *websocket.Conn) {
	defer runnerConn.Close()
	defer webConn.Close()
	defer fmt.Printf("ping goroutine exit,runner_record_id:%s\n", runner_record_id)

	ticker := time.NewTicker(pingPeriod)
	defer ticker.Stop()

	for range ticker.C {
		if err := webConn.WriteControl(websocket.PingMessage, []byte{}, time.Now().Add(10*time.Second)); err != nil {
			fmt.Printf("ping error: %v\n", err)
			return
		}
		if err := runnerConn.WriteControl(websocket.PingMessage, []byte{}, time.Now().Add(10*time.Second)); err != nil {
			fmt.Printf("ping error: %v\n", err)
			return
		}
	}
}

func forwardWeb2Runner(runner_record_id string, webConn, runnerConn *websocket.Conn) {
	defer runnerConn.Close()
	defer webConn.Close()
	defer fmt.Printf("forwardWeb2Runner exit,runner_record_id:%s\n", runner_record_id)

	for {
		_, msg, err := webConn.ReadMessage()
		if err != nil {
			fmt.Println("3:Error while reading message:", err)
			return
		}

		err = runnerConn.WriteMessage(websocket.TextMessage, msg)
		if err != nil {
			fmt.Println("4:Error while sending message:", err)
			break
		}
	}
}

func forwardRunner2Web(runner_record_id string, webConn, runnerConn *websocket.Conn) {
	defer runnerConn.Close()
	defer webConn.Close()
	defer fmt.Printf("forwardRunner2Web exit,runner_record_id:%s\n", runner_record_id)
	for {
		_, msg, err := runnerConn.ReadMessage()
		if err != nil {
			fmt.Println("1:Error while reading message:", err)
			return
		}

		err = webConn.WriteMessage(websocket.TextMessage, msg)

		if err != nil {
			fmt.Println("2:Error while sending message:", err)
			break
		}
	}
}

func connectRunner(runner_record_id string) (*websocket.Conn, error) {
	host := fmt.Sprintf("maxagent-runner-%s.maxagent-test.svc.cluster.local", runner_record_id)
	if gin.Mode() == gin.ReleaseMode {
		host = fmt.Sprintf("maxagent-runner-%s.maxagent-prod.svc.cluster.local", runner_record_id)
	}

	// host := "maxagent-test-api.spotmaxtech.com"
	// u := url.URL{Scheme: "wss", Host: host, Path: "/maxagent-runner-" + runner_record_id}
	u := url.URL{Scheme: "ws", Host: host, Path: ""}
	c, _, err := websocket.DefaultDialer.Dial(u.String(), nil)
	if err != nil {
		fmt.Println("Error while connecting to WebSocket:", err)
		return nil, err
	}
	return c, nil
}
