package main

// import (
// 	"fmt"
// 	"net/http"
// 	"net/url"

// 	"github.com/gorilla/websocket"
// )

// var upgrader = websocket.Upgrader{
// 	CheckOrigin: func(r *http.Request) bool {
// 		return true // Allow all origins
// 	},
// } // use default options

// func main() {
// 	runnerConn, err := connectRunner("runner_record_id")
// 	if err != nil || runnerConn == nil {
// 		fmt.Println("Failed to connect to runner:", err)

// 		return
// 	}

// 	defer runnerConn.Close()

// 	for {
// 		_, msg, err := runnerConn.ReadMessage()
// 		if err != nil {
// 			fmt.Println("Error while reading message:", err)
// 			return
// 		}
// 		fmt.Println(string(msg))
// 	}
// }

// func connectRunner(runner_record_id string) (*websocket.Conn, error) {
// 	// host := fmt.Sprintf("maxagent-runner-%s.maxagent-test.svc.cluster.local", runner_record_id)
// 	// if gin.Mode() == gin.ReleaseMode {
// 	// 	host = fmt.Sprintf("maxagent-runner-%s.maxagent-prod.svc.cluster.local", runner_record_id)
// 	// }

// 	// wss://maxagent-test-api.spotmaxtech.com/maxagent-runner-1907321869538549760
// 	host := "maxagent-test-api.spotmaxtech.com"

// 	u := url.URL{Scheme: "wss", Host: host, Path: "/maxagent-runner-1907321869538549760"}
// 	c, _, err := websocket.DefaultDialer.Dial(u.String(), nil)
// 	if err != nil {
// 		fmt.Println("Error while connecting to WebSocket:", err)
// 		return nil, err
// 	}
// 	return c, nil
// }
