import hashlib
import json
from typing import Any, Dict

import requests

# 常量定义
# 请求URL
# 上线环境地址
MAXAGENT_URL = "https://maxagent-api.spotmaxtech.com/api/v1/remote/trigger_sync"
# 测试环境地址
# MAXAGENT_URL = "https://maxagent-test-api.spotmaxtech.com/api/v1/remote/trigger_sync"
# 本地开发地址
# MAXAGENT_URL = "http://localhost:8000/api/v1/remote/trigger_sync"

# 用户ID和magic
USER_ID = "15757"
MAGIC = "b5c0c54775cb33d1fe6a143936faadcc"  # 根据你的代码库中的magic映射


class RemoteTriggerSyncReq:
    """请求结构体"""

    def __init__(self, remark: str, app_id: str, runner_type: str,
                 trigger_params: str, time_out_second: int):
        self.remark = remark
        self.app_id = app_id
        self.runner_type = runner_type
        self.trigger_params = trigger_params
        self.time_out_second = time_out_second

    def to_dict(self) -> Dict[str, Any]:
        return {
            "remark": self.remark,
            "app_id": self.app_id,
            "trigger_params": self.trigger_params,
            "time_out_second": self.time_out_second,
            "runner_type": self.runner_type
        }


def sort_json(data: bytes) -> bytes:
    """对JSON进行排序以确保稳定的输出"""
    try:
        # 解析JSON
        m = json.loads(data.decode('utf-8'))

        # 获取排序后的键
        keys = sorted(m.keys())

        # 创建排序后的字典
        sorted_dict = {k: m[k] for k in keys}

        # 重新序列化，确保无空格
        result = json.dumps(sorted_dict, separators=(
            ',', ':'), ensure_ascii=False)
        return result.encode('utf-8')
    except (json.JSONDecodeError, UnicodeDecodeError):
        return data


def generate_vcode(magic: str, body: bytes) -> str:
    """生成vcode"""
    # 确保JSON字符串的稳定性
    sorted_payload = sort_json(body).decode('utf-8')
    print(f"排序后的JSON: {sorted_payload}")

    # 计算base string
    base_string = magic + sorted_payload

    # 计算SHA256哈希
    return hashlib.sha256(base_string.encode('utf-8')).hexdigest()


def main():
    # 构建请求体
    req_body = RemoteTriggerSyncReq(
        remark="runner_1748245652641",
        app_id="1926915925149261824",
        runner_type="app",
        trigger_params="[{\"name\":\"seed_url\",\"value\":\"https://aiagentpublic.s3.amazonaws.com/3.png\",\"type\":\"string\"}]",
        time_out_second=30 * 60
    )

    # 序列化请求体为JSON
    try:
        json_body = json.dumps(
            req_body.to_dict(), separators=(',', ':')).encode('utf-8')
    except Exception as e:
        print(f"JSON序列化错误: {e}")
        return

    # 生成vcode
    vcode = generate_vcode(MAGIC, json_body)
    print(f"生成的vcode: {vcode}")

    # 设置请求头
    headers = {
        "Content-Type": "application/json",
        "userid": USER_ID,
        "vcode": vcode
    }

    # 发送请求
    try:
        timeout = (1800, 1800)  # (连接超时, 读取超时)
        response = requests.post(
            MAXAGENT_URL, data=json_body, headers=headers, timeout=timeout)

        # 打印响应状态
        print(f"响应状态: {response.status_code} {response.reason}")

        # 打印响应体内容
        print(f"响应体: {response.text}")

    except requests.exceptions.RequestException as e:
        print(f"发送请求错误: {e}")


if __name__ == "__main__":
    main()
