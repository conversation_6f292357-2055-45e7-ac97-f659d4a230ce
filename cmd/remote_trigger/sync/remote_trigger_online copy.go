package main

import (
	"bytes"
	"crypto/sha256"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"sort"
	"time"
)

const (
	// 请求URL
	// 上线环境地址
	MAXAGENT_URL = "https://maxagent-api.spotmaxtech.com/api/v1/remote/trigger_sync"
	// 测试环境地址
	//  MAXAGENT_URL = "https://maxagent-test-api.spotmaxtech.com/api/v1/remote/trigger_sync"
	// 本地开发地址
	// MAXAGENT_URL = "http://localhost:8000/api/v1/remote/trigger_sync"
	// 用户ID和magic
	USER_ID = "14592"
	MAGIC   = "aee8685ec44be69970036037e6b9c1f5" // 根据你的代码库中的magic映射
)

// RemoteTriggerReq 请求结构体
type RemoteTriggerSyncReq struct {
	Remark        string `json:"remark"`
	AppId         string `json:"app_id"`
	TriggerParams string `json:"trigger_params"`
	TimeOutSecond int    `json:"time_out_second"`
}

// sortJSON 对JSON进行排序以确保稳定的输出
func sortJSON(data []byte) []byte {
	var m map[string]interface{}
	if err := json.Unmarshal(data, &m); err != nil {
		return data
	}

	keys := make([]string, 0, len(m))
	for k := range m {
		keys = append(keys, k)
	}
	sort.Strings(keys)

	sorted := make(map[string]interface{})
	for _, k := range keys {
		sorted[k] = m[k]
	}

	result, _ := json.Marshal(sorted)
	return result
}

// 生成vcode
func generateVcode(magic string, body []byte) string {
	// 确保JSON字符串的稳定性
	sortedPayload := string(sortJSON(body))
	fmt.Println("排序后的JSON:", sortedPayload)

	// 计算base string
	baseString := magic + sortedPayload

	// 计算SHA256哈希
	h := sha256.New()
	h.Write([]byte(baseString))
	return hex.EncodeToString(h.Sum(nil))
}

func main() {
	// 构建请求体
	// reqBody := RemoteTriggerSyncReq{
	// 	Remark:        "runner_1747295958465",
	// 	AppId:         "1922573657713418240",
	// 	TriggerParams: `[{"name":"target_language","value":"中文","type":"string"},{"name":"question","value":"第一个登录月球的人是谁","type":"string"}]`,
	// 	TimeOutSecond: 30 * 60,
	// }
	reqBody := RemoteTriggerSyncReq{
		//Remark: "runner_1747295958465",
		Remark: "runner_1749554133993",
		//AppId:  "1922573657713418240",
		//AppId: "1930188370343936000",
		AppId: "1932648918415990784",
		//TriggerParams: `[{"name":"target_language","value":"中文","type":"string"},{"name":"question","value":"第一个登录月球的人是谁","type":"string"}]`,
		//TriggerParams: `[{\"name\": \"package_name\", \"type\": \"string\", \"value\": \"com.seenax.HideAndSeek\"}, {\"name\": \"app_name\", \"type\": \"string\", \"value\": \"Hide 'N Seek!\"}, {\"name\": \"mtg_category_l1\", \"type\": \"string\", \"value\": \"Game\"}]`,
		TriggerParams: `[{"name":"demand_package_name","value":"com.seenax.HideAndSeek","type":"string"},{"name":"app_name","value":"Hide 'N Seek!","type":"string"}]`,

		TimeOutSecond: 30 * 60,
	}

	// 序列化请求体为JSON
	jsonBody, err := json.Marshal(reqBody)
	if err != nil {
		fmt.Println("JSON序列化错误:", err)
		return
	}

	// 生成vcode
	vcode := generateVcode(MAGIC, jsonBody)
	fmt.Println("生成的vcode:", vcode)

	// 创建请求
	req, err := http.NewRequest("POST", MAXAGENT_URL, bytes.NewBuffer(jsonBody))
	if err != nil {
		fmt.Println("创建请求错误:", err)
		return
	}

	// 设置请求头
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("userid", USER_ID)
	req.Header.Set("vcode", vcode)

	start := time.Now()

	// 创建带有合理超时的HTTP客户端
	// 分别设置连接超时和总体超时
	client := &http.Client{
		Timeout: 35 * 60 * time.Second, // 总体超时稍微长于服务器超时
		Transport: &http.Transport{
			DisableKeepAlives:   true, // 禁用连接复用，避免连接问题
			MaxIdleConns:        1,    // 限制空闲连接
			IdleConnTimeout:     30 * time.Second,
			TLSHandshakeTimeout: 10 * time.Second,
		},
	}

	fmt.Println("开始发送请求...")
	resp, err := client.Do(req)
	elapsed := time.Since(start)
	fmt.Println("请求耗时:", elapsed)

	if err != nil {
		fmt.Println("发送请求错误:", err)
		// 检查是否是超时错误
		if elapsed > 30*60*time.Second {
			fmt.Println("请求可能因为超时而失败，建议检查服务器处理时间")
		}
		return
	}
	defer resp.Body.Close()

	// 打印响应状态
	fmt.Println("响应状态码:", resp.StatusCode)
	fmt.Println("响应状态:", resp.Status)

	// 检查响应状态码
	if resp.StatusCode != http.StatusOK {
		fmt.Printf("服务器返回非200状态码: %d\n", resp.StatusCode)
	}

	// 读取响应体内容
	bodyBytes, err := io.ReadAll(resp.Body)
	if err != nil {
		fmt.Println("读取响应体错误:", err)
		return
	}

	fmt.Println("响应体长度:", len(bodyBytes))
	if len(bodyBytes) > 0 {
		fmt.Println("响应体:", string(bodyBytes))
	} else {
		fmt.Println("响应体为空")
	}
}
