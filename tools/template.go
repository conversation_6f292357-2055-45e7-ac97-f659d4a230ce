package tools

import (
	"bytes"
	"context"

	// log "github.com/sirupsen/logrus"
	"text/template"
)

func ReplaceTemplate(ctx context.Context, templateContext string, data interface{}) (string, error) {
	_, log := GenContext(ctx)

	parse, err := template.New("ReplaceTemplate").Parse(templateContext)
	if err != nil {
		log.Error(err)
		return "", err
	}

	buffer := &bytes.Buffer{}
	err = parse.Execute(buffer, data)
	if err != nil {
		log.Error(err)
		return "", err
	}
	return buffer.String(), nil
}
