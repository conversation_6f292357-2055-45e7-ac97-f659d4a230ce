package tools

import (
	"crypto/md5"
	"fmt"
	"sort"
	"strconv"
	"strings"
)

func Exist(target string, str_array []string) bool {
	sort.Strings(str_array)
	index := sort.SearchStrings(str_array, target)
	//index的取值：[0,len(str_array)]
	if index < len(str_array) && str_array[index] == target { //需要注意此处的判断，先判断 &&左侧的条件，如果不满足则结束此处判断，不会再进行右侧的判断
		return true
	}
	return false
}

func StringSliceToSqlIn(values []string) string {
	if len(values) == 0 {
		return "''"
	}
	return "'" + strings.Join(values, "','") + "'"
}

func String2Int64(str string) int64 {
	value, err := strconv.ParseInt(str, 10, 64)
	if err != nil {
		fmt.Println("Error converting string to int64:", err)
		return int64(0)
	}
	return value
}

func StringSliceToSqlInWithInt(values []string) string {
	if len(values) == 0 {
		return "''"
	}
	return strings.Join(values, ",")
}

func TranslateMarkdown(markdownText string) string {
	return strings.Replace(markdownText, "&amp;", "&", -1)
}

func Int64SliceToSqlIn(data []int64, sep string) string {
	if len(data) == 0 {
		return ""
	}
	str := fmt.Sprintf("%d", data[0])
	for i := 1; i < len(data); i++ {
		str = str + sep + fmt.Sprintf("%d", data[i])
	}
	return str
}

func Md5(str string) string {
	return fmt.Sprintf("%x", md5.Sum([]byte(str)))
}
