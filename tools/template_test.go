package tools

import (
	"context"
	"strings"
	"testing"

	. "github.com/smartystreets/goconvey/convey"
)

func TestReplaceTemplate(t *testing.T) {
	Convey("模板替换", t, func() {
		Convey("MAP替换", func() {
			template := "{{.Name}}"
			data := map[string]interface{}{
				"Name": "test",
			}
			result, err := ReplaceTemplate(context.TODO(), template, data)
			So(err, ShouldBeNil)
			So(result, ShouldEqual, "test")
		})
		Convey("结构体替换", func() {
			template := "{{.Name}}"
			data := struct {
				Name string
			}{
				Name: "test",
			}
			result, err := ReplaceTemplate(context.TODO(), template, data)
			So(err, ShouldBeNil)
			So(result, ShouldEqual, "test")
		})
	})
}

func TestFullTemp(t *testing.T) {
	temp := `apiVersion: engineplus.mobvista.com/v1
kind: Engineplus
metadata:
  name: engineplus-installbymaxcloud
  namespace: engineplus
spec:
  #  PREFIX是aliyun这边我们已经封装好的镜像，目前直接调用这个前缀就可以，也可以通过docker images查到每一个镜像的地址
  repoPrefix: {{.repoPrefix}}
  #  镜像的tag，目前组件都使用这个tag即可
  repoTag: {{.repoTag}}
  #  AIRFLOW_REPO_TAG AIRFLOW 组件设置自己的tag

  #  自行判断是否使用ingress
  ingressEnabled: "{{.ingressEnabled}}"
  #  自行定义一个符合需求的ingress地址，然后给运维去审批，就可以使用了
  ingressHost: {{.ingressHost}}
  # 这个需要运维同学提供一个oss的bucket，这里不是写错的，不要写成oss://xxx，不然会报错，即使给的是oss的地址也用s3的前缀即可
  s3Prefix: {{.s3Prefix}}
  #  这个自定义一个字符串，作为sa
  sparkServiceaccount: {{.sparkServiceaccount}}
  #  这个比较重要，一定要写成aliyun 或者 ALIYUN不然会出现很大的问题
  cloud: {{.cloud}}
  #  这里把nodegroup的name放上就可以
  serverNode: {{.serverNode}}
  #  这里把nodegroup的name放上就可以
  taskNode: {{.taskNode}}
  #  这里我们配置aliyun的，aws的不用管，放在这就可以
  roleArn: {{.roleArn}}
  #  aliyun 这里的三个参数都需要运维提供，切记！！！一定要确定基于的是正确的参数，不然永远无法排查出错误 (todo 这个是内网地址)
  endpoint: {{.endpoint}}
  region: {{ .region }}
  accessKeyId: {{.accessKeyId}}
  secretAccessKey: {{.secretAccessKey}}

  password: {{.password}}



  ### delete
  # docker 拉取镜像用户邮箱
  dockerRegistryEmail: {{.dockerRegistryEmail}}
  # docker 拉取镜像用户名
  dockerRegistryName: {{.dockerRegistryName}}
  # docker 拉取镜像密码
  dockerRegistryPassword: {{.dockerRegistryPassword}}

  # 指定Airflow版本如果为空或者0.0.0则不安装
  airflowVersion: {{.airflowVersion}}
  airflowDbRdsMysqlHost: {{.airflowDbRdsMysqlHost}}
  airflowDbRdsMysqlPort: "{{.airflowDbRdsMysqlPort}}"
  airflowDbRdsMysqlUser: {{.airflowDbRdsMysqlUser}}
  airflowDbRdsMysqlPassword: {{.airflowDbRdsMysqlPassword}}
  airflowDbRdsMysqlDatebase: {{.airflowDbRdsMysqlDatebase}}
  airflowTimezone: Asia/Shanghai
  airflowRepoTag: {{.airflowRepoTag}}
  airflowServerNode: {{.airflowServerNode}}
  airflowTaskNode: {{.airflowTaskNode}}

  flinkStreamingWebVersion: {{.flinkStreamingWebVersion}}
  flinkWebDbRdsMysqlHost: {{.flinkWebDbRdsMysqlHost}}
  flinkWebDbRdsMysqlPort: {{.flinkWebDbRdsMysqlPort}}
  flinkWebDbRdsMysqlUser: {{.flinkWebDbRdsMysqlUser}}
  flinkWebDbRdsMysqlPassword: {{.flinkWebDbRdsMysqlPassword}}
  flinkWebDbRdsMysqlDatebase: {{.flinkWebDbRdsMysqlDatebase}}
  flinkWebTimezone: {{.flinkWebTimezone}}
  flinkWebRepoTag: {{.flinkWebRepoTag}}
  flinkWebServerNode: {{.flinkWebServerNode}}
  flinkWebTaskNode: {{.flinkWebTaskNode}}

  # 指定Flink版本如果为空或者0.0.0则不安装
  flinkVersion: {{.flinkVersion}}
  flinkRepoTag: {{.flinkRepoTag}}
  flinkServerNode: {{.flinkServerNode}}
  flinkTaskNode: {{.flinkTaskNode}}

  # 指定Zeppelin版本如果为空或者0.0.0则不安装
  zeppelinVerison: {{.zeppelinVerison}}
  zeeplinRepoTag: {{.zeeplinRepoTag}}
  zeeplinServerNode: {{.zeeplinServerNode}}
  zeeplinTaskNode: {{.zeeplinTaskNode}}


  # 指定SparkHistoryServer版本如果为空或者0.0.0则不安装
  sparkHistoryServerVersion: {{.sparkHistoryServerVersion}}
  sparkHistoryServerRepoTag: {{.sparkHistoryServerRepoTag}}
  sparkHistoryServerServerNode: {{.sparkHistoryServerServerNode}}
  sparkHistoryServerTaskNode: {{.sparkHistoryServerTaskNode}}

  # 指定Jupyter版本如果为空或者0.0.0则不安装
  jupyterVersion: {{.jupyterVersion}}
  jupyterRepoTag: {{.jupyterRepoTag}}
  jupyterServerNode: {{.jupyterServerNode}}
  jupyterTaskNode: {{.jupyterTaskNode}}


  sparkUiProxyVersion: {{.sparkUiProxyVersion}}
  sparkUiProxyRepoTag: {{.sparkUiProxyRepoTag}}
  sparkUiProxyServerNode: {{.sparkUiProxyServerNode}}
  sparkUiProxyTaskNode: {{.sparkUiProxyTaskNode}}`

	data := map[string]string{
		"sparkUiProxyTaskNode": "sparkUiProxyTaskNode",
	}
	template, err := ReplaceTemplate(context.TODO(), temp, data)
	if err != nil {
		t.Error(err)
		return
	}
	template = strings.Replace(template, "<no value>", "\"\"", -1)
	t.Log(template)
}

func TestGenAwsKubeConf(t *testing.T) {
	tmp := `
apiVersion: v1
clusters:
- cluster:
    certificate-authority-data: {{ .Certificate }}
    server: {{ .Server }}
  name: {{ .Name }}
contexts:
- context:
    cluster: {{ .Name }}
    user: {{ .Name }}
  name: {{ .Name }}
current-context: {{ .Name }}
kind: Config
preferences: {}
users:
- name: {{ .Name }}
  user:
    token: {{ .Token }}
`
	data := map[string]string{
		"Certificate": "xxx",
		"Server":      "https://40C932C617E1DA241DE569002A9BB842.gr7.us-east-2.eks.amazonaws.com",
		"Name":        "arn:aws:eks:us-east-2:818539432014:cluster/eksDemo",
		"Token":       "yyy",
	}
	result, err := ReplaceTemplate(context.TODO(), tmp, data)
	if err != nil {
		t.Log(err)
	}
	t.Log(result)
}
