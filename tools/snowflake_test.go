package tools

import (
	"fmt"
	"hash/fnv"
	"jarvis_api/constant"
	"os"
	"testing"

	"github.com/bwmarrin/snowflake"
	"github.com/google/uuid"
)

func Test_aab(t *testing.T) {
	fmt.Println(uuid.NewString())
	pod_id := os.Getenv("POD_ID")
	h := fnv.New64a()
	h.Write([]byte(pod_id))

	fmt.Printf("%v\n", h.Sum64()%1024)
	fmt.Printf("%v\n", 0%1024)

}
func Test_aa(t *testing.T) {

	// Create a new Node with a Node number of 1
	node, err := snowflake.NewNode(0)
	if err != nil {
		fmt.Println(err)
		return
	}

	// Generate a snowflake ID.
	_ = node.Generate()
	id := node.Generate()

	// Print out the ID in a few different ways.
	fmt.Printf("Int64  ID: %d\n", id)
	fmt.Printf("String ID: %s\n", id)
	fmt.Printf("Base2  ID: %s\n", id.Base2())
	fmt.Printf("Base64 ID: %s\n", id.Base64())

	// Print out the ID's timestamp
	fmt.Printf("ID Time  : %d\n", id.Time())

	// Print out the ID's node number
	fmt.Printf("ID Node  : %d\n", id.Node())

	// Print out the ID's sequence number
	fmt.Printf("ID Step  : %d\n", id.Step())

	// Generate and print, all in one.
	fmt.Printf("ID       : %d\n", node.Generate().Int64())
}

func TestI(t *testing.T) {
	fmt.Println(constant.MiddlewareKeyRequestId)
}
