import unittest
from optimizers.last_task_optimizer import parse_json_to_dataset



# ==== 测试代码部分 ====

class TestParseJsonToDataset(unittest.TestCase):

    def test_valid_input(self):
        json_input = '''
        {
           "samples": [
               {
                   
                    "task_parameters": {
                        "topic": "machine learning"
                    },
                    "agent_anwser": "AI answer text",
                    "user_feedback": "Helpful"
               
               }
           ]
        }
        '''
        dataset = parse_json_to_dataset(json_input)
        self.assertEqual(len(dataset.samples), 1)
        sample = dataset.samples[0]
        self.assertEqual(sample.task_parameters, {"topic": "machine learning"})
        self.assertEqual(sample.agent_anwser, "AI answer text")
        self.assertEqual(sample.user_feedback, "Helpful")


    def test_empty_samples(self):
        json_input = '''
        {
           "samples": []
        }
        '''
        dataset = parse_json_to_dataset(json_input)
        self.assertEqual(len(dataset.samples), 0)

    def test_invalid_json(self):
        json_input = '{ invalid json }'
        with self.assertRaises(Exception):  # Could be JSONDecodeError
            obj = parse_json_to_dataset(json_input)
            print(obj)


if __name__ == '__main__':
    unittest.main()
