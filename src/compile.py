import os
import shutil
import subprocess
from pathlib import Path
#pyarmor gen -O dist -r src/
def compile_and_collect_pyc(source_dir, dist_dir):
    """
    自动化编译项目为 .pyc，整理到独立目录，并清理残留文件
    :param source_dir: 源代码目录
    :param dist_dir: 目标目录（仅含 .pyc）
    """
    source_dir = Path(source_dir).resolve()
    dist_dir = Path(dist_dir).resolve()

    # 步骤1：编译项目（使用 -b 参数直接在源码目录生成 .pyc）
    print(f"编译项目: {source_dir}")
    result = subprocess.run(
        ["python", "-m", "compileall", "-b", "-s", str(source_dir), str(source_dir)],
        capture_output=True,
        text=True
    )
    if result.returncode != 0:
        print("编译失败！错误信息:")
        print(result.stderr)
        return False

    # 步骤2：复制 .pyc 到目标目录（保持结构）
    print(f"整理 .pyc 文件到: {dist_dir}")
    for root, dirs, files in os.walk(source_dir):
        for file in files:
            if file.endswith(".pyc"):
                src_pyc = Path(root) / file
                # 计算目标路径
                relative_path = Path(root).relative_to(source_dir)
                dest_dir = dist_dir / relative_path
                dest_dir.mkdir(parents=True, exist_ok=True)
                # 移动文件（非复制）
                shutil.move(str(src_pyc), str(dest_dir / file))
                print(f"移动: {src_pyc} → {dest_dir / file}")

    # 步骤3：清理源目录中的空 __pycache__ 文件夹（-b 参数不会生成，但保险起见）
    print("清理残留目录...")
    for root, dirs, files in os.walk(source_dir, topdown=False):
        for dir_name in dirs:
            if dir_name == "__pycache__":
                cache_dir = Path(root) / dir_name
                try:
                    cache_dir.rmdir()  # 删除空目录
                except OSError:
                    pass  # 忽略非空目录（理论上不存在）

    print("操作完成！")
    return True

if __name__ == "__main__":
    source_project = "."  # 替换为实际路径
    dist_project = "../dist"  # 替换为实际路径

    success = compile_and_collect_pyc(source_project, dist_project)
    if success:
        print(f"纯净的 .pyc 目录: {dist_project}")
    else:
        print("处理失败。")