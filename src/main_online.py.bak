import asyncio
import builtins
import contextvars
import datetime
import json
import os
import uuid
from typing import Any, Optional

from crewai import TaskOutput
from crewai.agents.parser import AgentAction, AgentFinish
from crewai.tools.tool_types import ToolResult
from dotenv import load_dotenv
from flask import Flask, Response, jsonify, make_response, request, stream_with_context
from pymysql.cursors import Cursor

from common.mysql_helper import get_sql_conn
from common.redirect_root import agent_config_home_path, redirect_root
from runner.runner import Runner
from service.agent_service import AgentService
from service.agent_team_service import AgentTeamService
from service.app_service import AppService
from service.connector_service import ConnectorService
from service.knowledge_service import KnowledgeService
from service.llm_service import LLMService
from service.runner_record_service import RunnerRecordService
from service.task_service import TaskService
from service.tool_service import ToolService

# 加载 .env 文件
dotenv_path = os.getenv('DOT_PATH', ".env")
if not load_dotenv(dotenv_path, override=True):
    raise ValueError(f"无法加载环境变量文件: {dotenv_path}")

# 创建上下文变量
request_id_var = contextvars.ContextVar('request_id', default=None)


# # 重定向 makedirs 方法
# def redirected_makedirs(path, *args, **kwargs):
#     if isinstance(path, str) and not os.path.isabs(path) and not path.startswith("/tmp/"):
#         # 从上下文变量获取 request_id
#         request_id = request_id_var.get()
#         if not request_id:
#             raise RuntimeError("No request_id set for redirected makedirs()")
#         redirected_path = redirect_root / request_id / path
#         return _original_makedirs(str(redirected_path), *args, **kwargs)
#     return _original_makedirs(path, *args, **kwargs)


# # 保存原始 makedirs
# _original_makedirs = os.makedirs

# # 注入 makedirs 的 monkey patch
# os.makedirs = redirected_makedirs


def redirected_open(file, *args, **kwargs):
    # 如果是相对路径且不是目录访问
    if isinstance(file, str) and not os.path.isabs(file) and not file.startswith(str(agent_config_home_path)) and not file.startswith("knowledge"):
        # 从上下文变量获取 request_id
        request_id = request_id_var.get()
        if not request_id:
            raise RuntimeError("No request_id set for redirected open()")
        redirected_path = redirect_root / request_id / file
        redirected_path.parent.mkdir(parents=True, exist_ok=True)
        file = str(redirected_path)
    return _original_open(file, *args, **kwargs)


# 保留原始 open
_original_open = builtins.open

# 注入 monkey patch
builtins.open = redirected_open


app = Flask(__name__)

runner_g = None


def init_runner():
    global runner_g
    print("Starting app...")

    if runner_g is not None:
        return runner_g

    # 初始化数据库连接
    cursor: Cursor
    cursor = get_sql_conn()

    # 初始化App服务
    app_service = AppService(cursor)
    # 初始化AgentTeam服务
    agent_team_service = AgentTeamService(cursor)
    # 初始化LLM服务
    llm_service = LLMService(cursor)
    # 初始化Tool服务
    tool_service = ToolService(cursor)
    knowledge_service = KnowledgeService(cursor)
    # 初始化Agent服务
    agent_service = AgentService(
        cursor, llm_service, tool_service, knowledge_service)
    # 初始化Task服务
    task_service = TaskService(cursor, tool_service, knowledge_service)
    # 初始化Runner记录服务
    runner_record_service = RunnerRecordService()
    # 初始化Connector服务
    connector_service = ConnectorService(cursor)
    # 初始化Runner
    runner = Runner(app_service=app_service,
                    agent_team_service=agent_team_service,
                    llm_service=llm_service,
                    agent_service=agent_service,
                    task_service=task_service,
                    tool_service=tool_service,
                    runner_record_service=runner_record_service,
                    connector_service=connector_service)
    runner_g = runner
    return runner


def compose_response(http_code=200, error_msg='success', data=None):
    response = make_response(jsonify({
        "message": error_msg,
        "data": data
    }))
    response.status_code = http_code
    return response


@app.route("/runner/exec", methods=["POST"])
async def runner_exec():
    # 获取request 的json中runner_record_id参数
    data = request.get_json()
    if not data or 'runner_record_id' not in data:
        return compose_response(400, "Missing runner_record_id in JSON body")
    if not isinstance(data['runner_record_id'], int):
        return compose_response(400, "Invalid runner_record_id in JSON body")

    # 每个请求一个独立目录
    request_id = str(uuid.uuid4())
    # 设置上下文变量
    token = request_id_var.set(request_id)

    try:
        runner_record_id = data['runner_record_id']
        runner = init_runner()
        result = runner.run(runner_record_id=int(
            runner_record_id), request_id=request_id)
        print(result)
        return compose_response(data=result)
    finally:
        # 清理上下文
        request_id_var.reset(token)


@app.route("/runner/exec_event", methods=["POST"])
async def runner_exec_event():
    data = request.get_json()
    if not data or 'runner_record_id' not in data:
        return compose_response(400, "Missing runner_record_id in JSON body")
    if not isinstance(data['runner_record_id'], int):
        return compose_response(400, "Invalid runner_record_id in JSON body")

    request_id = str(uuid.uuid4())
    token = request_id_var.set(request_id)

    try:
        runner_record_id = data['runner_record_id']
        runner = init_runner()

        # 创建一个队列来存储事件数据
        event_queue = asyncio.Queue()

        # 定义回调函数将数据放入队列
        def task_callback(task_output: TaskOutput):
            task_data = {"type": "thought",
                         "subtype": "task", "data": task_output.raw}
            event_queue.put_nowait(json.dumps(task_data, ensure_ascii=False))

        def step_callback(step_output: Any):
            if isinstance(step_output, AgentFinish):
                data = {"type": "thought", "subtype": "finish",
                        "data": step_output.text}
            elif isinstance(step_output, AgentAction):
                data = {"type": "thought", "subtype": "action",
                        "data": step_output.text}
            elif isinstance(step_output, ToolResult):
                data = {"type": "thought", "subtype": "tool",
                        "data": step_output.result}
            else:
                data = {"type": "thought", "subtype": "other",
                        "data": str(step_output)}
            event_queue.put_nowait(json.dumps(data, ensure_ascii=False))

        # 创建一个同步生成器函数
        def sync_generator():
            # 发送初始消息
            yield json.dumps({'type': 'start', 'date': '任务开始执行'}, ensure_ascii=False)

            # 在后台线程中运行runner
            import threading
            import time

            def run_task():
                result = runner.run(
                    int(runner_record_id),
                    request_id,
                    task_callback,
                    step_callback
                )
                # 任务完成后，添加结束消息到队列
                event_queue.put_nowait(json.dumps(
                    {'type': 'result', 'data': result}, ensure_ascii=False))

            # 启动后台线程
            thread = threading.Thread(target=run_task)
            thread.daemon = True
            thread.start()

            # 使用轮询方式获取队列中的消息
            while thread.is_alive() or not event_queue.empty():
                # 检查是否有消息
                try:
                    # 非阻塞方式检查队列
                    if not event_queue.empty():
                        message = event_queue.get_nowait()
                        yield message
                except:
                    pass

                # 短暂休眠，避免CPU占用过高
                time.sleep(0.1)

        return Response(sync_generator(), mimetype='text/event-stream')

    finally:
        # 清理上下文
        request_id_var.reset(token)


# def get_current_time():
#     return datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")

# # test event stream
# @app.route("/test_event_stream", methods=["GET"])
# async def test_event_stream():
#     def combined_event_stream():
#         # 第一步：立即返回当前时间
#         result = {"msg": "当前时间", "time": get_current_time()}
#         yield f"data: {json.dumps(result, ensure_ascii=False)}\n\n"

#         # 第二步：返回多个事件
#         for i in range(5):  # 假设调用5次
#             # 这里不使用 asyncio.sleep，因为这是同步生成器
#             result = {
#                 "index": i + 1,
#                 "msg": f"第{i+1}次调用结果",
#                 "time": get_current_time()
#             }
#             yield f"data: {json.dumps(result, ensure_ascii=False)}\n\n"

#     return Response(combined_event_stream(), mimetype='text/event-stream')


if __name__ == "__main__":
    import hypercorn.asyncio
    from hypercorn.config import Config

    config = Config()
    config.bind = ["0.0.0.0:8080"]
    asyncio.run(hypercorn.asyncio.serve(app, config))
