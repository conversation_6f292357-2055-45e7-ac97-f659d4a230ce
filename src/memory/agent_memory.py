import os
from mem0 import Memory
import chromadb
from chromadb.utils import embedding_functions
chroma_client = chromadb.PersistentClient(path="./chroma_db")

config = {
    "llm": {
        "provider": "litellm",
        "config": {
            "model": "vertex_ai/gemini-2.0-flash-exp",
            "temperature": 0.2,
            "max_tokens": 2000,
        }

    },
    "embedder": {
        "provider": "azure_openai",
        "config": {
            "model": "text-embedding-3-large",
            "azure_kwargs": {
                  "api_version": "2024-12-01-preview",
                  "azure_deployment": "text-embedding-3-small",
                  "azure_endpoint": os.environ["AZURE_OPENAI_ENDPOINT"],
                  "api_key": os.environ["AZURE_OPENAI_API_KEY"],
               
              }
        
        }
    }
}

m = Memory.from_config(config)
messages = [
    {"role": "user", "content": "Thinking of making a sandwich. What do you recommend?"},
    {"role": "assistant", "content": "How about adding some cheese for extra flavor?"},
    {"role": "user", "content": "Actually, I don't like cheese."},
    {"role": "assistant", "content": "I'll remember that you don't like cheese for future recommendations."}
]

#m.add(messages, user_id="alice", metadata={"category": "food"})
print(m.get_all(user_id="alice"))