from langgraph.prebuilt import create_react_agent
from langgraph.store.postgres import AsyncPostgresStore
from langmem import create_manage_memory_tool, create_search_memory_tool
from langgraph.store.postgres import PostgresStore
import uuid
import os
# CREATE TABLE IF NOT EXISTS store (
#     prefix TEXT NOT NULL,
#     key TEXT NOT NULL,
#     value JSONB NOT NULL,
#     created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
#     updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
#     expires_at TIMESTAMP WITH TIME ZONE,  -- Expiration timestamp
#     ttl_minutes INTEGER,                  -- TTL duration in minutes
#     PRIMARY KEY (prefix, key)
# );

def setup_store():
    store = PostgresStore.from_conn_string(
        os.environ['MEMORY_STORE_URL'],
    )
    # Initializes necessary tables and schemas
    # store.create_tables()
    return store

global_memory_store = setup_store()


user_id = "1"
namespace_for_memory = ("agent","memory")

# Save a memory to namespace as key and value
key = str(uuid.uuid4())

# The value needs to be a dictionary  
value = {"food_preference" : "I like pizza"}

# Save the memory
# in_memory_store.put(namespace_for_memory, key, value)
with setup_store() as memory_store:
    memories=memory_store.search(namespace_for_memory)
    print(memories[0].key, memories[0].value)

memory_management=create_manage_memory_tool(namespace=("memories", "agent"), store=setup_store())

    # Read from shared team namespace
memory_search=create_search_memory_tool(namespace=("memories", "agent"), store=setup_store())