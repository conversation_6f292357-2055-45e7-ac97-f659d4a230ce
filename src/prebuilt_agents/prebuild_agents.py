from crewai import Agent
from tools.human_feedback import collect_user_feedback
from tools.memory import Me<PERSON><PERSON><PERSON>
from tools.human_feedback_long_duration import collect_user_feedback_asyn
from engine.prebuilt_tools import prebuilt_tools

def user_feedback_agent(user_id, llm, memory_space) -> Agent:
    tools=[collect_user_feedback]
    print("memory:",memory_space)
    if memory_space != "" :
        memory_tool = prebuilt_tools["long_memory"]
        memory_tool.space = memory_space
        memory_tool.llm = llm
        memory_tool.user_id = user_id
        tools.append(memory_tool)
    print("xtools:",tools,len(tools))
    
    return Agent(
        role='User Feedback Collector',
        goal="""
         Collect user feedback on the archieved results. 
         Then, identify the user's preferences about the task in the feedback, 
         and store them into the long-term memory (memory category: user_preferences).""",     
        backstory=(
            "You are a User Feedback Collector with a passion for understanding user needs and remembering the preferences. "
            
        ),
        verbose=True,
        allow_delegation=False,     
        tools=tools,
        llm=llm
    )

def user_feedback_agent_asyn(user_id, llm,memory_space) -> Agent:
    tools=[collect_user_feedback_asyn]
    print("memory:",memory_space)
    if memory_space != "" :
        memory_tool = MemTool()
        memory_tool.space = memory_space
        memory_tool.llm = llm
        memory_tool.user_id = user_id
        tools.append(memory_tool)
    agent = user_feedback_agent(llm,memory_space)
    agent.tools=tools
    return agent
    
prebuilt_agents={
    "user_feedback_collector": user_feedback_agent
    ,
    "user_feedback_collector_asyn": user_feedback_agent_asyn}