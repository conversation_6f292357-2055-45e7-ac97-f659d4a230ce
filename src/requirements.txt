aiohappyeyeballs==2.6.1
aiohttp==3.11.13
aiosignal==1.3.2
altgraph==0.17.4
annotated-types==0.7.0
anthropic==0.49.0
anyio==4.9.0
appdirs==1.4.4
asgiref==3.8.1
asttokens==3.0.0
attrs==25.3.0
auth0-python==4.8.1
azure-common==1.1.28
azure-core==1.32.0
azure-search-documents==11.5.2
backoff==2.2.1
bcrypt==4.3.0
beautifulsoup4==4.13.4
blinker==1.9.0
build==1.2.2.post1
cachetools==5.5.2
certifi==2025.1.31
cffi==1.17.1
charset-normalizer==3.4.1
chroma-hnswlib==0.7.6
chromadb==0.6.3
click==8.1.8
coloredlogs==15.0.1
crewai==0.114.0
crewai-tools==0.40.1
cryptography==44.0.2
Cython==3.0.12
dataclasses-json==0.6.7
decorator==5.2.1
Deprecated==1.2.18
dill==0.4.0
distro==1.9.0
docling==2.31.0
docling-core==2.28.1
docling-ibm-models==3.4.2
docling-parse==4.0.1
docstring_parser==0.16
docx2txt==0.9
durationpy==0.9
dydantic==0.0.8
easyocr==1.7.2
et_xmlfile==2.0.0
executing==2.2.0
fastapi==0.115.11
filelock==3.18.0
filetype==1.2.0
flatbuffers==25.2.10
frozenlist==1.5.0
fsspec==2025.3.0
google-ai-generativelanguage==0.6.15
google-api-core==2.24.2
google-api-python-client==2.164.0
google-auth==2.38.0
google-auth-httplib2==0.2.0
google-cloud==0.34.0
google-cloud-aiplatform==1.84.0
google-cloud-bigquery==3.30.0
google-cloud-core==2.4.3
google-cloud-resource-manager==1.14.1
google-cloud-storage==2.19.0
google-crc32c==1.6.0
google-generativeai==0.8.4
google-resumable-media==2.7.2
googleapis-common-protos==1.69.1
grpc-google-iam-v1==0.14.1
grpcio==1.71.0
grpcio-status==1.71.0
grpcio-tools==1.71.0
h11==0.14.0
h2==4.2.0
hpack==4.1.0
html2text==2025.4.15
httpcore==1.0.7
httplib2==0.22.0
httptools==0.6.4
httpx==0.27.2
httpx-sse==0.4.0
huggingface-hub==0.30.2
humanfriendly==10.0
hyperframe==6.1.0
idna==3.10
imageio==2.37.0
importlib_metadata==8.6.1
importlib_resources==6.5.2
instructor==1.7.5
ipython==9.0.2
ipython_pygments_lexers==1.1.1
isodate==0.7.2
jedi==0.19.2
Jinja2==3.1.6
jiter==0.8.2
json5==0.10.0
json_repair==0.39.1
jsonlines==3.1.0
jsonpatch==1.33
jsonpickle==4.0.2
jsonpointer==3.0.0
jsonref==1.1.0
jsonschema==4.23.0
jsonschema-specifications==2024.10.1
kubernetes==32.0.1
langchain==0.3.20
langchain-anthropic==0.3.10
langchain-community==0.3.19
langchain-core==0.3.49
#langchain-google-vertexai==2.0.15
langchain-openai==0.3.11
langchain-text-splitters==0.3.6
langgraph==0.3.21
langgraph-checkpoint==2.0.23
langgraph-checkpoint-postgres==2.0.19
langgraph-prebuilt==0.1.3
langgraph-sdk==0.1.57
langmem==0.0.17
langsmith==0.3.15
latex2mathml==3.77.0
lazy_loader==0.4
litellm==1.60.2
lxml==5.4.0
macholib==1.16.3
markdown-it-py==3.0.0
marko==2.1.3
MarkupSafe==3.0.2
marshmallow==3.26.1
matplotlib-inline==0.1.7
mcp==1.6.0
mcpadapt==0.1.3
mdurl==0.1.2
mem0ai==0.1.74
mmh3==5.1.0
monotonic==1.6
mpire==2.10.2
mpmath==1.3.0
msgpack==1.1.0
multidict==6.1.0
multiprocess==0.70.18
mypy-extensions==1.0.0
networkx==3.4.2
ninja==********
numpy==2.2.4
oauthlib==3.2.2
onnxruntime==1.21.0
openai==1.69.0
opencv-python-headless==*********
openpyxl==3.1.5
opentelemetry-api==1.31.0
opentelemetry-exporter-otlp-proto-common==1.31.0
opentelemetry-exporter-otlp-proto-grpc==1.31.0
opentelemetry-exporter-otlp-proto-http==1.31.0
opentelemetry-instrumentation==0.52b0
opentelemetry-instrumentation-asgi==0.52b0
opentelemetry-instrumentation-fastapi==0.52b0
opentelemetry-proto==1.31.0
opentelemetry-sdk==1.31.0
opentelemetry-semantic-conventions==0.52b0
opentelemetry-util-http==0.52b0
orjson==3.10.15
ormsgpack==1.9.0
overrides==7.7.0
packaging==24.2
pandas==2.2.3
parso==0.8.4
pdfminer.six==20231228
pdfplumber==0.11.5
pexpect==4.9.0
pillow==11.1.0
pluggy==1.5.0
portalocker==2.10.1
posthog==3.20.0
prompt_toolkit==3.0.50
propcache==0.3.0
proto-plus==1.26.1
protobuf==5.29.3
psycopg==3.2.6
psycopg-binary==3.2.6
psycopg-pool==3.2.6
psycopg2-binary==2.9.10
ptyprocess==0.7.0
pure_eval==0.2.3
pyasn1==0.6.1
pyasn1_modules==0.4.1
pyclipper==1.3.0.post6
pycparser==2.22
pydantic==2.10.6
pydantic-settings==2.8.1
pydantic_core==2.27.2
Pygments==2.19.1
pyinstaller==6.13.0
pyinstaller-hooks-contrib==2025.3
PyJWT==2.10.1
pylatexenc==2.10
pyparsing==3.2.1
PyPDF2==3.0.1
pypdfium2==4.30.1
PyPika==0.48.9
pyproject_hooks==1.2.0
python-bidi==0.6.6
python-dateutil==2.9.0.post0
python-docx==1.1.2
python-dotenv==1.0.1
python-pptx==1.0.2
pytz==2024.2
pyvis==0.3.2
PyYAML==6.0.2
qdrant-client==1.13.3
referencing==0.36.2
regex==2024.11.6
requests==2.32.3
requests-oauthlib==2.0.0
requests-toolbelt==1.0.0
rich==13.9.4
rpds-py==0.23.1
rsa==4.9
rtree==1.4.0
safetensors==0.5.3
scikit-image==0.25.2
scipy==1.15.2
semchunk==2.2.2
shapely==2.0.7
shellingham==1.5.4
six==1.17.0
sniffio==1.3.1
soupsieve==2.7
SQLAlchemy==2.0.39
sse-starlette==2.3.3
stack-data==0.6.3
starlette==0.46.1
sympy==1.13.3
tabulate==0.9.0
tenacity==9.0.0
tifffile==2025.3.30
tiktoken==0.9.0
tokenizers==0.21.1
tomli==2.2.1
tomli_w==1.2.0
torch==2.7.0
torchvision==0.22.0
tqdm==4.67.1
traitlets==5.14.3
transformers==4.51.3
trustcall==0.0.38
typer==0.15.2
typing-inspect==0.9.0
typing_extensions==4.12.2
tzdata==2025.2
uritemplate==4.1.1
urllib3==2.3.0
uv==0.6.6
uvicorn==0.34.0
uvloop==0.21.0
watchfiles==1.0.4
wcwidth==0.2.13
websocket-client==1.8.0
websockets==15.0.1
wrapt==1.17.2
XlsxWriter==3.2.3
xxhash==3.5.0
yarl==1.18.3
zipp==3.21.0
zstandard==0.23.0
matplotlib==3.10.1