import json


# 解析流程图JSON并生成一条合并路径
def parse_team_road(flow_json):
    """
    解析流程图JSON并生成一条合并路径
    每个节点在路径中只出现一次
    路径从入度为0的节点开始
    """
    # 解析JSON数据
    if isinstance(flow_json, str):
        data = json.loads(flow_json)
    else:
        data = flow_json

    # 从JSON中提取`节点和边
    nodes = data["nodes"]
    edges = data["edges"]

    # 创建节点映射，以便更容易访问节点数据
    nodes_map = {node["id"]: node for node in nodes}

    order = {}
    desc_order = {}
    for edge in edges:
        node_source_id = edge["source"]
        source_node = nodes_map[node_source_id]
        source_node_id = source_node["data"]["id"]

        # if source_node_id in order:
        #     # 删除重复的边
        #     del order[source_node_id]
        #     continue

        node_target_id = edge["target"]
        target_node = nodes_map[node_target_id]
        target_node_id = target_node["data"]["id"]
        order[source_node_id] = target_node_id
        desc_order[target_node_id] = source_node_id

    # 为每个节点创建入度和出度字典
    in_degrees = {node["id"]: 0 for node in nodes}
    out_edges = {node["id"]: [] for node in nodes}

    # 计算每个节点的入度并构建出边列表
    for edge in edges:
        source = edge["source"]
        target = edge["target"]
        in_degrees[target] += 1
        out_edges[source].append(target)

    # 找出入度为0的节点（起始节点）
    start_nodes = [node_id for node_id, degree in in_degrees.items() if degree == 0]

    # 拓扑排序
    result = []
    queue = start_nodes.copy()  # 使用队列进行BFS
    visited = set()

    while queue:
        current_node = queue.pop(0)
        if current_node in visited:
            continue

        result.append(current_node)
        visited.add(current_node)

        for neighbor in out_edges[current_node]:
            in_degrees[neighbor] -= 1
            if in_degrees[neighbor] == 0:
                queue.append(neighbor)

    result = [nodes_map[node_id] for node_id in result]
    # result = [nodes_map[node_id]["data"] for node_id in result]

    return result, order, desc_order


# # Load JSON data from the file
# with open("/Users/<USER>/Downloads/sour.json", "r") as file:
# with open("/Users/<USER>/Downloads/sort2.json", "r") as file:
#     data = json.load(file)

# result, order = parse_team_road(data)
# print(result)
# print(order)

# 使用python解析上述json ，edges为流程图的边，source和target指向流程中的node，
# 请解析为开始到结尾的列表（如果某个node有多个出边，那么按照edges的顺序）
