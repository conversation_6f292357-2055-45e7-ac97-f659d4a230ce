import json
from typing import Any, Dict, List

from crewai import Agent, Task

from common.database_service import DatabaseService
from service.knowledge_service import KnowledgeService
from service.tool_service import ToolService


class TaskModel:
    """
    create table tasks (
        id bigint unsigned not null primary key,
        name varchar(200) default '' not null comment '任务名称',
        description text not null comment '任务描述(crewai)',
        app_id bigint default 0 not null comment '所属应用id',
        agent_team_id bigint default 0 not null comment '所属的自动化工作流id',
        agent_id bigint default 0 not null comment 'task 使用的主agent(crewai)',
        expected_output text not null comment '期望输出结果(crewai)',
        output_file varchar(200) default '' not null comment '结果存储到文件，指定文件路径(crewai)',
        ext_info json not null comment '扩展信息 使用json存储',
        copyable bool default false not null comment '是否可复制',
        create_user_id bigint default 0 not null comment '创建人',
        remark varchar(200) default '' not null comment '备注信息',
        created_time bigint default 0 not null comment '创建时间',
        updated_time bigint default 0 not null comment '修改时间',
        delete_time bigint default 0 not null comment '删除时间'
    ) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci comment 'task表';
    """

    def __init__(
        self,
        id: int,
        name: str,
        description: str,
        app_id: int,
        agent_team_id: int,
        agent_id: int,
        expected_output: str,
        output_file: str,
        ext_info: dict,
        create_user_id: int,
        macro_list: str,
        memory_space: str,
        memory_category: str,
        tool_ids: str,
        async_execution: bool,
        output_struct: str,
        dependent_tasks: str,
    ):
        self.id = id
        self.name = name
        self.description = description
        self.app_id = app_id
        self.agent_team_id = agent_team_id
        self.agent_id = agent_id
        self.expected_output = expected_output
        self.output_file = output_file
        self.ext_info = ext_info
        self.create_user_id = create_user_id
        self.macro_list = macro_list
        self.memory_space = memory_space
        self.memory_category = memory_category
        self.tool_ids = tool_ids
        self.async_execution = async_execution
        self.output_struct = output_struct
        self.dependent_tasks = dependent_tasks


class TaskService(DatabaseService):

    def __init__(self, tool_service: ToolService, knowledge_service: KnowledgeService):
        super().__init__()
        self.tool_service = tool_service
        self.knowledge_service = knowledge_service

    def get_by_app_id(self, app_id: int) -> list[TaskModel] | None:
        """根据app_id获取Task列表"""
        query = """
            SELECT id,name,description,app_id,agent_team_id,agent_id,expected_output,output_file,ext_info,create_user_id,macro_list,memory_space,memory_category,tool_ids,async_execution,output_struct,dependent_tasks
            FROM tasks
            WHERE app_id = %s AND delete_time = 0
        """
        results = self.fetch_all(query, (app_id,))
        if not results:
            return None
        return [TaskModel(**result) for result in results]

    def get_by_app_agent_team(
        self, app_id: int, agent_team_id: int
    ) -> List[TaskModel] | None:
        """根据app_id和agent_team_id获取Task列表"""
        query = """
            SELECT id,name,description,app_id,agent_team_id,agent_id,expected_output,output_file,ext_info,create_user_id,macro_list,memory_space,memory_category,tool_ids,async_execution,output_struct,dependent_tasks
            FROM tasks
            WHERE app_id = %s AND agent_team_id = %s AND delete_time = 0
        """
        results = self.fetch_all(query, (app_id, agent_team_id))
        if not results:
            return None
        return [TaskModel(**result) for result in results]

    def create_llm_task(self, task_model: TaskModel, agent: Agent):
        return Task(
            name=task_model.name,
            description=task_model.description,
            expected_output=task_model.expected_output,
            agent=agent,
        )

    def build_task_json_obj(
        self, task_model: TaskModel, agent: Dict[str, Any], agent_config_dir: str
    ):
        description = self.knowledge_service.parse_content(
            task_model.description, agent_config_dir
        )
        task = {
            "id": task_model.id,
            "name": task_model.name,
            "description": description,
            "expected_output": task_model.expected_output,
            "agent": agent["role"],
        }
        if task_model.output_struct:
            task["output_struct"] = task_model.output_struct
        if task_model.async_execution:
            task["async_execution"] = task_model.async_execution
        if task_model.output_file:
            task["output_file"] = task_model.output_file
        if task_model.memory_space and task_model.memory_category:
            task["memory_extraction"] = {
                "space": task_model.memory_space,
                "category": task_model.memory_category,
            }
        if task_model.dependent_tasks:
            dependent_task_ids = json.loads(task_model.dependent_tasks)
            dependent_task_models = self.get_by_app_id(task_model.app_id)
            for task_id in dependent_task_ids:
                for dependent_task_model in dependent_task_models:
                    if dependent_task_model.id == int(task_id):
                        # 初始化 dependent_tasks 为空列表
                        if "dependent_tasks" not in task:
                            task["dependent_tasks"] = []
                        task["dependent_tasks"].append(dependent_task_model.name)
                        break

        tools = []
        if task_model.tool_ids:
            tool_ids = json.loads(task_model.tool_ids)
            for tool_id in tool_ids:
                tool = self.tool_service.get_tool_key_by_id(tool_id)
                tools.append(tool)
        if len(tools) > 0:
            task["tools"] = tools
        return task
