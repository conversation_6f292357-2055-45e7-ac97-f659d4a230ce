from enum import Enum


class RunnerRecordStatus(Enum):
    # init,running、pending,success、failed
    INIT = "init"
    RUNNING = "running"
    PENDING = "pending"
    SUCCESS = "success"
    STOP = "stop"
    FAILED = "failed"

    def equals(self, value):
        """
        判断传入的值是否与当前枚举成员表示同一个对象

        参数:
            value (str 或 RunnerRecordStatus): 要比较的值

        返回:
            bool: 如果值与当前枚举成员表示相同对象则返回 True，否则返回 False
        """
        if isinstance(value, RunnerRecordStatus):
            return value == self
        elif isinstance(value, str):
            return value == self.value
        return False


class TriggerMode(Enum):
    # web、timer、api
    WEB = "web"
    TIMER = "timer"
    API = "api"

    def equals(self, value):
        if isinstance(value, TriggerMode):
            return value == self
        elif isinstance(value, str):
            return value == self.value
        return False


class RunnerType(Enum):
    # app、agent_team、data_process
    APP = "app"
    CONNECTOR = "connector"
    AGENT_TEAM = "agent_team"
    DATA_PROCESS = "data_process"
    PYTHON = "python"

    def equals(self, value):
        if isinstance(value, RunnerType):
            return value == self
        elif isinstance(value, str):
            return value == self.value
        return False

    @classmethod
    def is_data_process(cls, value: str) -> bool:
        return value == RunnerType.DATA_PROCESS.value
