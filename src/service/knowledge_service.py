from common.database_service import DatabaseService

# from common.mysql_helper import get_sql_conn
from engine.common import process_description_with_knowledge
from service.s3_service import download_from_s3


class KnowledgeModel:
    """
    create table knowledges
    (
        id bigint primary key ,
        file_name varchar(200) default '' not null comment '名称',
        content text not null comment '内容',
        file_type varchar(200) default '' not null comment '文件类型',
        s3_key varchar(200) default '' not null comment 'oss key',
        remark varchar(200) default '' not null comment '备注',
        create_user_id bigint default 0 not null comment '创建人',
        updated_time bigint default 0 not null comment '修改时间',
        created_time bigint default 0 not null comment '创建时间',
        delete_time bigint default 0 not null comment '删除时间'

    )CHARACTER SET utf8mb4
    COLLATE utf8mb4_0900_ai_ci comment '知识库';
    """

    def __init__(
        self,
        id: int,
        file_name: str,
        content: str,
        file_type: str,
        s3_key: str,
        remark: str,
        create_user_id: int,
        is_data: bool,
    ):
        self.id = id
        self.file_name = file_name
        self.content = content
        self.file_type = file_type
        self.s3_key = s3_key
        self.remark = remark
        self.create_user_id = create_user_id
        self.is_data = is_data


class KnowledgeService(DatabaseService):
    def __init__(self):
        super().__init__()

    # 解析内容获取是否有需要替换的知识ID
    def parse_content(self, content: str, config_path: str) -> str:

        def process_knowledge(content):
            return self.get_contents_by_prefix(config_path, content)

        description = process_description_with_knowledge(
            content, process_knowledge, max_depth=0
        )
        return description

    def get_contents_by_prefix(self, config_path, input_string: str) -> str:
        # 如果不是以id:开头，则返回原内容（原内容可能是 url:xxxx）为了后续能继续处理 需要加上[[]]
        if not input_string.startswith("id:") and not input_string.startswith(
            "data-id:"
        ):
            return f"[[{input_string}]]"
        if input_string.startswith("id:"):
            id = input_string[3:].strip()
            knowledge = self.get_knowledge_by_id(int(id))
            if knowledge.is_data:
                raise ValueError(f"{id} is not knowledge")
            # 如果type 是text 直接返回 content字段中的内容
            if knowledge.file_type == "text":
                return knowledge.content
            # 如果是文件类型 则下载文件 并拼接为[[txt:file_path]]
            file_path = download_from_s3(knowledge.s3_key, config_path)

            relative_path = file_path[len(config_path) + 1 :]
            return f"[[{knowledge.file_type}:{relative_path}]]"
        if input_string.startswith("data-id:"):
            id = input_string[8:].strip()
            knowledge = self.get_knowledge_by_id(int(id))
            if not knowledge.is_data:
                raise ValueError(f"{id} is not data")
            # 如果是文件类型 则下载文件 并拼接为[[txt:file_path]]
            file_path = download_from_s3(knowledge.s3_key, config_path)
            print(f"id:{id} data file_path: {file_path}")

            # relative_path = file_path[len(config_path)+1:]
            return f" data_file_path : {file_path} "

    def get_knowledge_by_id(self, id: int):
        """根据ID获取知识库信息"""
        print("get_knowledge_by_id", id)
        query = """
            SELECT id,file_name,content,file_type,s3_key,remark,create_user_id,is_data
            FROM knowledges
            WHERE id = %s
        """
        result = self.fetch_one(query, (id,))
        if result is None:
            return None
        return KnowledgeModel(**result)
