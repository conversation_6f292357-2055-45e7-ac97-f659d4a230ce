from common.database_service import DatabaseService


class AgentTeamModel:
    """
    create table agent_team (
        id bigint unsigned not null primary key,
        name varchar(200) default '' not null comment 'agent_team名称',
        app_id bigint default 0 not null comment '所属应用id',
        copyable bool default false not null comment '是否可复制',
        task_sort json not null comment '排序 json demo [1,2]',
        result_params_key varchar(200) default '' not null comment 'team 执行结束后的结果保存变量，用于作为下一个team的参数名',
        create_user_id bigint default 0 not null comment '创建人',
        remark varchar(200) default '' not null comment '备注信息',
        created_time bigint default 0 not null comment '创建时间',
        updated_time bigint default 0 not null comment '修改时间',
        delete_time bigint default 0 not null comment '删除时间'
    ) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci comment 'agent_team表';
    """

    def __init__(
        self,
        id: int,
        name: str,
        app_id: int,
        task_sort: str,
        result_params_key: str,
        create_user_id: int,
        remark: str,
        is_planning: bool,
        planning_llm_id: int,
        python_struct: str,
    ):
        self.id = id
        self.name = name
        self.app_id = app_id
        self.task_sort = task_sort
        self.result_params_key = result_params_key
        self.create_user_id = create_user_id
        self.remark = remark
        self.is_planning = is_planning
        self.planning_llm_id = planning_llm_id
        self.python_struct = python_struct


class AgentTeamService(DatabaseService):

    def __init__(self):
        super().__init__()

    def get_by_id(self, id: int) -> AgentTeamModel | None:
        """根据ID获取AgentTeam"""
        query = """
            SELECT id,name,app_id,task_sort,result_params_key,create_user_id,remark,is_planning,planning_llm_id,python_struct
            FROM agent_team
            WHERE id = %s
        """
        result = self.fetch_one(query, (id,))
        if result:
            return AgentTeamModel(**result)
        return None

    def get_by_app_id(self, app_id: int) -> list[AgentTeamModel] | None:
        """根据app_id获取AgentTeam列表"""
        query = """
            SELECT id,name,app_id,task_sort,result_params_key,create_user_id,remark,is_planning,planning_llm_id,python_struct
            FROM agent_team
            WHERE app_id = %s AND delete_time = 0
        """
        results = self.fetch_all(query, (app_id,))
        if not results:
            return None
        return [AgentTeamModel(**result) for result in results]
