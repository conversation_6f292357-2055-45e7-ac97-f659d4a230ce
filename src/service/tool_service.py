from common.database_service import DatabaseService

# from tools.human_feedback_websocket import Human<PERSON><PERSON><PERSON><PERSON>
from tools.langchain_tools import arxiv, asknews, wikipedia
from tools.web_search import web_search

# from common.mysql_helper import get_sql_conn


class ToolModel:

    def __init__(self, id: int, name: str, tool_key: str, url: str, transport: str):
        self.id = id
        self.name = name
        self.tool_key = tool_key
        self.url = url
        self.transport = transport


class ToolService(DatabaseService):

    def __init__(self):
        super().__init__()

    def get_tool_by_id(self, tool_id: int):
        """根据tool_id获取工具"""
        query = "SELECT id,name,tool_key,url,transport FROM tools WHERE id = %s"
        result = self.fetch_one(query, (tool_id,))
        if result is None:
            return None
        return self.create_tool(result["tool_key"])

    def get_tool_key_by_id(self, tool_id: int):
        """根据tool_id获取tool_key"""
        query = "SELECT id,name,tool_key,url,transport FROM tools WHERE id = %s"
        result = self.fetch_one(query, (tool_id,))
        if result is None:
            return None
        return result["tool_key"]

    def load_mcp_tools(self):
        """查询所有mcp tool"""
        query = "SELECT id,name,tool_key,url,transport FROM tools WHERE tool_type = 'mcp' and delete_time = 0"
        results = self.fetch_all(query, ())
        if not results:
            return None
        return [ToolModel(**result) for result in results]

    def create_tool(self, tool_key: str):
        tool_handlers = {
            "google_search": web_search,
            "asknews": asknews,
            "arxiv": arxiv,
            "wikipedia": wikipedia,
            # 'wikipedia': wikipedia,
            # 'human_feed_back': HumanFeedBack()
        }
        return tool_handlers.get(tool_key, lambda: None)
