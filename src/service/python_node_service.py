from common.database_service import DatabaseService


class PythonNodeModel:
    #     create table python_nodes (
    #     id bigint unsigned not null primary key,
    #     name varchar(200) default '' not null comment '名称',
    #     remark varchar(200) default '' not null comment '备注信息',
    #     copyable bool default false not null comment '是否可复制',
    #     app_id bigint default 0 not null comment '所属应用id',
    #     code text not null comment 'python代码',
    #     result_var varchar(200) default '' not null comment '结果变量名',
    #     path_tag_var varchar(200) default '' not null comment '路径变量名',
    #     macro_list text not null comment '宏列表',
    #     create_user_id bigint default 0 not null comment '创建人',
    #     updated_time bigint default 0 not null comment '修改时间',
    #     created_time bigint default 0 not null comment '创建时间',
    #     delete_time bigint default 0 not null comment '删除时间'
    # ) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci comment 'python节点表';

    def __init__(
        self,
        id: int,
        name: str,
        remark: str,
        app_id: int,
        code: str,
        result_var: str,
        path_tag_var: str,
        macro_list: str,
        create_user_id: int,
        router: str,
    ):
        self.id = id
        self.name = name
        self.remark = remark
        self.app_id = app_id
        self.code = code
        self.result_var = result_var
        self.path_tag_var = path_tag_var
        self.macro_list = macro_list
        self.create_user_id = create_user_id
        self.router = router


class PythonNodeService(DatabaseService):

    def __init__(self):
        super().__init__()

    def get_by_id(self, id: int) -> PythonNodeModel | None:
        """根据ID获取PythonNode"""
        query = """
            SELECT id,name,remark,app_id,code,result_var,path_tag_var,macro_list,create_user_id,router
            FROM python_nodes
            WHERE id = %s
        """
        result = self.fetch_one(query, (id,))
        if result:
            return PythonNodeModel(**result)
        return None

    def get_by_app_id(self, app_id: int) -> list[PythonNodeModel] | None:
        """根据app_id获取PythonNode列表"""
        query = """
            SELECT id,name,remark,app_id,code,result_var,path_tag_var,macro_list,create_user_id,router
            FROM python_nodes
            WHERE app_id = %s
        """
        results = self.fetch_all(query, (app_id,))
        if not results:
            return None
        return [PythonNodeModel(**result) for result in results]
