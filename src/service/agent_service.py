import json
import os
import shutil
from typing import Any, Dict, List

import pymysql
from crewai import Agent

from common.database_service import DatabaseService
from common.redirect_root import agent_config_home_path, cw_path
from service.knowledge_service import KnowledgeService
from service.llm_service import LLMService
from service.s3_service import download_from_s3
from service.tool_service import ToolService


class AgentModel:
    """
    create table agents (
        id bigint unsigned not null primary key,
        name varchar(200) default '' not null comment '别名',
        app_id bigint default 0 not null comment '所属应用id',
        agent_team_id bigint default 0 not null comment '所属的自动化工作流id',
        role varchar(200) default '' not null comment '角色（crewai）',
        goal text not null comment '目标（crewai）',
        backstory text not null comment '背景信息（crewai）',
        verbose bool default false not null comment '详细思考日志（crewai）',
        allow_delegation bool default false not null comment '是否允许委托给其他agent（crewai）',
        memory bool default false not null comment '是否使用记忆（crewai）',
        max_iter bigint default 2 not null comment '最大迭代次数（crewai）',
        tool_ids json not null comment '工具列表[数组]',
        llm_id bigint default 0 not null comment '模型',
        ext_info json not null comment '扩展信息 json存储',
        copyable bool default false not null comment '是否可复制',
        create_user_id bigint default 0 not null comment '创建人',
        remark varchar(200) default '' not null comment '备注信息',
        created_time bigint default 0 not null comment '创建时间',
        updated_time bigint default 0 not null comment '修改时间',
        delete_time bigint default 0 not null comment '删除时间'
    ) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci comment 'agent表';
    """

    # id,name,app_id,agent_team_id,role,goal,backstory,verbose,allow_delegation,memory,max_iter,tool_ids,llm_id,ext_info,create_user_id
    def __init__(
        self,
        id: int,
        name: str,
        app_id: int,
        agent_team_id: int,
        role: str,
        goal: str,
        backstory: str,
        verbose: bool,
        allow_delegation: bool,
        memory: bool,
        max_iter: int,
        tool_ids: str,
        llm_id: int,
        knowledges: str,
        memory_space: str,
        multimodal: str,
        ext_info: dict,
    ):
        self.id = id
        self.name = name
        self.app_id = app_id
        self.agent_team_id = agent_team_id
        self.role = role
        self.goal = goal
        self.backstory = backstory
        self.verbose = verbose
        self.allow_delegation = allow_delegation
        self.memory = memory
        self.max_iter = max_iter
        self.tool_ids = tool_ids
        self.llm_id = llm_id
        self.knowledges = knowledges
        self.memory_space = memory_space
        self.multimodal = multimodal
        self.ext_info = ext_info


class AgentService(DatabaseService):

    def __init__(
        self,
        llm_service: LLMService,
        tool_service: ToolService,
        knowledge_service: KnowledgeService,
    ):
        super().__init__()
        self.llm_service = llm_service
        self.tool_service = tool_service
        self.knowledge_service = knowledge_service

    def get_by_app_agent_team(
        self, app_id: int, agent_team_id: int
    ) -> List[AgentModel] | None:
        """根据app_id和agent_team_id获取agents"""
        query = """
            SELECT id,name,app_id,agent_team_id,role,goal,backstory,verbose,allow_delegation,memory,max_iter,tool_ids,llm_id,knowledges,memory_space,multimodal,ext_info
            FROM agents
            WHERE app_id = %s AND agent_team_id = %s AND delete_time = 0
        """
        results = self.fetch_all(query, (app_id, agent_team_id))
        if not results:
            return None
        return [AgentModel(**result) for result in results]

    def get_by_app_id(self, app_id: int) -> list[AgentModel] | None:
        """根据app_id获取agents"""
        query = """
            SELECT id,name,app_id,agent_team_id,role,goal,backstory,verbose,allow_delegation,memory,max_iter,tool_ids,llm_id,knowledges,memory_space,multimodal,ext_info
            FROM agents
            WHERE app_id = %s AND delete_time = 0
        """
        results = self.fetch_all(query, (app_id,))
        if not results:
            return None
        return [AgentModel(**result) for result in results]

    def create_llm_agent(self, agent_model: AgentModel):
        llm = self.llm_service.get_llm_by_id(agent_model.llm_id)
        tools = []
        if agent_model.tool_ids:
            tool_ids = json.loads(agent_model.tool_ids)
            for tool_id in tool_ids:
                tool = self.tool_service.get_tool_by_id(tool_id)
                tools.append(tool)
        return Agent(
            llm=llm,
            role=agent_model.role,
            goal=agent_model.goal,
            backstory=(agent_model.backstory),
            verbose=agent_model.verbose,
            allow_delegation=agent_model.allow_delegation,
            #  memory=agent_model.memory,
            max_iter=agent_model.max_iter,
            tools=tools,
        )

    def build_agent_json_obj(
        self, agent_model: AgentModel, agent_config_dir: str
    ) -> Dict[str, Any]:
        llm_key = self.llm_service.get_llm_key_by_id(agent_model.llm_id)
        agent = {
            "id": agent_model.id,
            "name": agent_model.name,
            "role": agent_model.role,
            "goal": agent_model.goal,
            "backstory": agent_model.backstory,
            "verbose": agent_model.verbose,
            "max_iter": agent_model.max_iter,
            "allow_delegation": agent_model.allow_delegation,
            "multimodal": agent_model.multimodal,
            "llm": llm_key,
        }

        if agent_model.memory and agent_model.memory_space:
            agent["memory_space"] = agent_model.memory_space

        agent_knowledge = []
        if agent_model.knowledges:
            knowledges = json.loads(agent_model.knowledges)
            for knowledge in knowledges:
                knowledge_type = knowledge.get("knowledge_type")
                knowledge_value = knowledge.get("knowledge_value")
                if knowledge_type != "id":
                    agent_knowledge.append(f"{knowledge_type}:{knowledge_value}")
                    continue
                knowledge_model = self.knowledge_service.get_knowledge_by_id(
                    knowledge_value
                )
                if not knowledge_model:
                    continue

                download_local_file = ""
                if knowledge_model.file_type == "text":
                    download_local_file = os.path.join(
                        agent_config_dir, f"{knowledge_model.id}.txt"
                    )
                    with open(download_local_file, "w") as f:
                        f.write(knowledge_model.content)
                else:
                    download_local_file = download_from_s3(
                        knowledge_model.s3_key, agent_config_dir
                    )
                # agent knowledge 加载file的时候不知道为什么 会自动再路径前面加个 knowledge，这里把文件复制一份到knowledge目录
                agent_config_home_path_str = str(agent_config_home_path)
                knowledge_dir = ""
                if agent_config_dir.startswith("./"):
                    knowledge_dir = agent_config_dir.replace("./", "knowledge/")
                if agent_config_dir.startswith(agent_config_home_path_str):
                    knowledge_dir = agent_config_dir.replace(
                        agent_config_home_path_str, "knowledge"
                    )
                if agent_config_dir.startswith("/") and not agent_config_dir.startswith(
                    agent_config_home_path_str
                ):
                    knowledge_dir = f"knowledge{agent_config_dir}"

                # 复制文件download_local_file 到knowledge_dir目录得到新文件名
                os.makedirs(knowledge_dir, exist_ok=True)
                file_name = os.path.basename(download_local_file)
                new_file_path = os.path.join(knowledge_dir, file_name)
                shutil.copy2(download_local_file, new_file_path)

                relative_path = download_local_file
                if agent_config_dir.startswith(agent_config_home_path_str):
                    relative_path = download_local_file[
                        len(agent_config_home_path_str) + 1 :
                    ]
                if knowledge_model.file_type == "text":
                    agent_knowledge.append(f"txt:{relative_path}")
                else:
                    agent_knowledge.append(
                        f"{knowledge_model.file_type}:{relative_path}"
                    )

        agent["knowledge"] = agent_knowledge

        tools = []
        if agent_model.tool_ids:
            tool_ids = json.loads(agent_model.tool_ids)
            for tool_id in tool_ids:
                tool = self.tool_service.get_tool_key_by_id(tool_id)
                tools.append(tool)
        if len(tools) > 0:
            agent["tools"] = tools
        return agent
