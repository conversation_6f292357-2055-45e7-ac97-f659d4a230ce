import os

from crewai import LLM

from common.database_service import DatabaseService

# from common.mysql_helper import get_sql_conn


class LLMModel:

    def __init__(self, id: int):
        self.id = id


MaxCompletionTokens = 8192
MaxTokens = 8192
Temperature = 0


class LLMService(DatabaseService):

    def __init__(self):
        super().__init__()

    def get_llm_by_id(self, llm_id: int):
        """根据llm_id获取LLM"""
        query = "SELECT id,name,llm_key FROM llms WHERE id = %s"
        result = self.fetch_one(query, (llm_id,))
        if result is None:
            return None
        return self.create_llm(result["llm_key"])

    def get_llm_key_by_id(self, llm_id: int):
        """根据llm_id获取llm_key"""
        query = "SELECT id,name,llm_key FROM llms WHERE id = %s"
        result = self.fetch_one(query, (llm_id,))
        if result is None:
            return None
        return result["llm_key"]

    def create_llm(self, llm_key: str):
        llm_handlers = {
            "gpt_4o": self.gpt_4o,
            "gemini_2_pro_exp": self.gemini_2_pro_exp,
            "gemini_2_flash_exp": self.gemini_2_flash_exp,
            "claude_35_v2": self.claude_35_v2,
        }
        return llm_handlers.get(llm_key, lambda: None)()

    def gpt_4o(self):
        os.environ["AZURE_API_KEY"] = os.environ["AZURE_OPENAI_API_KEY"]
        os.environ["AZURE_API_BASE"] = os.environ["AZURE_OPENAI_ENDPOINT"]
        os.environ["AZURE_API_VERSION"] = "2024-12-01-preview"
        return LLM(
            model="azure/gpt-4o",
            max_completion_tokens=MaxCompletionTokens,
            max_tokens=MaxTokens,
            temperature=0,
        )

    def gemini_2_pro_exp(self):
        return LLM(
            model="vertex_ai/gemini-2.0-pro-exp-02-05",
            max_completion_tokens=MaxCompletionTokens,
            max_tokens=MaxTokens,
            temperature=0,
            seed=1,
        )

    def gemini_2_flash_exp(self):
        return LLM(
            model="vertex_ai/gemini-2.0-flash-exp",
            max_completion_tokens=MaxCompletionTokens,
            max_tokens=MaxTokens,
            temperature=0,
            seed=1,
        )

    def claude_35_v2(self):
        os.environ["AWS_ACCESS_KEY_ID"] = os.environ["AWS_BEDROCK_ACCESS_KEY_ID"]
        os.environ["AWS_SECRET_ACCESS_KEY"] = os.environ[
            "AWS_BEDROCK_SECRET_ACCESS_KEY"
        ]
        return LLM(
            model="bedrock/us.anthropic.claude-3-5-sonnet-20241022-v2:0",
            max_completion_tokens=MaxCompletionTokens,
            max_tokens=MaxTokens,
            temperature=0,
            seed=123,
        )
