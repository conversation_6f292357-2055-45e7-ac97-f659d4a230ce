from common.database_service import DatabaseService

# from common.mysql_helper import get_sql_conn


class RunnerRecordModel:
    """
    create table runner_record (
        id bigint unsigned not null primary key,
        runner_type varchar(200) default '' not null comment 'runner类型[app、agent_team、data_process]',
        app_id bigint default 0 not null comment 'app_id',
        agent_team_id bigint default 0 not null comment 'agent_team_id',
        data_process_id bigint default 0 not null comment 'data_process_id',
        create_user_id bigint default 0 not null comment 'create_user_id',
        trigger_time bigint default 0 not null comment '触发时间',
        trigger_mode varchar(200) default '' not null comment '触发方式[web、timer、api]',
        trigger_params json not null comment '触发参数',
        execute_status varchar(200) default '' not null comment '执行状态[init,running、pending,success、failed]',
        execute_result text not null comment '执行结果',
        execute_log varchar(200) default '' not null comment '执行日志',
        remark varchar(200) default '' not null comment '备注信息',
        updated_time bigint default 0 not null comment '修改时间',
        created_time bigint default 0 not null comment '创建时间',
        delete_time bigint default 0 not null comment '删除时间'
    ) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci comment 'runner记录表';
    """

    def __init__(
        self,
        id: int,
        runner_type: str,
        app_id: int,
        agent_team_id: int,
        data_process_id: int,
        connector_id: int,
        python_node_id: int,
        create_user_id: int,
        trigger_mode: str,
        trigger_params: str,
        execute_status: str,
        is_multi_turn: bool,
        allow_team_id: int,
        allow_tasks: str,
    ):
        self.id = id
        self.runner_type = runner_type
        self.app_id = app_id
        self.agent_team_id = agent_team_id
        self.data_process_id = data_process_id
        self.connector_id = connector_id
        self.python_node_id = python_node_id
        self.create_user_id = create_user_id
        self.trigger_mode = trigger_mode
        self.trigger_params = trigger_params
        self.execute_status = execute_status
        self.is_multi_turn = is_multi_turn
        self.allow_team_id = allow_team_id
        self.allow_tasks = allow_tasks

    def __str__(self):
        return f"RunnerRecordModel(id={self.id}, runner_type={self.runner_type}, app_id={self.app_id}, agent_team_id={self.agent_team_id}, data_process_id={self.data_process_id}, create_user_id={self.create_user_id}, trigger_mode={self.trigger_mode}, trigger_params={self.trigger_params}, execute_status={self.execute_status})"


class RunnerRecordService(DatabaseService):

    def __init__(self):
        super().__init__()

    def get_by_id(self, runner_record_id: int) -> RunnerRecordModel | None:
        """获取运行记录"""
        query = """
            SELECT id,runner_type,app_id,agent_team_id,data_process_id,connector_id,python_node_id,
                   create_user_id,trigger_mode,trigger_params,execute_status,is_multi_turn,allow_team_id,allow_tasks
            FROM runner_record
            WHERE id = %s
        """
        result = self.fetch_one(query, (runner_record_id,))
        if result:
            return RunnerRecordModel(**result)
        return None

    def update_status(
        self, runner_record_id: int, status: str, execute_result: str
    ) -> bool:
        """更新运行状态"""
        query = "UPDATE runner_record SET execute_status = %s, execute_result = %s WHERE id = %s"
        affected_rows = self.execute_update(
            query, (status, execute_result, runner_record_id)
        )

        if affected_rows > 0:
            return True
        else:
            print("Update failed. No rows were affected.")
            return False

    def update_execute_log(self, runner_record_id: int, s3_log_key: str) -> bool:
        """更新执行日志"""
        query = "UPDATE runner_record SET execute_log = %s WHERE id = %s"
        affected_rows = self.execute_update(query, (s3_log_key, runner_record_id))

        if affected_rows > 0:
            print(
                f"update execute log success,runner_record_id:{runner_record_id} s3_log_key:{s3_log_key}"
            )
            return True
        else:
            print(
                f"update execute log failed,runner_record_id:{runner_record_id} s3_log_key:{s3_log_key}"
            )
            return False
