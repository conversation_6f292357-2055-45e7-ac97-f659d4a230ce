from common.database_service import DatabaseService


class AppModel:
    """
    create table app (
        id bigint unsigned not null primary key,
        name varchar(200) default '' not null comment 'app名称',
        create_user_id bigint default 0 not null comment '创建人',
        copyable bool default false not null comment '是否可复制',
        sort json not null comment '排序 json demo [{"process_type":"agent_team","id":1},{"process_type":"date_process","id":2}]',
        remark varchar(200) default '' not null comment '备注信息',
        created_time bigint default 0 not null comment '创建时间',
        updated_time bigint default 0 not null comment '修改时间',
        delete_time bigint default 0 not null comment '删除时间'
    ) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci comment 'app表';
    """

    def __init__(self, id: int, name: str, sort: str):
        self.id = id
        self.name = name
        self.sort = sort


class AppService(DatabaseService):

    def __init__(self):
        super().__init__()

    def get_by_id(self, id: int) -> AppModel | None:
        """根据ID获取应用信息"""
        query = "SELECT id,name,sort FROM app WHERE id = %s"
        result = self.fetch_one(query, (id,))
        if result:
            return AppModel(**result)
        return None
