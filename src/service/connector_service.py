from common.database_service import DatabaseService


class ConnectorModel:
    """
    create table connectors
    (
        id                bigint primary key,
        name              varchar(200) default ''    not null comment '名称',
        restful_url       varchar(500) default ''    not null comment 'restful url',
        remark            varchar(200) default ''    not null comment '备注',
        copyable          bool         default false not null comment '是否可复制',
        app_id            bigint       default 0     not null comment '所属应用id',
        result_params_key varchar(200) default ''    not null comment '结果参数key',
        macro_list        text                       not null comment '宏列表',
        create_user_id    bigint       default 0     not null comment '创建人',
        updated_time      bigint       default 0     not null comment '修改时间',
        created_time      bigint       default 0     not null comment '创建时间',
        delete_time       bigint       default 0     not null comment '删除时间'
    ) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci comment 'connector表';
    """

    def __init__(
        self,
        id: int,
        name: str,
        restful_url: str,
        remark: str,
        copyable: bool,
        app_id: int,
        result_params_key: str,
        macro_list: str,
        create_user_id: int,
        router: str,
    ):
        self.id = id
        self.name = name
        self.restful_url = restful_url
        self.remark = remark
        self.copyable = copyable
        self.app_id = app_id
        self.result_params_key = result_params_key
        self.macro_list = macro_list
        self.create_user_id = create_user_id
        self.router = router

    def to_json(self):
        return {
            "id": self.id,
            "name": self.name,
            "remark": self.remark,
            "copyable": self.copyable,
            "app_id": self.app_id,
            "result_params_key": self.result_params_key,
            "macro_list": self.macro_list,
            "create_user_id": self.create_user_id,
        }


class ConnectorService(DatabaseService):

    def __init__(self):
        super().__init__()

    def get_by_app_id(self, app_id: int) -> list[ConnectorModel] | None:
        """根据app_id获取Connector列表"""
        query = """
            SELECT id,name,restful_url,remark,copyable,app_id,result_params_key,macro_list,create_user_id,router
            FROM connectors
            WHERE app_id = %s
        """
        results = self.fetch_all(query, (app_id,))
        if not results:
            return None
        return [ConnectorModel(**result) for result in results]

    def get_by_id(self, id: int) -> ConnectorModel | None:
        """根据ID获取Connector"""
        query = """
            SELECT id,name,restful_url,remark,copyable,app_id,result_params_key,macro_list,create_user_id,router
            FROM connectors
            WHERE id = %s
        """
        result = self.fetch_one(query, (id,))
        if result:
            return ConnectorModel(**result)
        return None
