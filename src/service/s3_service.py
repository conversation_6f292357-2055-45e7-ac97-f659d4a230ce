
import os
import boto3
import uuid
import time

bucket_name = "spotmax-maxagent"


def get_s3_client():
    return boto3.client(
        's3',
        aws_access_key_id=os.environ["AWS_S3_ACCESS_KEY_ID"],
        aws_secret_access_key=os.environ["AWS_S3_SECRET_ACCESS_KEY"],
    )


def download_from_s3(s3_key, path):
    if not os.path.exists(path):
        os.makedirs(path, exist_ok=True)

    parts = s3_key.split("/", 1)
    bucket_name = parts[0]
    key = parts[1] if len(parts) > 1 else ""

    file_name = os.path.basename(s3_key)
    file_path = f"{path}/{uuid.uuid4()}_{file_name}"
    try:
        # 下载文件
        s3_client = get_s3_client()
        s3_client.download_file(bucket_name, key, file_path)
        print(f"File downloaded successfully to {file_path}")
        return file_path
    except Exception as e:
        print(f"Error downloading file: {str(e)}")
        return ""


def upload_to_s3(file_path, s3_key):

    if not os.path.exists(file_path):
        print(f"File not found: {file_path}")
        return

    parts = s3_key.split("/", 1)
    bucket_name = parts[0]
    key = parts[1] if len(parts) > 1 else ""

    try:
        s3_client = get_s3_client()
        # 上传文件
        s3_client.upload_file(file_path, bucket_name, key)
        print(f"File uploaded successfully to {s3_key}")
    except Exception as e:
        print(f"Error uploading file: {str(e)}")
