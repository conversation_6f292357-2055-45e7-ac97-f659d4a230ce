import asyncio
import base64
import json
import logging
import os
import sys
import threading
from typing import Any

import websockets
from crewai import TaskOutput
from crewai.agents.parser import AgentA<PERSON>, AgentFinish
from crewai.tools.tool_types import ToolResult
from dotenv import load_dotenv
from loguru import logger
from watchdog.observers import Observer

from common.message_queue import read_message_queue, write_message_queue
from common.upload_output_file import OutputDirHandler, update_log
from runner.runner import Runner
from service.agent_service import AgentService
from service.agent_team_service import AgentTeamService
from service.app_service import AppService
from service.connector_service import ConnectorService
from service.knowledge_service import KnowledgeService
from service.llm_service import LLMService
from service.python_node_service import PythonNodeService
from service.runner_record_service import RunnerRecordService
from service.status_enum import RunnerRecordStatus
from service.task_service import TaskService
from service.tool_service import ToolService
from tools.command_websocket import CONTINUE_THE_AUTOMATION, collect_command

# 加载 .env 文件
dotenv_path = os.getenv("DOT_PATH", ".env")
if not load_dotenv(dotenv_path, override=True):
    raise ValueError(f"无法加载环境变量文件: {dotenv_path}")

log_file_path = "log/app.log"
# 删除默认的终端输出处理器
logger.remove()
logger.add(
    log_file_path, rotation="50 MB", format="{message}", mode="a", compression="zip"
)

# 全局变量存储当前的WebSocket连接
current_client = None

# 保存原始的stdout以便调试
original_stdout = sys.stdout


# 创建自定义 StreamHandler 将日志发送到 WebSocket
class WebSocketStreamHandler(logging.StreamHandler):

    def __init__(self, original_stdout):
        super().__init__()
        self.original_stdout = original_stdout

    def emit(self, record):
        # 仍然在控制台输出
        self.original_stdout.write(f"{record.msg}\n")
        self.original_stdout.flush()

        try:
            content = base64.b64encode(str(record.msg).encode("utf-8")).decode("utf-8")
            message_json = (
                f"""{{"type":"msg","source":"logging","content":"{content}"}}"""
            )
            write_message_queue.put(message_json)
            logger.info(message_json)
        except Exception as e:
            print(f"Error in WebSocketStreamHandler.emit: {e}", file=original_stdout)


# 自定义的 stdout 重定向
class WebSocketStdout:

    def __init__(self, original_stdout):
        self.original_stdout = original_stdout

    def write(self, message):
        # 仍然在控制台输出
        self.original_stdout.write(message)
        self.original_stdout.flush()

        try:
            content = base64.b64encode(str(message).encode("utf-8")).decode("utf-8")
            message_json = (
                f"""{{"type":"msg","source":"print","content":"{content}"}}"""
            )
            write_message_queue.put(message_json)
            logger.info(message_json)
        except Exception as e:
            print(f"Error in WebSocketStdout.write: {e}", file=self.original_stdout)

    def flush(self):
        self.original_stdout.flush()

    def isatty(self):
        return self.original_stdout.isatty()


# 设置重定向
sys.stdout = WebSocketStdout(original_stdout)
# 设置日志拦截
root_logger = logging.getLogger()
root_logger.setLevel(logging.INFO)  # 设置日志级别
websocket_handler = WebSocketStreamHandler(original_stdout)
# websocket_handler.setFormatter(
#     logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s'))
root_logger.addHandler(websocket_handler)


# 从环境变量获取要运行的runner记录ID
runner_record_id = os.getenv("RUNNER_RECORD_ID", "0")
# print(f"Runner record id: {runner_record_id}")
logging.info(f"Runner record id: {runner_record_id}")

# 发送消息给客户端


async def send_messages():
    while True:
        # 等待有客户端连接
        if current_client is None:
            await asyncio.sleep(0.1)
            continue

        # 非阻塞地检查队列
        try:
            message_sent_this_round = False
            while not write_message_queue.empty() and current_client is not None:
                try:
                    message = write_message_queue.get_nowait()
                    await current_client.send(message)
                    message_sent_this_round = True
                except websockets.exceptions.ConnectionClosed:
                    print(
                        "Client connection closed during message send",
                        file=original_stdout,
                    )
                    # Don't set current_client to None here as it's handled in handler function
                    break
                except Exception as e:
                    print(f"Error sending message to client: {e}", file=original_stdout)
                    # Continue and try next message
                    continue

            # 如果这轮发送了消息，打印队列状态
            if message_sent_this_round and not write_message_queue.empty():
                print(
                    f"消息发送中，队列剩余: {write_message_queue.qsize()}",
                    file=original_stdout,
                )

        except Exception as e:
            print(f"Error in message sending loop: {e}", file=original_stdout)

        # Short sleep to prevent CPU overuse
        await asyncio.sleep(0.01)


stop_event = threading.Event()


def handle_message(message):
    try:
        message = json.loads(message)
        print("Parsed JSON message:", message)
        if message["type"] == "command":
            decoded_content = base64.b64decode(message["content"]).decode("utf-8")
            print(f"Received command: {decoded_content}")
            if decoded_content == "stop":
                stop_event.set()
                return True
            else:
                return False
        else:
            return False
    except (json.JSONDecodeError, KeyError, base64.binascii.Error) as e:
        print(f"Error processing message: {e}")
        return False


# WebSocket 处理函数
async def handler(websocket):
    global current_client

    print(f"New client connected from {websocket.remote_address}", file=original_stdout)

    # 更新当前客户端
    current_client = websocket

    try:
        # 发送欢迎消息
        await websocket.send("[SYS] Connection established...")

        # 保持连接直到客户端断开
        async for message in websocket:
            if handle_message(message):
                continue
            read_message_queue.put(message)

    except websockets.exceptions.ConnectionClosed:
        print(
            f"Client connection closed from {websocket.remote_address}",
            file=original_stdout,
        )
    except Exception as e:
        print(f"Error in websocket handler: {e}", file=original_stdout)
    finally:
        if current_client == websocket:
            print(
                f"WebSocket handler finished for {websocket.remote_address}",
                file=original_stdout,
            )
            # 只有在应用完成后才清空客户端引用
            if app_finished.is_set():
                print("应用已完成，清空客户端引用", file=original_stdout)
                current_client = None
            else:
                print("应用仍在运行，保持客户端引用", file=original_stdout)


server = None
send_task = None
app_finished = threading.Event()


def run_app():
    print("Starting app...")

    # 初始化App服务
    app_service = AppService()
    # 初始化AgentTeam服务
    agent_team_service = AgentTeamService()
    # 初始化LLM服务
    llm_service = LLMService()
    # 初始化Tool服务
    tool_service = ToolService()
    knowledge_service = KnowledgeService()
    # 初始化Agent服务
    agent_service = AgentService(llm_service, tool_service, knowledge_service)
    # 初始化Task服务
    task_service = TaskService(tool_service, knowledge_service)
    # 初始化Runner记录服务
    runner_record_service = RunnerRecordService()
    # 初始化Connector服务
    connector_service = ConnectorService()
    # 初始化PythonNode服务
    python_node_service = PythonNodeService()
    # 初始化Runner
    runner = Runner(
        app_service=app_service,
        agent_team_service=agent_team_service,
        llm_service=llm_service,
        agent_service=agent_service,
        task_service=task_service,
        tool_service=tool_service,
        runner_record_service=runner_record_service,
        connector_service=connector_service,
        python_node_service=python_node_service,
    )

    # 定义回调函数将数据放入队列
    def task_callback(task_output: TaskOutput):
        content = base64.b64encode(str(task_output.raw).encode("utf-8")).decode("utf-8")
        message_json = (
            f"""{{"type":"thought","source":"task_callback","content":"{content}"}}"""
        )
        write_message_queue.put(message_json)
        logger.info(message_json)

    def step_callback(step_output: Any):
        if isinstance(step_output, AgentFinish):
            data = {
                "type": "thought",
                "subtype": "finish",
                "data": step_output.text,
            }
        elif isinstance(step_output, AgentAction):
            data = {
                "type": "thought",
                "subtype": "action",
                "data": step_output.text,
            }
        elif isinstance(step_output, ToolResult):
            data = {
                "type": "thought",
                "subtype": "tool",
                "data": step_output.result,
            }
        else:
            data = {"type": "thought", "subtype": "other", "data": str(step_output)}
        content = base64.b64encode(
            json.dumps(data, ensure_ascii=False).encode("utf-8")
        ).decode("utf-8")
        message_json = (
            f"""{{"type":"thought","source":"step_callback","content":"{content}"}}"""
        )
        write_message_queue.put(message_json)
        logger.info(message_json)

    try:
        current_runner_record_id = runner_record_id
        logging.info(f"Start running id: {current_runner_record_id}")
        print(f"Start running id: {current_runner_record_id}")
        historical_context = ""
        while True:
            # 获取要执行的记录
            runner_record = runner_record_service.get_by_id(current_runner_record_id)
            if runner_record is None:
                logging.error(f"Runner record not found: {current_runner_record_id}")
                print(f"Runner record not found: {current_runner_record_id}")
                runner_record_service.update_status(
                    current_runner_record_id,
                    RunnerRecordStatus.FAILED.value,
                    f"runner record not found: {current_runner_record_id}",
                )
                return
            logging.info(f"Runner record : {runner_record}")
            runner_record.historical_context = historical_context
            # 启动运行
            result = runner.run_record(
                runner_record=runner_record,
                task_callback=task_callback,
                step_callback=step_callback,
            )

            content = base64.b64encode(
                json.dumps(result, ensure_ascii=False).encode("utf-8")
            ).decode("utf-8")
            message_json = (
                f"""{{"type":"thought","source":"result","content":"{content}"}}"""
            )
            write_message_queue.put(message_json)
            logger.info(message_json)

            historical_context += f"\n\request: {runner_record.trigger_params} \n\n response: {result} \n\n"
            if runner_record.is_multi_turn:
                user_input = collect_command(CONTINUE_THE_AUTOMATION).strip().lower()
                # user_input = user_input = input(
                #     "Do you want to continue the automation (holding the context) ? (Y/N): ").strip().lower()
                if user_input == "n":
                    break
                else:
                    current_runner_record_id = user_input
                    continue
            else:
                break

    except Exception as e:
        print(f"Error running app: {e}")
        logging.error(f"Error running app: {e}")
        runner_record_service.update_status(
            int(runner_record_id),
            RunnerRecordStatus.FAILED.value,
            f"Error running app: {e}",
        )
    finally:
        logging.info("App execution completed.")
        # Set the event to signal that the app has finished
        app_finished.set()


# 修改 start_server 函数以在应用完成后关闭
async def start_server():
    global server, send_task
    # Create a server instance with the handler function
    server = await websockets.serve(handler, "0.0.0.0", 8765)
    print("WebSocket 服务器已启动在 ws://0.0.0.0:8765", file=original_stdout)
    # 创建并启动消息发送任务
    send_task = asyncio.create_task(send_messages())
    # 在主线程中打印一些消息
    print("WebSocket 服务器已启动在 ws://0.0.0.0:8765")
    logging.info("WebSocket logging 已启用")

    # 在另一个线程启动应用逻辑
    app_thread = threading.Thread(target=run_app, daemon=True)
    app_thread.start()

    # 检测output 目录中的文件如果有新文件就上传到s3
    output_dir = "."  # 根据实际路径修改
    event_handler = OutputDirHandler(output_dir)
    observer = Observer()
    observer.schedule(event_handler, output_dir, recursive=False)
    observer.start()

    # 等待应用完成运行
    while not app_finished.is_set() and not stop_event.is_set():
        await asyncio.sleep(0.5)

    if stop_event.is_set():
        print("应用已被用户停止", file=original_stdout)
        logging.info("应用已被用户停止")
        # 当应用完成后，发送一条最终消息
        if current_client is not None:
            try:
                await current_client.send(
                    """{"type":"end", "content":"QXBwbGljYXRpb24gaGFzIHN0b3A="}"""
                )
                # 给最终消息一些时间发送
                await asyncio.sleep(1)
            except Exception as e:
                print(f"Error sending final message: {e}", file=original_stdout)

        runner_record_service.update_status(
            int(runner_record_id),
            RunnerRecordStatus.STOP.value,
            "client send stop",
        )
        # 设置应用完成事件，以确保正确关闭
        app_finished.set()

    # 应用完成后，等待所有文件上传完成
    print("应用已完成，等待文件上传完成...", file=original_stdout)
    logging.info("应用已完成，等待文件上传完成...")

    # 在异步环境中等待上传完成
    def wait_uploads():
        event_handler.wait_for_uploads_completion(timeout=120)  # 等待最多2分钟

    # 在线程池中运行同步的等待函数
    import concurrent.futures

    with concurrent.futures.ThreadPoolExecutor() as executor:
        await asyncio.get_event_loop().run_in_executor(executor, wait_uploads)

    # 手动发送剩余消息
    print("手动发送剩余消息...", file=original_stdout)
    remaining_messages = write_message_queue.qsize()
    print(f"队列中剩余消息数: {remaining_messages}", file=original_stdout)
    print(f"当前客户端状态: {current_client is not None}", file=original_stdout)

    # 手动发送所有剩余消息
    sent_count = 0
    failed_count = 0

    # 保存当前客户端引用，避免在发送过程中被清空
    client_for_sending = current_client

    while not write_message_queue.empty():
        try:
            message = write_message_queue.get_nowait()

            # 检查客户端是否仍然可用
            if client_for_sending is not None:
                try:
                    await client_for_sending.send(message)
                    sent_count += 1
                    if (
                        sent_count % 5 == 0 or sent_count == remaining_messages
                    ):  # 每5条或最后一条打印进度
                        print(
                            f"手动发送消息 {sent_count}/{remaining_messages}",
                            file=original_stdout,
                        )
                except websockets.exceptions.ConnectionClosed:
                    print("客户端连接已关闭，无法继续发送消息", file=original_stdout)
                    # 将消息放回队列
                    write_message_queue.put(message)
                    break
                except Exception as e:
                    print(f"发送消息失败: {e}", file=original_stdout)
                    failed_count += 1
                    # 继续尝试发送下一条消息
            else:
                print("没有可用的客户端连接", file=original_stdout)
                # 将消息放回队列
                write_message_queue.put(message)
                break

        except Exception as e:
            print(f"获取消息失败: {e}", file=original_stdout)
            break

    print(
        f"手动发送完成，已发送: {sent_count}, 失败: {failed_count}, 剩余: {write_message_queue.qsize()}",
        file=original_stdout,
    )

    # 当应用完成后，发送一条最终消息
    if current_client is not None:
        try:
            await current_client.send(
                """{"type":"end", "content":"QXBwbGljYXRpb24gaGFzIGNvbXBsZXRlZA=="}"""
            )
            # 给最终消息一些时间发送
            await asyncio.sleep(1)
        except Exception as e:
            print(f"Error sending final message: {e}", file=original_stdout)

    # 取消消息发送任务
    if send_task:
        send_task.cancel()
        try:
            await send_task
        except asyncio.CancelledError:
            print("消息发送任务已取消", file=original_stdout)

    # 关闭客户端连接
    if current_client is not None:
        try:
            await current_client.close()
        except Exception as e:
            print(f"Error closing client connection: {e}", file=original_stdout)

    # 关闭文件检测
    observer.stop()
    # 上传日志
    s3_log_key = update_log(log_file_path)
    if s3_log_key:
        # 初始化Runner记录服务
        runner_record_service = RunnerRecordService()
        runner_record_service.update_execute_log(runner_record_id, s3_log_key)

    # await asyncio.sleep(60)  # 5分钟
    # 关闭服务器
    print("应用已完成，正在关闭 WebSocket 服务器...", file=original_stdout)
    server.close()
    await server.wait_closed()

    if not stop_event.is_set():
        await asyncio.sleep(240)  # 5分钟


# 主入口点
if __name__ == "__main__":
    try:
        # 启动异步事件循环
        asyncio.run(start_server())
    except KeyboardInterrupt:
        print("服务器已被用户中断", file=original_stdout)
    except Exception as e:
        print(f"服务器发生错误: {e}", file=original_stdout)
    finally:
        # 恢复标准输出
        sys.stdout = original_stdout
        print("程序已退出")
