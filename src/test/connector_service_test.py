# from common.mysql_helper import get_sql_conn
from dotenv import load_dotenv
from pymysql.cursors import Cursor

from service.connector_service import ConnectorService

# def test_get_by_id(id: int):
#     print("Starting app...")
#     # 初始化数据库连接
#     cursor: Cursor
#     cursor = get_sql_conn()
#     connector_service = ConnectorService(cursor)
#     connector = connector_service.get_by_id(id)
#     print(f"Connector JSON: {connector.to_json() if connector else None}")

#     assert connector is not None
#     assert connector.id == id
#     assert connector.name == "邮件"


# load_dotenv()
# test_get_by_id(id=1911695451096752128)
