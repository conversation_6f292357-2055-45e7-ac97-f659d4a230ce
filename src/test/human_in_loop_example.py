from crewai import LLM, Agent, Crew, Process, Task

# from prebuilt_agents.prebuild_agents import user_feedback_agent
from src.engine.models import Models

# from src.tools.human_feedback import HumanTool
# from tools.memory import MemTool
from src.tools.web_search import web_search

# Loading Tools

# human_feedback_tool = HumanTool()
models = Models()
# memory_tool = MemTool()
llm = models.gemini_2_flash_exp()
# llm = models.gemini_2_flash_001()

# llm_m = models.gpt_4o()

manager = Agent(
    role="Project Manager",
    goal="Coordinate your team members to complete the tasks and make the user satisfied",
    backstory=(
        "You are a Project Manager at a tech company, responsible for overseeing the completion of various projects. "
    ),
    verbose=True,
    allow_delegation=True,
    llm=llm,
)


# Define your agents with roles, goals, tools, and additional attributes
researcher = Agent(
    role="Senior Research Analyst",
    goal="Uncover cutting-edge developments in AI and data science according to the requirements and user feedback",
    backstory=(
        "You are a Senior Research Analyst at a leading tech think tank. "
        "Your expertise lies in identifying emerging trends and technologies in AI and data science. "
        "You have a knack for dissecting complex data and presenting actionable insights."
    ),
    verbose=True,
    allow_delegation=False,
    llm=llm,
    tools=[web_search],
)

# user_feedback_collector = user_feedback_agent(llm_m)

# Create tasks for your agents
task = Task(
    description=(
        "Conduct a comprehensive analysis of the latest advancements in AI agent and multi-agent. "
        "Compile the findings in a report. "
        "Make sure to check with the user (get the user's feedback) if the report is good before finalizing your answer."
        """
        ---
        The following steps are to complete a user satisfied task:
        1. Write the draft report on the latest advancements.
        2. Get the feedback from the user about the report.
        3. Revise the report according to  the user feedback.
        Repeat the step 2 and 3 until user satisfaction feedback is received..
        """
    ),
    agent=manager,
    expected_output="A comprehensive full report",
)

# Instantiate your crew with a sequential process
crew = Crew(
    agents=[researcher],
    tasks=[task],
    verbose=1,
    memory=False,
)

# Get your crew to work!
result = crew.kickoff()

print("######################")
print(result)
print("######### Memory #############")
# print(memory_tool.get_memories())
