

import json

import os
import boto3
import uuid


def download_from_s3(s3_key, path):
    s3 = boto3.client(
        's3',
        aws_access_key_id=os.environ["AWS_S3_ACCESS_KEY_ID"],
        aws_secret_access_key=os.environ["AWS_S3_SECRET_ACCESS_KEY"],
    )

    if not os.path.exists(path):
        os.makedirs(path, exist_ok=True)

    parts = s3_key.split("/", 1)
    bucket_name = parts[0]
    key = parts[1] if len(parts) > 1 else ""

    file_name = os.path.basename(s3_key)
    file_path = f"{path}/{uuid.uuid4()}_{file_name}"
    try:
        # 下载文件
        s3.download_file(bucket_name, key, file_path)
        print(f"File downloaded successfully to {file_path}")
        return file_path
    except Exception as e:
        print(f"Error downloading file: {str(e)}")
        return ""


json_str_obj = """
{"topic": "大模型发展"}
"""

json_str_list = """
[
{"name":"topic", "type":"string", "value":"大模型发展"},
{"name":"ceshi","type":"string", "value":"ceshi value"},
{"name":"target_file", "type":"file", "value":"spotmax-maxagent/2025/04/25/f06e2f2bc85000de8bab3310cfd8b636.txt"}
]
"""

json_str_obj = json.loads(json_str_obj)
print(type(json_str_obj))
json_str_list = json.loads(json_str_list)
print(type(json_str_list))


if type(json_str_obj) is dict:
    print("json_str_obj is dict")
if type(json_str_list) is list:
    print("json_str_list is list")

    dir_params = {}
    for param in json_str_list:
        if not param.get("type") or not param.get("value") or not param.get("name"):
            continue
        if param["type"] == "string":
            dir_params[param["name"]] = param["value"]
        if param["type"] == "file":
            s3_key = param["value"]
            file_name = s3_key.split('/')[-1]
            local_file_path = "/Users/<USER>/workspace/Jarvis/jarvis/agent_config/aa/"
            full_path = download_from_s3(s3_key, local_file_path)
            dir_params[param["name"]] = full_path

    print(dir_params)



