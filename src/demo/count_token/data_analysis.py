from crewai import Agent, Crew, Process, Task

from src.engine.prebuilt_llms import prebuilt_llms
from src.tools.python_repl import code_interpreter as code_interpreter_tool

# Initialize the tool

llm = prebuilt_llms.get("gpt-4.1")()
# Define an agent that uses the tool
data_analyst = Agent(
    role="Data Analyst",
    goal="Analyze data using Python code",
    backstory="""You are an expert data analyst who specializes in using Python
    to analyze and visualize data. You can write efficient code to process
    large datasets and extract meaningful insights.""",
    tools=[code_interpreter_tool],
    verbose=True,
    llm=llm,
)

# Create a task for the agent
analysis_task = Task(
    description="""
    Write Python code to:
    1. Generate a random data set of 100 x and y coordinate points
2. Calculate the correlation coefficient between x and y
3. Create a heat map of the data
4. Print the correlation coefficient and save the plot as a "scatter.png" file

Make sure to process all necessary imports and print the results.

    """,
    expected_output="The correlation coefficient and confirmation that the scatter plot has been saved.",
    agent=data_analyst,
)

# Run the task
crew = Crew(
    agents=[data_analyst],
    tasks=[analysis_task],
    verbose=True,
    process=Process.sequential,
)

# Execute the crew
result = crew.kickoff()

# Access usage metrics after execution
print("=== 任务执行结果 ===")
print(result)

print("\n=== Token使用情况详细分析 ===")
usage_metrics = crew.usage_metrics

# # 打印详细的Token使用信息
print(f"总Token数: {usage_metrics.total_tokens}")
print(f"提示Token数: {usage_metrics.prompt_tokens}")
print(f"缓存提示Token数: {usage_metrics.cached_prompt_tokens}")
print(f"完成Token数: {usage_metrics.completion_tokens}")
print(f"成功请求次数: {usage_metrics.successful_requests}")
