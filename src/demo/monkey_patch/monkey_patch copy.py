
# third_party_lib.py
import builtins
import os
import uuid
from pathlib import Path

from flask import Flask, request, send_file

# from third_party_lib import generate_file  # 假设这是第三方库


def generate_file(filename):
    with open(filename, "w") as f:
        f.write("Third party generated content.")


# 全局变量，保存重定向路径（你可以为每个请求生成不同的目录）
redirect_root = Path("/tmp/redirect_output")
redirect_root.mkdir(parents=True, exist_ok=True)

# 保留原始 open
_original_open = builtins.open

# Monkey patch 的 open


def redirected_open(file, *args, **kwargs):
    # 如果是相对路径且不是目录访问
    if isinstance(file, str) and not os.path.isabs(file) and not file.startswith("/tmp/"):
        request_id = getattr(redirected_open, "request_id", None)
        if not request_id:
            raise RuntimeError("No request_id set for redirected open()")
        redirected_path = redirect_root / request_id / file
        redirected_path.parent.mkdir(parents=True, exist_ok=True)
        file = str(redirected_path)
    return _original_open(file, *args, **kwargs)


# 注入 monkey patch
builtins.open = redirected_open


app = Flask(__name__)


@app.route("/generate", methods=["GET"])
def generate():
    filename = request.args.get("filename")
    if not filename:
        return "Missing filename", 400

    # 每个请求一个独立目录
    request_id = str(uuid.uuid4())
    redirected_open.request_id = request_id

    try:
        # 调用三方库写文件（它调用 open(filename)）
        generate_file(filename)

        final_path = redirect_root/request_id/filename
        if final_path.exists():
            return send_file(str(final_path), as_attachment=True)
        else:
            return "File not generated", 500

    finally:
        # 清理上下文（可选）
        del redirected_open.request_id


if __name__ == "__main__":
    app.run(port=8080)
