# from smolagents import CodeAgent, WebSearchTool, InferenceClientModel
# from smolagents.models import AzureOpenAIServerModel
# import os
# # Initialize the Azure OpenAI model
# class CodingAgent:
#     def __init__(self):
#         self.model = AzureOpenAIServerModel(
#             model_id="gpt-41",  # This is your Azure OpenAI deployment name
#             azure_endpoint=os.environ["AZURE_OPENAI_ENDPOINT_4_1"],
#             api_key=os.environ["AZURE_OPENAI_API_KEY_4_1"],
#             api_version="2024-12-01-preview",
#         )

#         self.agent = CodeAgent(
#                                 tools=[WebSearchTool()],
#                                 model=self.model,
#                                 additional_authorized_imports=["pandas",
#                                                                "seaborn",
#                                                                "matplotlib",
#                                                                "matplotlib.*",

#                                                                "openpyxl",
#                                                                 "numpy",
#                                                                 "math",
#                                                                 "scipy",
#                                                                 "scipy.*",
#                                                                 "scikit_learn",
#                                                                 "scikit_learn.*",
#                                                                 "stat"
#                                                                 "pandas.*",
#                                                                 "seaborn.*",
#                                                                 "time",
#                                                                 "datetime",
#                                                                 "json",
#                                                                ],
#                                 stream_outputs=True,
#                                )
#     def run(self, prompt):
#         return self.agent.run(prompt)

# from crewai.tools import tool

# coding_agent = CodingAgent()

# @tool("coding tool")
# def code_for_requirement(requirement: str) -> str:
#     """
#     A tool to generate code based on a given requirement and execute it to return the result.
#     The input is the requirement for the code。
#     The output is the  execution result of the generated code.(The generated diagrams will be saved in the current directory as the png files.)
#     """
#     requirement = requirement + "If any dependencies are required and not installed, please install them using pip."
#     return coding_agent.run(requirement)
