{"tools": [{"name": "math_server", "description": "Math server for arithmetic operations", "stdio_server_parameters": {"command": "python", "args": ["mcp_tools_cfg/math_server.py"]}}, {"name": "youtube_video_summarizer", "description": "Youtube video summarizer", "stdio_server_parameters": {"command": "npx", "args": ["-y", "youtube-video-summarizer-mcp"]}}, {"name": "mtg_data_query_tool", "description": "query the programtic ads business data from database", "server_parameters": {"url": "http://mcp-alchemy-nocodb.mtg-ai-agent-internal.rayjump.com/sse", "transport": "sse"}}, {"name": "some_streamable_tool", "description": "create business data transaction", "server_parameters": {"url": "http://mcp-mtg-demand-stream.mtg-ai-agent-internal.rayjump.com/mcp", "transport": "streamable-http"}}]}