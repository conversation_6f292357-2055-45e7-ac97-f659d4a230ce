import logging
from typing import Any, Dict, List, Optional, Tuple

from src.common.mysql_helper_pool import get_sql_conn, retry_on_db_error

logger = logging.getLogger(__name__)


class DatabaseService:
    """数据库服务基类，提供通用的数据库操作方法"""

    @retry_on_db_error(max_retries=3, delay=1)
    def fetch_one(
        self, query: str, params: Optional[Tuple] = None
    ) -> Optional[Dict[str, Any]]:
        """执行查询并返回单行结果"""
        with get_sql_conn() as cursor:
            cursor.execute(query, params or ())
            result = cursor.fetchone()
            if result:
                field_names = [column[0] for column in cursor.description]
                return dict(zip(field_names, result))
            return None

    @retry_on_db_error(max_retries=3, delay=1)
    def fetch_all(
        self, query: str, params: Optional[Tuple] = None
    ) -> List[Dict[str, Any]]:
        """执行查询并返回所有结果"""
        with get_sql_conn() as cursor:
            cursor.execute(query, params or ())
            results = cursor.fetchall()
            if results:
                field_names = [column[0] for column in cursor.description]
                return [dict(zip(field_names, row)) for row in results]
            return []

    @retry_on_db_error(max_retries=3, delay=1)
    def execute_update(self, query: str, params: Optional[Tuple] = None) -> int:
        """执行更新操作并返回受影响的行数"""
        with get_sql_conn() as cursor:
            affected_rows = cursor.execute(query, params or ())
            return affected_rows

    @retry_on_db_error(max_retries=3, delay=1)
    def execute_insert(self, query: str, params: Optional[Tuple] = None) -> int:
        """执行插入操作并返回插入的ID"""
        with get_sql_conn() as cursor:
            cursor.execute(query, params or ())
            return cursor.lastrowid

    @retry_on_db_error(max_retries=3, delay=1)
    def execute_batch(self, query: str, params_list: List[Tuple]) -> int:
        """执行批量操作"""
        with get_sql_conn() as cursor:
            affected_rows = cursor.executemany(query, params_list)
            return affected_rows

    def safe_execute(self, operation_name: str, func, *args, **kwargs):
        """安全执行数据库操作，包含错误处理和日志"""
        try:
            logger.info(f"开始执行数据库操作: {operation_name}")
            result = func(*args, **kwargs)
            logger.info(f"数据库操作成功: {operation_name}")
            return result
        except Exception as e:
            logger.error(f"数据库操作失败: {operation_name}, 错误: {e}")
            raise
