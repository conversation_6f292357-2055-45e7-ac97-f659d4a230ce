import json
import logging
import os
import threading
import time
from contextlib import contextmanager

import boto3
import pymysql
from botocore.exceptions import ClientError
from dbutils.pooled_db import PooledDB

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def get_secret():
    # secret_name = "spotmax-maxcloud-rds-hk-admin"
    # region_name = "ap-east-1"
    secret_name = "spotmax-maxagent-rds-vg-admin"
    region_name = "us-east-1"

    aws_access_key_id = os.environ["AWS_DB_ACCESS_KEY_ID"]
    aws_secret_access_key = os.environ["AWS_DB_SECRET_ACCESS_KEY"]

    # Create a Secrets Manager client
    session = boto3.Session(
        aws_access_key_id=aws_access_key_id, aws_secret_access_key=aws_secret_access_key
    )
    client = session.client(service_name="secretsmanager", region_name=region_name)

    try:
        get_secret_value_response = client.get_secret_value(SecretId=secret_name)
    except ClientError as e:
        # For a list of exceptions thrown, see
        # https://docs.aws.amazon.com/secretsmanager/latest/apireference/API_GetSecretValue.html
        raise e

    secret = get_secret_value_response["SecretString"]
    return secret


class DatabaseConnectionPool:
    """数据库连接池，使用PooledDB实现，支持并发访问"""

    def __init__(self, max_connections=20, min_connections=5):
        self._max_connections = max_connections
        self._min_connections = min_connections
        self._db_config = None
        self._pool = None
        self._lock = threading.Lock()

        # 初始化PooledDB连接池
        self._initialize_pool()

    def _get_db_config(self):
        """获取数据库配置"""
        if self._db_config is None:
            secret = json.loads(get_secret())
            database_name = os.getenv("DATABASE_NAME", "jarvis_test")
            self._db_config = {
                "host": secret.get("host", ""),
                "user": secret.get("username", ""),
                "password": secret.get("password", ""),
                "database": database_name,
                "port": secret.get("port", 3306),
                "charset": "utf8mb4",
                "autocommit": True,
                "connect_timeout": 60,
                "read_timeout": 30,
                "write_timeout": 30,
                # 启用连接池相关参数
                "max_allowed_packet": 16777216,
                "sql_mode": "STRICT_TRANS_TABLES,NO_ZERO_DATE,NO_ZERO_IN_DATE,ERROR_FOR_DIVISION_BY_ZERO",
            }
        return self._db_config

    def _initialize_pool(self):
        """初始化PooledDB连接池"""
        with self._lock:
            if self._pool is None:
                config = self._get_db_config()
                try:
                    self._pool = PooledDB(
                        creator=pymysql,  # 使用pymysql作为数据库连接器
                        maxconnections=self._max_connections,  # 连接池允许的最大连接数
                        mincached=self._min_connections,  # 初始化时，连接池中至少创建的空闲连接
                        maxcached=self._max_connections,  # 连接池中最多闲置的连接
                        maxshared=3,  # 连接池中最多共享的连接数量
                        blocking=True,  # 连接池中如果没有可用连接后，是否阻塞等待
                        maxusage=None,  # 一个连接最多被重复使用的次数，None表示无限制
                        setsession=[],  # 开始会话前执行的命令列表
                        ping=0,  # ping MySQL服务端，检查是否服务可用
                        host=config["host"],
                        user=config["user"],
                        password=config["password"],
                        database=config["database"],
                        port=config["port"],
                        charset=config["charset"],
                        autocommit=config["autocommit"],
                        connect_timeout=config["connect_timeout"],
                        read_timeout=config["read_timeout"],
                        write_timeout=config["write_timeout"],
                        max_allowed_packet=config["max_allowed_packet"],
                        sql_mode=config["sql_mode"],
                    )
                    logger.info(
                        f"PooledDB连接池初始化成功: {config['host']}:{config['port']}"
                    )
                except Exception as e:
                    logger.error(f"PooledDB连接池初始化失败: {e}")
                    raise

    def get_connection(self):
        """从PooledDB连接池获取连接"""
        if self._pool is None:
            self._initialize_pool()

        try:
            # PooledDB会自动管理连接的获取和验证
            conn = self._pool.connection()
            logger.debug("从PooledDB连接池获取连接成功")
            return conn
        except Exception as e:
            logger.error(f"从PooledDB连接池获取连接失败: {e}")
            raise

    def return_connection(self, conn):
        """将连接返回到PooledDB连接池"""
        # PooledDB的连接会自动返回到池中，只需要关闭连接即可
        if conn:
            try:
                conn.close()
                logger.debug("连接已返回到PooledDB连接池")
            except Exception as e:
                logger.warning(f"返回连接到PooledDB连接池时出错: {e}")

    @contextmanager
    def get_cursor(self):
        """获取数据库游标的上下文管理器"""
        conn = None
        cursor = None
        try:
            conn = self.get_connection()
            cursor = conn.cursor()
            yield cursor
        except Exception as e:
            logger.error(f"数据库操作失败: {e}")
            raise
        finally:
            if cursor:
                try:
                    cursor.close()
                except:
                    pass
            if conn:
                self.return_connection(conn)

    def close_all(self):
        """关闭PooledDB连接池"""
        with self._lock:
            if self._pool is not None:
                try:
                    # PooledDB没有直接的close_all方法，但可以通过设置为None来清理
                    self._pool = None
                    logger.info("PooledDB连接池已关闭")
                except Exception as e:
                    logger.error(f"关闭PooledDB连接池时出错: {e}")


# 全局连接池实例
_db_pool = DatabaseConnectionPool()


def get_sql_conn():
    """
    获取数据库连接游标（改进版本，支持连接池和自动重连）
    返回上下文管理器，确保连接正确释放
    """
    return _db_pool.get_cursor()


def close_db_connection():
    """
    关闭所有数据库连接
    """
    _db_pool.close_all()


# 重试装饰器
def retry_on_db_error(max_retries=3, delay=1):
    """数据库操作重试装饰器"""

    def decorator(func):
        def wrapper(*args, **kwargs):
            last_exception = None
            for attempt in range(max_retries):
                try:
                    return func(*args, **kwargs)
                except Exception as e:
                    last_exception = e
                    error_msg = str(e).lower()
                    # 检查是否是可重试的数据库错误
                    if any(
                        keyword in error_msg
                        for keyword in [
                            "lost connection",
                            "timed out",
                            "packet sequence",
                            "connection",
                            "mysql server has gone away",
                        ]
                    ):
                        logger.warning(f"数据库操作失败，第{attempt + 1}次重试: {e}")
                        if attempt < max_retries - 1:
                            time.sleep(delay * (2**attempt))  # 指数退避
                            continue
                    # 非数据库连接错误，直接抛出
                    raise e
            # 所有重试都失败，抛出最后一个异常
            raise last_exception

        return wrapper

    return decorator
