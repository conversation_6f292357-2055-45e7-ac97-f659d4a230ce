

import base64
import hashlib
import os
import threading
import time

from loguru import logger
from watchdog.events import FileSystemEventHandler

from common.message_queue import write_message_queue
from service.s3_service import bucket_name, upload_to_s3


def update_log(log_file_path: str):
    runner_record_id = os.getenv('RUNNER_RECORD_ID', '0')
    # 获取当前日期并格式化为yyyymmdd
    current_date = time.strftime("%Y/%m/%d")
    # 拼接新的S3 key
    s3_key = f"{bucket_name}/{current_date}/{runner_record_id}/app.log"

    upload_to_s3(log_file_path, s3_key)
    return s3_key


class OutputDirHandler(FileSystemEventHandler):
    def __init__(self, directory):
        self.directory = directory
        self.last_modified_times = {}
        # 跟踪正在进行的上传任务
        self.active_uploads = set()
        self.upload_lock = threading.Lock()

    def on_modified(self, event):
        if event.is_directory:
            return

        file_path = event.src_path
        self.last_modified_times[file_path] = time.time()

        # Schedule processing with a delay
        threading.Thread(target=self.process_file_after_delay,
                         args=(file_path,)).start()

    def process_file_after_delay(self, file_path):
        # 将此上传任务添加到活跃任务集合中
        upload_id = f"{file_path}_{time.time()}"
        with self.upload_lock:
            self.active_uploads.add(upload_id)

        try:
            # Wait to ensure file writing is complete
            time.sleep(1.5)

            # Check if file was modified during wait period
            if time.time() - self.last_modified_times.get(file_path, 0) < 1.5:
                # File was modified recently, don't process yet
                return

            # 获取文件名和后缀
            file_name = os.path.basename(file_path)
            file_base, file_ext = os.path.splitext(file_name)

            # 对文件名进行MD5处理
            # 确保 file_base 是字符串类型，再进行编码操作
            if isinstance(file_base, bytes):
                md5_hash = hashlib.md5(file_base).hexdigest()
            else:
                md5_hash = hashlib.md5(file_base.encode()).hexdigest()

            runner_record_id = os.getenv('RUNNER_RECORD_ID', '0')
            # 获取当前日期并格式化为yyyymmdd
            current_date = time.strftime("%Y/%m/%d")
            # 拼接新的S3 key
            s3_key = f"{bucket_name}/{current_date}/{runner_record_id}/{md5_hash}/{file_base}{file_ext}"

            upload_to_s3(file_path, s3_key)
            content = base64.b64encode(
                str(s3_key).encode('utf-8')).decode('utf-8')
            message_json = f"""{{"type":"file","source":"logging","content":"{content}"}}"""
            write_message_queue.put(message_json)
            logger.info(message_json)
        finally:
            # 从活跃任务集合中移除此上传任务
            with self.upload_lock:
                self.active_uploads.discard(upload_id)

    def has_active_uploads(self):
        """检查是否有正在进行的上传任务"""
        with self.upload_lock:
            return len(self.active_uploads) > 0

    def wait_for_uploads_completion(self, timeout=60):
        """等待所有上传任务完成，最多等待timeout秒"""
        start_time = time.time()
        while self.has_active_uploads():
            if time.time() - start_time > timeout:
                logger.warning(
                    f"等待上传完成超时，仍有 {len(self.active_uploads)} 个上传任务未完成")
                break
            time.sleep(0.5)
        logger.info("所有文件上传任务已完成")
