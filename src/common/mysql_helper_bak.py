import json
import os
import threading
import time
from typing import Optional

import boto3
import pymysql
from botocore.exceptions import ClientError
from pymysql.connections import Connection
from pymysql.cursors import Cursor


def get_secret():
    secret_name = "spotmax-maxcloud-rds-hk-admin"
    region_name = "ap-east-1"

    aws_access_key_id = os.environ["AWS_DB_ACCESS_KEY_ID"]
    aws_secret_access_key = os.environ["AWS_DB_SECRET_ACCESS_KEY"]

    # Create a Secrets Manager client
    session = boto3.Session(aws_access_key_id=aws_access_key_id,
                            aws_secret_access_key=aws_secret_access_key)
    client = session.client(service_name='secretsmanager',
                            region_name=region_name)

    try:
        get_secret_value_response = client.get_secret_value(
            SecretId=secret_name)
    except ClientError as e:
        # For a list of exceptions thrown, see
        # https://docs.aws.amazon.com/secretsmanager/latest/apireference/API_GetSecretValue.html
        raise e

    secret = get_secret_value_response['SecretString']
    return secret


class DatabaseConnectionManager:
    """数据库连接管理器，支持连接池和自动重连"""

    def __init__(self):
        self._connection: Optional[Connection] = None
        self._cursor: Optional[Cursor] = None
        self._lock = threading.Lock()
        self._last_ping_time = 0
        self._ping_interval = 300  # 5分钟检查一次连接
        self._connection_timeout = 28800  # 8小时连接超时
        self._db_config = None

    def _get_db_config(self):
        """获取数据库配置"""
        if self._db_config is None:
            secret = json.loads(get_secret())
            database_name = os.getenv('DATABASE_NAME', 'jarvis_test')
            self._db_config = {
                'host': secret.get('host', ''),
                'user': secret.get('username', ''),
                'password': secret.get('password', ''),
                'database': database_name,
                'port': secret.get('port', 3306),
                'charset': 'utf8mb4',
                'autocommit': True,
                'connect_timeout': 60,
                'read_timeout': 30,
                'write_timeout': 30,
                # 启用连接池相关参数
                'max_allowed_packet': 16777216,
                'sql_mode': 'STRICT_TRANS_TABLES,NO_ZERO_DATE,NO_ZERO_IN_DATE,ERROR_FOR_DIVISION_BY_ZERO'
            }
        return self._db_config

    def _create_connection(self):
        """创建新的数据库连接"""
        config = self._get_db_config()
        try:
            conn = pymysql.connect(**config)
            print(f"数据库连接创建成功: {config['host']}:{config['port']}")
            return conn
        except Exception as e:
            print(f"数据库连接创建失败: {e}")
            raise

    def _is_connection_alive(self) -> bool:
        """检查连接是否存活"""
        if self._connection is None:
            return False

        try:
            # 使用ping检查连接
            self._connection.ping(reconnect=False)
            return True
        except Exception as e:
            print(f"数据库连接检查失败: {e}")
            return False

    def _ensure_connection(self):
        """确保连接可用，如果不可用则重新连接"""
        current_time = time.time()

        # 如果距离上次ping超过间隔时间，或者连接不存在，则检查连接
        if (current_time - self._last_ping_time > self._ping_interval or
            self._connection is None or
                not self._is_connection_alive()):

            print("重新建立数据库连接...")

            # 关闭旧连接
            if self._connection:
                try:
                    self._connection.close()
                except:
                    pass

            if self._cursor:
                try:
                    self._cursor.close()
                except:
                    pass

            # 创建新连接
            self._connection = self._create_connection()
            self._cursor = self._connection.cursor()
            self._last_ping_time = current_time

    def get_cursor(self) -> Cursor:
        """获取数据库游标，确保连接可用"""
        with self._lock:
            self._ensure_connection()
            # 每次都返回新的游标以避免并发问题
            return self._connection.cursor()

    def close(self):
        """关闭连接"""
        with self._lock:
            if self._cursor:
                try:
                    self._cursor.close()
                except:
                    pass
                self._cursor = None

            if self._connection:
                try:
                    self._connection.close()
                except:
                    pass
                self._connection = None


# 全局连接管理器实例
_db_manager = DatabaseConnectionManager()


def get_sql_conn():
    """
    获取数据库连接游标（改进版本，支持连接池和自动重连）
    """
    return _db_manager.get_cursor()


def close_db_connection():
    """
    关闭数据库连接
    """
    _db_manager.close()
