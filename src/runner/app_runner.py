import json
import logging
import os
import uuid
from typing import Any, Dict, List, Optional, Tuple

from automation.automation_imp import run_config
from common.redirect_root import agent_config_home_path
from parse_team_road import parse_team_road
from service.agent_service import Agent<PERSON>odel, AgentService
from service.agent_team_service import <PERSON><PERSON><PERSON><PERSON><PERSON>ode<PERSON>, AgentTeamService
from service.app_service import AppModel
from service.connector_service import Connector<PERSON>odel, ConnectorService
from service.llm_service import LLMService
from service.python_node_service import <PERSON>NodeModel, PythonNodeService
from service.runner_record_service import Runner<PERSON><PERSON>ordModel, RunnerRecordService
from service.status_enum import Runner<PERSON><PERSON>ord<PERSON>tatus
from service.task_service import TaskModel, TaskService
from service.tool_service import ToolService


class AppRunner:

    def __init__(
        self,
        agent_team_service: AgentTeamService,
        agent_service: AgentService,
        task_service: TaskService,
        runner_record_service: RunnerRecordService,
        connector_service: ConnectorService,
        llm_service: LLMService,
        python_node_service: PythonNodeService,
        tool_service: ToolService,
    ):
        self.agent_team_service = agent_team_service
        self.agent_service = agent_service
        self.task_service = task_service
        self.runner_record_service = runner_record_service
        self.connector_service = connector_service
        self.llm_service = llm_service
        self.python_node_service = python_node_service
        self.tool_service = tool_service

    # 组装app数据
    def assembly_app_data(self, app_id: int) -> Optional[
        Tuple[
            List[AgentTeamModel] | None,
            List[AgentModel] | None,
            List[TaskModel] | None,
            List[ConnectorModel] | None,
        ]
    ]:
        # 获取agent_team
        agent_teams = self.agent_team_service.get_by_app_id(app_id)
        if agent_teams is None:
            logging.error(f"Agent team not found: {app_id}")
            print(f"Agent team not found: {app_id}")

        # 获取agent
        agents = self.agent_service.get_by_app_id(app_id)
        if agents is None:
            logging.warning(f"Agent not found: {app_id}")
            print(f"Agent not found: {app_id}")

        # 获取task
        tasks = self.task_service.get_by_app_id(app_id)
        if tasks is None:
            logging.warning(f"Task not found: {app_id}")
            print(f"Task not found: {app_id}")

        # 获取connector
        connectors = self.connector_service.get_by_app_id(app_id)
        if connectors is None:
            logging.warning(f"Connector not found: {app_id}")
            print(f"Connector not found: {app_id}")

        # 获取python_node
        python_nodes = self.python_node_service.get_by_app_id(app_id)
        if python_nodes is None:
            logging.error(f"Python node not found: {app_id}")
            print(f"Python node not found: {app_id}")
        return agent_teams, agents, tasks, connectors, python_nodes

    def build_team_json_obj(
        self,
        runner_record: RunnerRecordModel,
        agent_team_model: AgentTeamModel,
        agents: List,
        tasks: List,
        request_id: str = "",
    ):
        agent_config_home_dir = agent_config_home_path / request_id
        agent_config_dir = os.path.join(
            agent_config_home_dir, str(runner_record.id), str(agent_team_model.id)
        )
        os.makedirs(agent_config_dir, exist_ok=True)
        agent_config_path = os.path.join(agent_config_dir, "agents.json")
        task_config_path = os.path.join(agent_config_dir, "tasks.json")
        python_struct_path = os.path.join(agent_config_dir, "python_struct.py")
        if agent_team_model.python_struct:
            with open(python_struct_path, "w", encoding="utf-8") as f:
                f.write(agent_team_model.python_struct)
        try:
            # 组装agent
            if agent_team_model.is_planning:
                llm_key = self.llm_service.get_llm_key_by_id(
                    agent_team_model.planning_llm_id
                )
                with open(agent_config_path, "w", encoding="utf-8") as f:
                    json.dump(
                        {
                            "agents": agents,
                            "team": {"is_planning": True, "planning_llm": llm_key},
                        },
                        f,
                        indent=2,
                        ensure_ascii=False,
                    )
            else:
                with open(agent_config_path, "w", encoding="utf-8") as f:
                    json.dump({"agents": agents}, f, indent=2, ensure_ascii=False)

            # 组装task
            if agent_team_model.python_struct:
                with open(task_config_path, "w", encoding="utf-8") as f:
                    json.dump(
                        {"tasks": tasks, "struct": "txt:python_struct.py"},
                        f,
                        indent=2,
                        ensure_ascii=False,
                    )
            else:
                with open(task_config_path, "w", encoding="utf-8") as f:
                    json.dump({"tasks": tasks}, f, indent=2, ensure_ascii=False)
            return agent_config_dir
        except Exception as e:
            logging.error(
                f"Failed to write agents configuration to {agent_config_dir}: {e}"
            )
            print(f"Failed to write agents configuration to {agent_config_dir}: {e}")
            raise e

    def get_next_node_name(self, next_node: any):
        id = next_node["id"]
        next_node_type = next_node["next_node_type"]
        if next_node_type == "agent_team":
            agent_team_model = self.agent_team_service.get_by_id(id)
            return agent_team_model.result_params_key
        elif next_node_type == "connector":
            connector_model = self.connector_service.get_by_id(id)
            return connector_model.result_params_key
        elif next_node_type == "python":
            python_node_model = self.python_node_service.get_by_id(id)
            return (
                python_node_model.result_var
                if python_node_model.result_var != ""
                else python_node_model.name
            )
        return ""

    def create_connector_node(
        self, connector: ConnectorModel, trigger_params, previous_result_key: str
    ) -> Dict[str, Any]:
        macro_list = json.loads(connector.macro_list)

        params = []
        for macro in macro_list:
            name = macro["name"]
            if name in trigger_params:
                params.append({"param": {"name": name, "value": trigger_params[name]}})
            elif previous_result_key == name:
                params.append(
                    {"param": {"name": name, "value": "{" + previous_result_key + "}"}}
                )
            else:
                params.append({"param": {"name": name, "value": f"[{name}]"}})

        router_list = []
        if connector.router:
            routers = json.loads(connector.router)
            for router in routers:
                next_node_name = self.get_next_node_name(router["next_node"])
                router_list.append(
                    {
                        "path": {
                            "path_tag": router["path_tag"],
                            "next_node": next_node_name,
                        }
                    }
                )

        return {
            "name": connector.result_params_key,
            "type": "connector",
            "config": {
                "restful_url": connector.restful_url,
                "params": params,
                "router": router_list,
            },
        }

    def create_python_node(
        self,
        runner_record: RunnerRecordModel,
        python_node_model: PythonNodeModel,
        trigger_params,
        previous_result_key,
        request_id: str,
    ):

        agent_config_home_dir = agent_config_home_path / request_id
        agent_config_dir = os.path.join(
            agent_config_home_dir,
            str(runner_record.id),
            str(python_node_model.id),
            str(uuid.uuid4()),
        )
        os.makedirs(agent_config_dir, exist_ok=True)

        python_code_path = os.path.join(agent_config_dir, "python_code.py")
        with open(python_code_path, "w", encoding="utf-8") as f:
            f.write(python_node_model.code)

        trigger_params = json.loads(runner_record.after_download_params)

        macro_list = json.loads(python_node_model.macro_list)

        params = []
        for macro in macro_list:
            name = macro["name"]
            if name in trigger_params:
                params.append({"param": {"name": name, "value": trigger_params[name]}})
            elif previous_result_key == name:
                params.append(
                    {"param": {"name": name, "value": "{" + previous_result_key + "}"}}
                )
            else:
                params.append({"param": {"name": name, "value": f"[{name}]"}})

        router_list = []
        if python_node_model.router:
            routers = json.loads(python_node_model.router)
            for router in routers:
                next_node_name = self.get_next_node_name(router["next_node"])
                router_list.append(
                    {
                        "path": {
                            "path_tag": router["path_tag"],
                            "next_node": next_node_name,
                        }
                    }
                )
        python_node_name = (
            python_node_model.result_var
            if python_node_model.result_var != ""
            else python_node_model.name
        )
        return {
            "name": python_node_name,
            "type": "python",
            "config": {
                "code_file_path": python_code_path,
                "result_var": python_node_model.result_var,
                "path_tag_var": python_node_model.path_tag_var,
                "params": params,
                "router": router_list,
            },
        }

    def create_agentteam_node(
        self,
        sort_nodes_id,
        runner_record,
        agent_team_id_map,
        task_agentteam_id_map,
        agent_agentteam_id_map,
        taskmodels_agentteam_id_map,
        trigger_params,
        previous_result_key: str,
        request_id: str = "",
    ) -> Dict[str, Any] | None:
        if sort_nodes_id not in agent_team_id_map:
            logging.info(f"Agent team id:{sort_nodes_id} is duplicate")
            return None

        agent_team_model = agent_team_id_map[sort_nodes_id]
        logging.info(
            f"Running agent team: id:{agent_team_model.id} name:{agent_team_model.name}"
        )
        print(
            f"Running agent team: id:{agent_team_model.id} name:{agent_team_model.name}"
        )

        # 安全检查，确保sort_nodes_id存在于task_agentteam_id_map中
        if sort_nodes_id not in task_agentteam_id_map:
            logging.error(f"Task for agent team id:{sort_nodes_id} not found")
            print(f"Task for agent team id:{sort_nodes_id} not found")
            return None

        tasks = task_agentteam_id_map[sort_nodes_id]
        task_sort = json.loads(agent_team_model.task_sort)
        if task_sort:
            task_id_map = {str(task["id"]): task for task in tasks}
            # 安全检查，只添加存在于task_id_map中的任务
            sorted_tasks = []
            for i in task_sort:
                if i in task_id_map:
                    sorted_tasks.append(task_id_map[i])
            tasks = sorted_tasks

        # 安全检查，确保sort_nodes_id存在于agent_agentteam_id_map中
        if sort_nodes_id not in agent_agentteam_id_map:
            logging.error(f"Agent for agent team id:{sort_nodes_id} not found")
            print(f"Agent for agent team id:{sort_nodes_id} not found")
            return None

        agents = agent_agentteam_id_map[sort_nodes_id]

        # 安全检查，确保sort_nodes_id存在于taskmodels_agentteam_id_map中
        if sort_nodes_id not in taskmodels_agentteam_id_map:
            logging.error(f"Task model for agent team id:{sort_nodes_id} not found")
            print(f"Task model for agent team id:{sort_nodes_id} not found")
            return None

        task_models = taskmodels_agentteam_id_map[sort_nodes_id]

        macro_list = []
        for task_model in task_models:
            macros = json.loads(task_model.macro_list)
            macro_list.extend(macros)

        agent_team_json_path = self.build_team_json_obj(
            runner_record, agent_team_model, agents, tasks, request_id
        )

        params = []
        for macro in macro_list:
            name = macro["name"]
            if name in trigger_params:
                params.append({"param": {"name": name, "value": trigger_params[name]}})
            elif previous_result_key == name:
                params.append(
                    {"param": {"name": name, "value": "{" + previous_result_key + "}"}}
                )
            else:
                params.append({"param": {"name": name, "value": name}})

        return {
            "name": agent_team_model.result_params_key,
            "type": "agent",
            "config": {"config_path": agent_team_json_path, "params": params},
        }

    def run(
        self,
        runner_record: RunnerRecordModel,
        app: AppModel,
        request_id: str = "",
        task_callback: Optional[Any] = None,
        step_callback: Optional[Any] = None,
    ):
        result = self.assembly_app_data(runner_record.app_id)
        if result is None:
            return
        agent_team_models, agent_models, task_models, connector_models, python_nodes = (
            result
        )

        if (
            agent_team_models is None or agent_models is None or task_models is None
        ) and (connector_models is None and python_nodes is None):
            logging.error(
                f"agent_team_models and connector_models python_nodes is empty: {runner_record.app_id}"
            )
            return
        agent_config_home_dir = agent_config_home_path / request_id
        automation_dir = os.path.join(agent_config_home_dir, str(runner_record.id))
        os.makedirs(automation_dir, exist_ok=True)

        agent_id_map = {}
        agent_agentteam_id_map = {}
        for agent_model in agent_models or []:
            agent_config_dir = os.path.join(
                agent_config_home_dir,
                str(runner_record.id),
                str(agent_model.agent_team_id),
            )
            os.makedirs(agent_config_dir, exist_ok=True)
            # agent = self.agent_service.create_llm_agent(agent_model)
            agent = self.agent_service.build_agent_json_obj(
                agent_model, agent_config_dir
            )
            agent_id_map[agent_model.id] = agent
            if agent_model.agent_team_id not in agent_agentteam_id_map:
                agent_agentteam_id_map[agent_model.agent_team_id] = []
            if not any(
                existing_agent["id"] == agent["id"]
                for existing_agent in agent_agentteam_id_map[agent_model.agent_team_id]
            ):
                agent_agentteam_id_map[agent_model.agent_team_id].append(agent)

        task_agentteam_id_map = {}
        taskmodels_agentteam_id_map = {}
        for task_model in task_models or []:
            # task = self.task_service.create_llm_task(
            #     task_model, agent_id_map[task_model.agent_id])
            agent_config_dir = os.path.join(
                agent_config_home_dir,
                str(runner_record.id),
                str(task_model.agent_team_id),
            )
            os.makedirs(agent_config_dir, exist_ok=True)

            task = self.task_service.build_task_json_obj(
                task_model, agent_id_map[task_model.agent_id], agent_config_dir
            )
            if task_model.agent_team_id not in taskmodels_agentteam_id_map:
                taskmodels_agentteam_id_map[task_model.agent_team_id] = []
            if not any(
                existing_task.id == task_model.id
                for existing_task in taskmodels_agentteam_id_map[
                    task_model.agent_team_id
                ]
            ):
                taskmodels_agentteam_id_map[task_model.agent_team_id].append(task_model)

            if task_model.agent_team_id not in task_agentteam_id_map:
                task_agentteam_id_map[task_model.agent_team_id] = []
            if not any(
                existing_task["id"] == task["id"]
                for existing_task in task_agentteam_id_map[task_model.agent_team_id]
            ):
                task_agentteam_id_map[task_model.agent_team_id].append(task)

        agent_team_id_map = {}
        for agent_team_model in agent_team_models or []:
            agent_team_id_map[agent_team_model.id] = agent_team_model

        connector_id_map = {}
        for connector_model in connector_models or []:
            connector_id_map[connector_model.id] = connector_model

        python_node_id_map = {}
        for python_node_model in python_nodes or []:
            python_node_id_map[python_node_model.id] = python_node_model

        # self.build_automation_json_obj(runner_record, app)

        trigger_params = json.loads(runner_record.after_download_params)
        # trigger_params = json.loads(runner_record.trigger_params)

        # sort json demo: /Users/<USER>/workspace/Jarvis/jarvis/src/app_sort.json
        app_sort = json.loads(app.sort)
        version = app_sort["version"]
        if version != "v1":
            logging.error(f"App sort version unsupported: {version}")
            print(f"App sort version unsupported: {version}")
            self.runner_record_service.update_status(
                runner_record.id,
                RunnerRecordStatus.FAILED.value,
                "app sort version unsupported",
            )
            return

        sort_nodes, order, desc_order = parse_team_road(app_sort)

        nodes = []
        # previous_result_key = ""
        id_name = {}
        for sort_node in sort_nodes:
            sort_nodes_id = int(sort_node["data"]["id"])
            node_type = sort_node["type"]
            if node_type == "connector":
                connector = connector_id_map[sort_nodes_id]

                sort_nodes_id_str = str(sort_nodes_id)
                previous_result_key = ""
                if sort_nodes_id_str in desc_order:
                    previous_node_id = desc_order[sort_nodes_id_str]
                    if previous_node_id in id_name:
                        previous_result_key = id_name[previous_node_id]

                node = self.create_connector_node(
                    connector, trigger_params, previous_result_key
                )

                node["id"] = sort_nodes_id_str
                id_name[sort_nodes_id_str] = node["name"]

                nodes.append(node)
                continue
            if node_type == "python":
                python_node = python_node_id_map[sort_nodes_id]
                sort_nodes_id_str = str(sort_nodes_id)

                previous_result_key = ""
                if sort_nodes_id_str in desc_order:
                    previous_node_id = desc_order[sort_nodes_id_str]
                    if previous_node_id in id_name:
                        previous_result_key = id_name[previous_node_id]

                node = self.create_python_node(
                    runner_record,
                    python_node,
                    trigger_params,
                    previous_result_key,
                    request_id,
                )

                node["id"] = sort_nodes_id_str
                id_name[sort_nodes_id_str] = node["name"]

                nodes.append(node)
                continue
            if node_type == "team":
                previous_result_key = ""

                sort_nodes_id_str = str(sort_nodes_id)

                previous_result_key = ""
                if sort_nodes_id_str in desc_order:
                    previous_node_id = desc_order[sort_nodes_id_str]
                    if previous_node_id in id_name:
                        previous_result_key = id_name[previous_node_id]

                node = self.create_agentteam_node(
                    sort_nodes_id,
                    runner_record,
                    agent_team_id_map,
                    task_agentteam_id_map,
                    agent_agentteam_id_map,
                    taskmodels_agentteam_id_map,
                    trigger_params,
                    previous_result_key,
                    request_id,
                )
                if node is None:
                    continue

                node["id"] = sort_nodes_id_str
                id_name[sort_nodes_id_str] = node["name"]
                nodes.append(node)
                continue

        # 设置next_node
        for node in nodes:
            if str(node["id"]) in order:
                next_node_id = order[node["id"]]
                name = id_name[next_node_id]
                node["next_node"] = name
            del node["id"]

        automation_json = {"nodes": nodes}
        automation_config_path = os.path.join(automation_dir, "automation.json")
        with open(automation_config_path, "w", encoding="utf-8") as f:
            json.dump(automation_json, f, indent=2, ensure_ascii=False)
        # 执行automation
        result = run_config(
            str(runner_record.create_user_id),
            automation_config_path,
            trigger_params,
            is_multi_turn=runner_record.is_multi_turn,
            task_callback=task_callback,
            step_callback=step_callback,
            tool_service=self.tool_service,
        )
        # 更新状态为completed
        if not self.runner_record_service.update_status(
            runner_record.id, RunnerRecordStatus.SUCCESS.value, "success"
        ):
            logging.error(
                f"Failed to update runner record status id:{runner_record.id} status: {RunnerRecordStatus.SUCCESS.value}"
            )
            print(
                f"Failed to update runner record status id:{runner_record.id} status: {RunnerRecordStatus.SUCCESS.value}"
            )
            return result
        return result
