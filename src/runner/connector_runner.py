import json
import logging
import os

from automation.automation_imp import run_config
from common.redirect_root import agent_config_home_path
from service.connector_service import Connector<PERSON><PERSON><PERSON>, ConnectorService
from service.runner_record_service import Runner<PERSON><PERSON>ordModel, RunnerRecordService
from service.status_enum import RunnerR<PERSON>ord<PERSON>tatus
from service.tool_service import ToolService


class ConnectorRunner:

    def __init__(
        self,
        connector_service: ConnectorService,
        runner_record_service: RunnerRecordService,
        tool_service: ToolService,
    ):
        self.connector_service = connector_service
        self.runner_record_service = runner_record_service
        self.tool_service = tool_service

    # 组装app数据
    def assembly_data(self, connector_id: int) -> ConnectorModel | None:
        # 获取connector
        connectors = self.connector_service.get_by_id(connector_id)
        if connectors is None:
            logging.error(f"Connector not found: {connector_id}")
            print(f"Connector not found: {connector_id}")
            return None

        return connectors

    def run(self, runner_record: <PERSON>RecordModel, request_id: str = ""):
        connector_model = self.assembly_data(runner_record.connector_id)
        if connector_model is None:
            logging.error(f"Connector not found: {runner_record.connector_id}")
            print(f"Connector not found: {runner_record.connector_id}")
            return

        try:
            automation_config_path = self.build_connector_json_obj(
                runner_record=runner_record,
                connector_model=connector_model,
                request_id=request_id,
            )
            trigger_params = json.loads(runner_record.after_download_params)
            result = run_config(
                str(runner_record.create_user_id),
                automation_config_path,
                trigger_params,
                is_multi_turn=runner_record.is_multi_turn,
                tool_service=self.tool_service,
            )
            # 更新状态为completed
            if not self.runner_record_service.update_status(
                runner_record.id, RunnerRecordStatus.SUCCESS.value, "success"
            ):
                logging.error(
                    f"Failed to update runner record status id:{runner_record.id} status: {RunnerRecordStatus.SUCCESS.value}"
                )
                print(
                    f"Failed to update runner record status id:{runner_record.id} status: {RunnerRecordStatus.SUCCESS.value}"
                )
            return result
        except Exception as e:
            logging.error(f"Failed to run connector: {e}")
            print(f"Failed to run connector: {e}")
            raise e

    def build_connector_json_obj(
        self,
        runner_record: RunnerRecordModel,
        connector_model: ConnectorModel,
        request_id: str = "",
    ):
        agent_config_home_dir = agent_config_home_path / request_id
        agent_config_dir = os.path.join(
            agent_config_home_dir, str(runner_record.id), str(connector_model.id)
        )
        os.makedirs(agent_config_dir, exist_ok=True)

        automation_config_path = os.path.join(agent_config_dir, "automation.json")

        # trigger_params = json.loads(runner_record.trigger_params)
        trigger_params = json.loads(runner_record.after_download_params)

        macro_list = json.loads(connector_model.macro_list)

        params = []
        for macro in macro_list:
            name = macro["name"]
            if name not in trigger_params:
                params.append({"param": {"name": name, "value": f"[{name}]"}})
            else:
                params.append({"param": {"name": name, "value": trigger_params[name]}})

        try:
            automation_json = {
                "nodes": [
                    {
                        "name": connector_model.result_params_key,
                        "type": "connector",
                        "config": {
                            "restful_url": connector_model.restful_url,
                            "params": params,
                        },
                    }
                ]
            }
            with open(automation_config_path, "w", encoding="utf-8") as f:
                json.dump(automation_json, f, indent=2, ensure_ascii=False)
        except Exception as e:
            logging.error(
                f"Failed to write automation configuration to {automation_config_path}: {e}"
            )
            raise e

        return automation_config_path

    #     {
    # "nodes": [
    #   {
    #     "type": "connector",
    #     "config": {
    #       "restful_url": "http://127.0.0.1:5100/get_next_mail",
    #       "params": [
    #         {
    #           "param": {
    #             "name": "search_criteria",
    #             "value": "SINCE \"01-Apr-2023\""
    #           }
    #         }
    #       ]
    #     }
    #   }
    #     }
