import json
import logging
import os

from automation.automation_imp import run_config
from common.redirect_root import agent_config_home_path
from service.python_node_service import PythonNodeMode<PERSON>, PythonNodeService
from service.runner_record_service import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, RunnerRecordService
from service.status_enum import RunnerRecordStatus
from service.tool_service import ToolService


class PythonRunner:
    def __init__(
        self,
        python_node_service: PythonNodeService,
        runner_record_service: RunnerRecordService,
        tool_service: ToolService,
    ):
        self.python_node_service = python_node_service
        self.runner_record_service = runner_record_service
        self.tool_service = tool_service

    def run(self, runner_record: RunnerRecordModel, request_id: str = ""):
        python_node_model = self.python_node_service.get_by_id(
            runner_record.python_node_id
        )
        if python_node_model is None:
            logging.error(f"Python node not found: {runner_record.python_node_id}")
            print(f"Python node not found: {runner_record.python_node_id}")
            self.runner_record_service.update_status(
                runner_record.id, RunnerRecordStatus.FAILED.value
            )
            return

        try:
            automation_config_path = self.build_python_json_obj(
                runner_record=runner_record,
                python_node_model=python_node_model,
                request_id=request_id,
            )
            trigger_params = json.loads(runner_record.after_download_params)
            result = run_config(
                str(runner_record.create_user_id),
                automation_config_path,
                trigger_params,
                is_multi_turn=runner_record.is_multi_turn,
                tool_service=self.tool_service
            )
            # 更新状态为completed
            if not self.runner_record_service.update_status(
                runner_record.id, RunnerRecordStatus.SUCCESS.value, "success"
            ):
                logging.error(
                    f"Failed to update runner record status id:{runner_record.id} status: {RunnerRecordStatus.SUCCESS.value}"
                )
                print(
                    f"Failed to update runner record status id:{runner_record.id} status: {RunnerRecordStatus.SUCCESS.value}"
                )
            return result
        except Exception as e:
            logging.error(f"Failed to run connector: {e}")
            print(f"Failed to run connector: {e}")
            raise e

    def build_python_json_obj(
        self,
        runner_record: RunnerRecordModel,
        python_node_model: PythonNodeModel,
        request_id: str = "",
    ):
        agent_config_home_dir = agent_config_home_path / request_id
        agent_config_dir = os.path.join(
            agent_config_home_dir, str(runner_record.id), str(python_node_model.id)
        )
        os.makedirs(agent_config_dir, exist_ok=True)

        automation_config_path = os.path.join(agent_config_dir, "automation.json")

        python_code_path = os.path.join(agent_config_dir, "python_code.py")
        with open(python_code_path, "w", encoding="utf-8") as f:
            f.write(python_node_model.code)

        trigger_params = json.loads(runner_record.after_download_params)

        macro_list = json.loads(python_node_model.macro_list)

        params = []
        for macro in macro_list:
            name = macro["name"]
            if name not in trigger_params:
                params.append({"param": {"name": name, "value": f"[{name}]"}})
            else:
                params.append({"param": {"name": name, "value": trigger_params[name]}})

        try:
            automation_json = {
                "nodes": [
                    {
                        "name": python_node_model.result_var,
                        "type": "python",
                        "config": {
                            "code_file_path": python_code_path,
                            "result_var": python_node_model.result_var,
                            "path_tag_var": python_node_model.path_tag_var,
                            "params": params,
                        },
                    }
                ]
            }
            with open(automation_config_path, "w", encoding="utf-8") as f:
                json.dump(automation_json, f, indent=2, ensure_ascii=False)
        except Exception as e:
            logging.error(
                f"Failed to write automation configuration to {automation_config_path}: {e}"
            )
            raise e

        return automation_config_path
