import json
import logging
import os
from typing import Any, Optional

from common.redirect_root import agent_config_home_path
from runner.agent_team_runner import Agent<PERSON><PERSON><PERSON><PERSON><PERSON>ner
from runner.app_runner import AppRunner
from runner.connector_runner import Connector<PERSON>unner
from runner.python_node_runner import <PERSON><PERSON><PERSON><PERSON>
from service.agent_service import Agent<PERSON>ervice
from service.agent_team_service import AgentTeamService
from service.app_service import AppService
from service.connector_service import ConnectorService
from service.llm_service import LLMService
from service.python_node_service import PythonNodeService
from service.runner_record_service import Runner<PERSON><PERSON>ordModel, RunnerRecordService
from service.s3_service import download_from_s3
from service.status_enum import Runner<PERSON><PERSON>ord<PERSON>tatus, RunnerType
from service.task_service import TaskService
from service.tool_service import ToolService


class Runner:

    def __init__(
        self,
        app_service: AppService,
        agent_team_service: AgentTeamService,
        llm_service: LLMService,
        agent_service: AgentService,
        task_service: TaskService,
        tool_service: ToolService,
        runner_record_service: RunnerRecordService,
        connector_service: Connector<PERSON>ervice,
        python_node_service: PythonNodeService,
    ):
        self.app_service = app_service
        self.agent_team_service = agent_team_service
        self.llm_service = llm_service
        self.agent_service = agent_service
        self.task_service = task_service
        self.tool_service = tool_service
        self.runner_record_service = runner_record_service
        self.connector_service = connector_service
        self.python_node_service = python_node_service

    def run(
        self,
        runner_record_id: int,
        request_id: str = "",
        task_callback: Optional[Any] = None,
        step_callback: Optional[Any] = None,
    ):

        logging.info(f"Start running id: {runner_record_id}")
        print(f"Start running id: {runner_record_id}")
        # 获取要执行的记录
        runner_record = self.runner_record_service.get_by_id(runner_record_id)
        if runner_record is None:
            logging.error(f"Runner record not found: {runner_record_id}")
            print(f"Runner record not found: {runner_record_id}")
            self.runner_record_service.update_status(
                runner_record_id,
                RunnerRecordStatus.FAILED.value,
                f"runner record not found: {runner_record_id}",
            )
            return

        logging.info(f"Runner record : {runner_record}")

        # 执行参数 trigger_params 中如果有类型为file的参数，需要下载文件
        self.download_file_params(runner_record=runner_record, request_id=request_id)

        if os.getenv("DEBUG", "false") != "true":
            # 检查状态是否是init # todo 测试结束 打开后面注释
            if not RunnerRecordStatus.INIT.equals(runner_record.execute_status):
                logging.error(
                    f"Runner record id:{runner_record_id} status is not init: {runner_record.execute_status}"
                )
                print(
                    f"Runner record id:{runner_record_id} status is not init: {runner_record.execute_status}"
                )
                return

        # 更新状态为running
        self.runner_record_service.update_status(
            runner_record.id, RunnerRecordStatus.RUNNING.value, ""
        )

        # 目前支持运行app和agent_team
        if RunnerType.is_data_process(runner_record.runner_type):
            logging.error(
                f"Runner record runner_type is not app or agent_team: {runner_record_id}"
            )
            print(
                f"Runner record runner_type is not app or agent_team: {runner_record_id}"
            )
            self.runner_record_service.update_status(
                runner_record_id,
                RunnerRecordStatus.FAILED.value,
                f"runner record runner_type is not app or agent_team: {runner_record_id}",
            )
            return

        # 运行app
        result = self.run_app(
            runner_record=runner_record,
            request_id=request_id,
            task_callback=task_callback,
            step_callback=step_callback,
        )
        return result

    def run_record(
        self,
        runner_record: RunnerRecordModel = None,
        request_id: str = "",
        task_callback: Optional[Any] = None,
        step_callback: Optional[Any] = None,
    ):
        runner_record_id = int(runner_record.id)

        # 执行参数 trigger_params 中如果有类型为file的参数，需要下载文件
        self.download_file_params(runner_record=runner_record, request_id=request_id)

        if os.getenv("DEBUG", "false") != "true":
            # 检查状态是否是init # todo 测试结束 打开后面注释
            if not RunnerRecordStatus.INIT.equals(runner_record.execute_status):
                logging.error(
                    f"Runner record id:{runner_record_id} status is not init: {runner_record.execute_status}"
                )
                print(
                    f"Runner record id:{runner_record_id} status is not init: {runner_record.execute_status}"
                )
                return

        # 更新状态为running
        self.runner_record_service.update_status(
            runner_record.id, RunnerRecordStatus.RUNNING.value, ""
        )

        # 目前支持运行app和agent_team
        if RunnerType.is_data_process(runner_record.runner_type):
            logging.error(
                f"Runner record runner_type is not app or agent_team: {runner_record_id}"
            )
            print(
                f"Runner record runner_type is not app or agent_team: {runner_record_id}"
            )
            self.runner_record_service.update_status(
                runner_record_id,
                RunnerRecordStatus.FAILED.value,
                f"runner record runner_type is not app or agent_team: {runner_record_id}",
            )
            return

        # 运行app
        result = self.run_app(
            runner_record=runner_record,
            request_id=request_id,
            task_callback=task_callback,
            step_callback=step_callback,
        )
        return result

    # 运行app
    def run_app(
        self,
        runner_record: RunnerRecordModel,
        request_id: str = "",
        task_callback: Optional[Any] = None,
        step_callback: Optional[Any] = None,
    ):
        # 运行app
        if RunnerType.APP.equals(runner_record.runner_type):
            app_runner = AppRunner(
                agent_team_service=self.agent_team_service,
                agent_service=self.agent_service,
                task_service=self.task_service,
                runner_record_service=self.runner_record_service,
                connector_service=self.connector_service,
                llm_service=self.llm_service,
                python_node_service=self.python_node_service,
                tool_service=self.tool_service,
            )
            # 获取app
            app = self.app_service.get_by_id(runner_record.app_id)
            if app is None:
                logging.error(f"App not found: {runner_record.app_id}")
                print(f"App not found: {runner_record.app_id}")
                self.runner_record_service.update_status(
                    runner_record.id,
                    RunnerRecordStatus.FAILED.value,
                    f"app not found: {runner_record.app_id}",
                )
                return
            result = app_runner.run(
                runner_record=runner_record,
                app=app,
                request_id=request_id,
                task_callback=task_callback,
                step_callback=step_callback,
            )
            return result
        elif RunnerType.AGENT_TEAM.equals(runner_record.runner_type):
            agent_team_runner = AgentTeamRunner(
                agent_team_service=self.agent_team_service,
                agent_service=self.agent_service,
                task_service=self.task_service,
                runner_record_service=self.runner_record_service,
                llm_service=self.llm_service,
                tool_service=self.tool_service,
            )
            result = agent_team_runner.run(
                runner_record=runner_record,
                request_id=request_id,
                task_callback=task_callback,
                step_callback=step_callback,
            )
            return result
        elif RunnerType.CONNECTOR.equals(runner_record.runner_type):
            connector_runner = ConnectorRunner(
                connector_service=self.connector_service,
                runner_record_service=self.runner_record_service,
                tool_service=self.tool_service,
            )
            result = connector_runner.run(
                runner_record=runner_record, request_id=request_id
            )
            return result
        elif RunnerType.PYTHON.equals(runner_record.runner_type):
            python_runner = PythonRunner(
                python_node_service=self.python_node_service,
                runner_record_service=self.runner_record_service,
                tool_service=self.tool_service,
            )
            result = python_runner.run(
                runner_record=runner_record, request_id=request_id
            )
            return result
        else:
            logging.error(
                f"Runner record runner_type is not app or agent_team: {runner_record.runner_type}"
            )
            print(
                f"Runner record runner_type is not app or agent_team: {runner_record.runner_type}"
            )
            self.runner_record_service.update_status(
                runner_record.id,
                RunnerRecordStatus.FAILED.value,
                "runner type is not app or agent_team",
            )
            return None

    def download_file_params(
        self, runner_record: RunnerRecordModel, request_id: str = ""
    ):
        if (
            runner_record.trigger_params is None
            or len(runner_record.trigger_params) == 0
        ):
            runner_record.after_download_params = runner_record.trigger_params
            return

        trigger_params_json = json.loads(runner_record.trigger_params)

        if type(trigger_params_json) is dict:
            runner_record.after_download_params = runner_record.trigger_params
            return

        agent_config_home_dir = agent_config_home_path / request_id

        agent_config_dir = os.path.join(agent_config_home_dir, str(runner_record.id))
        after_download_params = {}
        os.makedirs(agent_config_dir, exist_ok=True)

        if type(trigger_params_json) is list:
            for param in trigger_params_json:
                if (
                    not param.get("type")
                    or not param.get("value")
                    or not param.get("name")
                ):
                    continue
                name = param.get("name")
                param_type = param.get("type")
                value = param.get("value")
                if param_type == "string":
                    after_download_params[name] = value
                if param_type == "file":
                    full_path = download_from_s3(value, agent_config_dir)
                    after_download_params[name] = full_path

        if hasattr(runner_record, "historical_context"):
            after_download_params["historical_context"] = (
                runner_record.historical_context
            )
        runner_record.after_download_params = json.dumps(
            after_download_params, ensure_ascii=False
        )
