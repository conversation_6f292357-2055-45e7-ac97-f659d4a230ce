import json
import logging
import os
from typing import Any, List, Optional, Tuple

from automation.automation_imp import run_config
from common.redirect_root import agent_config_home_path
from service.agent_service import Agent<PERSON><PERSON><PERSON>, AgentService
from service.agent_team_service import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, AgentTeamService
from service.llm_service import LLMService
from service.runner_record_service import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, RunnerRecordService
from service.status_enum import RunnerRecordStatus
from service.task_service import TaskModel, TaskService
from service.tool_service import ToolService


class AgentTeamRunner:

    def __init__(
        self,
        agent_team_service: AgentTeamService,
        agent_service: AgentService,
        task_service: TaskService,
        runner_record_service: RunnerRecordService,
        llm_service: LLMService,
        tool_service: ToolService,
    ):
        self.agent_team_service = agent_team_service
        self.agent_service = agent_service
        self.task_service = task_service
        self.runner_record_service = runner_record_service
        self.llm_service = llm_service
        self.tool_service = tool_service

    # 组装agent_team数据
    def assembly_agent_team_data(
        self, app_id: int, agent_team_id: int
    ) -> (
        Optional[Tuple[AgentTeamModel, List[TaskModel], List[AgentModel]]]
        | Tuple[None, None, None]
    ):
        # 获取agent_team
        agent_team = self.agent_team_service.get_by_id(agent_team_id)
        if agent_team is None:
            logging.error(f"Agent team not found: {agent_team_id}")
            print(f"Agent team not found: {agent_team_id}")
            return None, None, None

        # 获取agent
        agents = self.agent_service.get_by_app_agent_team(app_id, agent_team_id)
        if agents is None:
            logging.error(f"Agent not found: {agent_team_id}")
            print(f"Agent not found: {agent_team_id}")
            return None, None, None

        # 获取task
        tasks = self.task_service.get_by_app_agent_team(app_id, agent_team_id)
        if tasks is None:
            logging.error(f"Task not found: {agent_team_id}")
            print(f"Task not found: {agent_team_id}")
            return None, None, None

        return agent_team, tasks, agents

    def build_team_json_obj(
        self,
        runner_record: RunnerRecordModel,
        agent_team_model: AgentTeamModel,
        agents: List,
        tasks: List,
        macro_list: List,
        automation_dir: str,
        agent_config_dir: str,
    ):

        agent_config_path = os.path.join(agent_config_dir, "agents.json")
        task_config_path = os.path.join(agent_config_dir, "tasks.json")
        python_struct_path = os.path.join(agent_config_dir, "python_struct.py")
        if agent_team_model.python_struct:
            with open(python_struct_path, "w", encoding="utf-8") as f:
                f.write(agent_team_model.python_struct)

        try:
            # 组装agent
            if agent_team_model.is_planning:
                llm_key = self.llm_service.get_llm_key_by_id(
                    agent_team_model.planning_llm_id
                )
                with open(agent_config_path, "w", encoding="utf-8") as f:
                    json.dump(
                        {
                            "agents": agents,
                            "team": {"is_planning": True, "planning_llm": llm_key},
                        },
                        f,
                        indent=2,
                        ensure_ascii=False,
                    )
            else:
                with open(agent_config_path, "w", encoding="utf-8") as f:
                    json.dump({"agents": agents}, f, indent=2, ensure_ascii=False)
            # 组装task
            if agent_team_model.python_struct:
                with open(task_config_path, "w", encoding="utf-8") as f:
                    json.dump(
                        {"tasks": tasks, "struct": "txt:python_struct.py"},
                        f,
                        indent=2,
                        ensure_ascii=False,
                    )
            else:
                with open(task_config_path, "w", encoding="utf-8") as f:
                    json.dump({"tasks": tasks}, f, indent=2, ensure_ascii=False)

            # trigger_params = json.loads(runner_record.trigger_params)
            trigger_params = json.loads(runner_record.after_download_params)

            params = []
            for macro in macro_list:
                name = macro["name"]
                if name not in trigger_params:
                    params.append({"param": {"name": name, "value": f"[{name}]"}})
                else:
                    params.append(
                        {"param": {"name": name, "value": trigger_params[name]}}
                    )

            name = agent_team_model.result_params_key
            if not name:
                name = agent_team_model.name

            automation_json = {
                "nodes": [
                    {
                        "name": name,
                        "type": "agent",
                        "config": {"config_path": agent_config_dir, "params": params},
                    }
                ]
            }
            automation_config_path = os.path.join(automation_dir, "automation.json")
            with open(automation_config_path, "w", encoding="utf-8") as f:
                json.dump(automation_json, f, indent=2, ensure_ascii=False)
            return automation_config_path
        except Exception as e:
            logging.error(
                f"Failed to write agents configuration to {automation_dir}: {e}"
            )
            print(f"Failed to write agents configuration to {automation_dir}: {e}")
            raise e

    def run(
        self,
        runner_record: RunnerRecordModel,
        request_id: str = "",
        task_callback: Optional[Any] = None,
        step_callback: Optional[Any] = None,
    ):
        result = self.assembly_agent_team_data(
            runner_record.app_id, runner_record.agent_team_id
        )
        if result is None:
            return
        agent_team_model, task_models, agent_models = result
        if agent_team_model is None or task_models is None or agent_models is None:
            return

        agent_config_home_dir = agent_config_home_path / request_id
        automation_dir = os.path.join(agent_config_home_dir, str(runner_record.id))
        agent_config_dir = os.path.join(
            agent_config_home_dir, str(runner_record.id), str(agent_team_model.id)
        )
        os.makedirs(agent_config_dir, exist_ok=True)

        agent_id_map = {}
        agents = []
        for agent_model in agent_models:
            # agent = self.agent_service.create_llm_agent(agent_model)
            agent = self.agent_service.build_agent_json_obj(
                agent_model, agent_config_dir
            )
            agent_id_map[agent_model.id] = agent
            agents.append(agent)

        tasks = []
        macro_list = []
        task_sort = json.loads(agent_team_model.task_sort)

        # 创建task_id到task_model的映射
        task_model_map = {str(task_model.id): task_model for task_model in task_models}
        # 兼容有时候task_sort为空的情况
        if (len(task_sort) == 0 or task_sort is None) and len(task_model_map) == 1:
            task_sort = list(task_model_map.keys())

        for task_id in task_sort:
            if task_id not in task_model_map:
                logging.warning(
                    f"Task ID {task_id} in task_sort not found in task_models"
                )
                continue

            task_model = task_model_map[task_id]
            macros = json.loads(task_model.macro_list)
            macro_list.extend(macros)
            task = self.task_service.build_task_json_obj(
                task_model, agent_id_map[task_model.agent_id], agent_config_dir
            )
            tasks.append(task)

        automation_config_path = self.build_team_json_obj(
            runner_record,
            agent_team_model,
            agents,
            tasks,
            macro_list,
            automation_dir,
            agent_config_dir,
        )

        trigger_params = json.loads(runner_record.after_download_params)
        if runner_record.allow_tasks:
            trigger_params["allow_tasks"] = json.loads(runner_record.allow_tasks)
        result = run_config(
            str(runner_record.create_user_id),
            automation_config_path,
            trigger_params,
            is_multi_turn=runner_record.is_multi_turn,
            task_callback=task_callback,
            step_callback=step_callback,
            tool_service=self.tool_service,
        )

        # 更新状态为completed
        if not self.runner_record_service.update_status(
            runner_record.id, RunnerRecordStatus.SUCCESS.value, "success"
        ):
            logging.error(
                f"Failed to update runner record status id:{runner_record.id} status: {RunnerRecordStatus.SUCCESS.value}"
            )
            print(
                f"Failed to update runner record status id:{runner_record.id} status: {RunnerRecordStatus.SUCCESS.value}"
            )
        return result
