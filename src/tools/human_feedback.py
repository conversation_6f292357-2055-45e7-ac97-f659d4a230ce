from crewai.tools import tool

@tool("user feedback collecting tool")
def collect_user_feedback(content_need_to_get_user_feedback: str) -> str:
    """
    Ask for the feedback on your work result. 
    The input is the content you want to send to the user for feedback and its format is a plain text string (not json).
    """
    res = input(f"{content_need_to_get_user_feedback}:")
    return "user's feedback: " + res 
