import requests
from crewai.tools import tool

# 添加全局变量
# BASE_URL = "http://localhost:8080/api/tools"
BASE_URL = "http://**************:8080/api/tools"
# AUTH_TOKEN = os.getenv('AI_GENERAL_TOKEN', 'default-token-here')  # 设置您的认证token
AUTH_TOKEN = "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJ1c2VyX2lkIjoxMjMsInVzZXJuYW1lIjoiam9obl9kb2UiLCJleHAiOjE3MTU1MTg2NDV9.Y0EDUCZJ7C1S3kg09exjh_fCgDbpzYF3qJOGu9Ck9Hc"


def finddefinition(path: str) -> str:
    url = f"{BASE_URL}/find-definition"
    headers = {
        "Content-Type": "application/json",
        "Authorization": f"Bearer {AUTH_TOKEN}"  # 添加认证头
    }
    data = {
        "input": path
    }

    try:
        response = requests.post(url, headers=headers, json=data)
        response.raise_for_status()  # 检查响应状态
        result = response.json()

        if "error" in result and result["error"]:
            return f"Error: {result['error']}"

        return result.get("result", "")
    except requests.exceptions.RequestException as e:
        return f"Request failed: {str(e)}"


@tool("finddefinition")
def definition(question: str) -> str:
    """
    Useful for investigate the variable definition or investigate the function implementation at span, the span is the position where it's called. 
        The input to this tool is span. the output is one or more spans with code snippet. 
        if error with keyword "not an identifier", means span is invalid, should call "FindSpansAtLine" tool to read all valid spans and choose valid span to call finddefinition tool.
        if some error happens, you can call "readcodeat" tool to read the code, and decide what to do next.
        to find definition of identity in <selector>.<VarOrFuncName>, the <column> of the span must be set the the start position of last identity <VarOrFuncName>.
    """
    result = finddefinition(question)
    return result


def findimplementation(path: str) -> str:
    url = f"{BASE_URL}/find-implementation"
    headers = {
        "Content-Type": "application/json",
        "Authorization": f"Bearer {AUTH_TOKEN}"  # 添加认证头
    }
    data = {
        "input": path
    }

    try:
        response = requests.post(url, headers=headers, json=data)
        response.raise_for_status()  # 检查响应状态
        result = response.json()

        if "error" in result and result["error"]:
            return f"Error: {result['error']}"

        return result.get("result", "")
    except requests.exceptions.RequestException as e:
        return f"Request failed: {str(e)}"


@tool("findimplementation")
def implementation(question: str) -> str:
    """
    "FindImplementation" tool is used to query where the current func is implemented. Combined with "finddefinition",
    it can solve some problems of func internal implementation,
    including some methods registered in the interface.

    If the span contains "func" and belongs to a function, you must call "FindImplementation" to analyze whether there are other implementations.

    If the return value contains ", not a", the analysis result of the current tools is ignored directly.
    """
    result = findimplementation(question)
    return result


def findreferences(path: str) -> str:
    url = f"{BASE_URL}/find-references"
    headers = {
        "Content-Type": "application/json",
        "Authorization": f"Bearer {AUTH_TOKEN}"  # 添加认证头
    }
    data = {
        "input": path
    }

    try:
        response = requests.post(url, headers=headers, json=data)
        response.raise_for_status()  # 检查响应状态
        result = response.json()

        if "error" in result and result["error"]:
            return f"Error: {result['error']}"

        return result.get("result", "")
    except requests.exceptions.RequestException as e:
        return f"Request failed: {str(e)}"


@tool("findreferences")
def references(question: str) -> str:
    """
    Useful for finding references of variable or function at span. 
        The input to this tool is span, the output is one or more spans with code snippet. 
        if error with keyword "not an identifier", means span is invalid, should call "FindSpansAtLine" tool to read all valid spans and choose valid span to call finddefinition tool.
        if some error happens, you can call "readcodeat" tool to read the code, and decide what to do next.
        to find references of identity in <selector>.<VarOrFuncName>, the <column> of the span must be set the the start position of last identity <VarOrFuncName>.
    """
    result = findreferences(question)
    return result


def findrelated(path: str) -> str:
    url = f"{BASE_URL}/find-related"
    headers = {
        "Content-Type": "application/json",
        "Authorization": f"Bearer {AUTH_TOKEN}"  # 添加认证头
    }
    data = {
        "input": path
    }

    try:
        response = requests.post(url, headers=headers, json=data)
        response.raise_for_status()  # 检查响应状态
        result = response.json()

        if "error" in result and result["error"]:
            return f"Error: {result['error']}"

        return result.get("result", "")
    except requests.exceptions.RequestException as e:
        return f"Request failed: {str(e)}"


@tool("findrelated")
def related(question: str) -> str:
    """
    Useful for extract related variables and functions of the variable at span. 
    The input to this tool is span. the output is one or more spans with code snippet. 
    if error with keyword "not an identifier", means span is invalid, should call "FindSpansAtLine" tool to read all valid spans and choose valid span to call finddefinition tool.
    "no related variable or related function found" is return if no assignment and no function call happens at span.
    if some error happens, you can call "readcodeat" tool to read the code, and decide what to do next.
    """
    result = findrelated(question)
    return result


def findspans(path: str) -> str:
    url = f"{BASE_URL}/find-spans"
    headers = {
        "Content-Type": "application/json",
        "Authorization": f"Bearer {AUTH_TOKEN}"  # 添加认证头
    }
    data = {
        "input": path
    }

    try:
        response = requests.post(url, headers=headers, json=data)
        response.raise_for_status()  # 检查响应状态
        result = response.json()

        if "error" in result and result["error"]:
            return f"Error: {result['error']}"

        return result.get("result", "")
    except requests.exceptions.RequestException as e:
        return f"Request failed: {str(e)}"


@tool("findspans")
def spans(question: str) -> str:
    """
    Useful for extract spans for a line of code, 
        The input to this tool is span. the output is code at input span and one or more spans with identity.
        this code must be invoked to find valid spans, before call other tools.
    """
    result = findspans(question)
    return result


def readcode(path: str) -> str:
    url = f"{BASE_URL}/read-code"
    headers = {
        "Content-Type": "application/json",
        "Authorization": f"Bearer {AUTH_TOKEN}"  # 添加认证头
    }
    data = {
        "input": path
    }

    try:
        response = requests.post(url, headers=headers, json=data)
        response.raise_for_status()  # 检查响应状态
        result = response.json()

        if "error" in result and result["error"]:
            return f"Error: {result['error']}"

        return result.get("result", "")
    except requests.exceptions.RequestException as e:
        return f"Request failed: {str(e)}"


@tool("readcode")
def code(question: str) -> str:
    """
    Useful for reading code from file at span. 
    The input to this tool is span, the output is span with code snippet. output the relate code at the specified position with line numbers
    """
    result = readcode(question)
    return result


def readdirectorytree(path: str) -> str:
    url = f"{BASE_URL}/read-directory-tree"
    headers = {
        "Content-Type": "application/json",
        "Authorization": f"Bearer {AUTH_TOKEN}"  # 添加认证头
    }
    data = {
        "input": path
    }

    try:
        response = requests.post(url, headers=headers, json=data)
        response.raise_for_status()  # 检查响应状态
        result = response.json()

        if "error" in result and result["error"]:
            return f"Error: {result['error']}"

        return result.get("result", "")
    except requests.exceptions.RequestException as e:
        return f"Request failed: {str(e)}"


@tool("readdirectorytree")
def directorytree(question: str) -> str:
    """
    Used to read a directory tree.
    The input of this tool is span, and the output is the directory tree of the project where the code is located.
    """
    result = readdirectorytree(question)
    return result


def readfile(path: str) -> str:
    url = f"{BASE_URL}/read-file"
    headers = {
        "Content-Type": "application/json",
        "Authorization": f"Bearer {AUTH_TOKEN}"  # 添加认证头
    }
    data = {
        "input": path
    }

    try:
        response = requests.post(url, headers=headers, json=data)
        response.raise_for_status()  # 检查响应状态
        result = response.json()

        if "error" in result and result["error"]:
            return f"Error: {result['error']}"

        return result.get("result", "")
    except requests.exceptions.RequestException as e:
        return f"Request failed: {str(e)}"


@tool("readfile")
def read(question: str) -> str:
    """
    Useful for reading content of file at span. 
    The input to this tool is span, the output is content of the file.
    """
    result = readfile(question)
    return result
