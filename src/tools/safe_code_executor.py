from crewai.tools import tool
from io import StringIO
import sys
from automation.code_executor import CodeExecutor

@tool("safe code interpreter")
def safe_code_interpreter(command: str) -> str:
    """A Python interpreter. Use this to execute python code. 
        Input should be a complete and self-contained python code. 
        *All the variables and functions should be defined in the current input code snippet.
        If you expect output it should be printed out.
        """
    
    print ("----Executing Python code snippet: \n" + command + "\n----\n\n")
    executor = CodeExecutor()
    return executor.run_code_with_executor(command)

