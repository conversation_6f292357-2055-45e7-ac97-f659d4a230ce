import base64
import json
import time

from common.message_queue import read_message_queue, write_message_queue


def send_command(body: str) -> str:
    content = base64.b64encode(str(body).encode('utf-8')).decode('utf-8')
    message_json = f"""{{"type":"command","source":"print","content":"{content}"}}"""
    write_message_queue.put(message_json)
    return message_json


def receive_command() -> str:
    start_time = time.time()
    timeout = 180  # 3分钟超时时间（秒）

    while True:
        if not read_message_queue.empty():
            message = read_message_queue.get(block=False)
            print("Raw message:", message)
            try:
                message = json.loads(message)
                print("Parsed JSON message:", message)
                if message["type"] == "command":
                    decoded_content = base64.b64decode(
                        message["content"]).decode('utf-8')
                    print("Decoded content:", decoded_content)
                    return decoded_content
            except (json.J<PERSON><PERSON><PERSON><PERSON><PERSON>r, KeyError,
                    base64.binascii.Error) as e:
                print(f"Error processing message: {e}")
                return None
        else:
            # 检查是否超时
            if time.time() - start_time > timeout:
                print("等待反馈超时：已经等待超过3分钟")
                return "等待反馈超时，系统自动结束"
            time.sleep(10)


def collect_command(tips: str) -> str:
    send_command(tips)
    return receive_command()


CONTINUE_THE_AUTOMATION = "continue_the_automation"