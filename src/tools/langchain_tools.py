from crewai.tools import tool
from langchain_community.utilities.arxiv import Arxiv<PERSON><PERSON><PERSON>rapper
from langchain_community.utilities.asknews import AskNewsAPIWrapper

# from langchain_community.utilities import GoogleBooksAPIWrapper
from langchain_community.utilities.wikipedia import WikipediaAPIWrapper

# # GOOGLE_BOOKS_API_KEY 需要设置这个环境变量
# # https://console.cloud.google.com/apis/api/books.googleapis.com/credentials?invt=Abtq0Q&project=spotmax
# @tool("google_books")
# def google_books(question: str) -> str:
#     """Wrapper around Google Books API.

#     To use, you should have a Google Books API key available.
#     This wrapper will use the Google Books API to conduct searches and
#     fetch books based on a query passed in by the agents. By default,
#     it will return the top-k results.

#     The response for each book will contain the book title, author name, summary, and
#     a source link.
#     """
#     google_book = GoogleBooksAPIWrapper()
#     return google_book.run(question)


@tool("asknews")
def asknews(question: str) -> str:
    """Wrapper for AskNews API."""
    """
    AskNews是一款新闻增强工具,每日处理30万篇文章,提供自然语言查询接口。它翻译、总结、提取实体,索引到向量数据库,覆盖多国多语言,注重透明度。
    """
    asknews = AskNewsAPIWrapper()
    return asknews.run(question)


@tool("wikipedia")
def wikipedia(question: str) -> str:
    """Wrapper around WikipediaAPI.

    To use, you should have the ``wikipedia`` python package installed.
    This wrapper will use the Wikipedia API to conduct searches and
    fetch page summaries. By default, it will return the page summaries
    of the top-k results.
    It limits the Document content by doc_content_chars_max.
    """
    """
    维基百科 是一个多语言的自由在线百科全书，由志愿者社区（称为维基人）通过开放协作和使用名为 MediaWiki 的 wiki 编辑系统编写和维护。 Wikipedia 是历史上规模最大、阅读量最多的参考作品。
    """
    wiki = WikipediaAPIWrapper()
    return wiki.run(question)


@tool("arxiv")
def arxiv(question: str) -> str:
    """Wrapper around ArxivAPI."""
    """
    arXiv是一个收集物理学、数学、计算机科学、生物学与数理经济学的论文预印本的网站。其中arXiv发音同“archive”，因为“X”代表希腊字母，国际音标为[kai]。它于1991年8月14日成立，现由美国康奈尔大学维护。
    """
    arxiv = ArxivAPIWrapper()
    return arxiv.run(question)
