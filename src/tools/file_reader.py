from engine.common import read_file_as_string, read_pdf_as_string, read_docx_as_string, url_to_markdown, excel_to_csv_string
from crewai.tools import tool

@tool("file reader tool")
def get_contents_from_file(file_path:str) -> str:
    """
    Read the content of a file and return it as a string.
    input is the file path or URL, and the output is a plain text string.
    """
    if file_path.endswith('.txt') or file_path.endswith('.csv') or file_path.endswith('.json'):
        return read_file_as_string(file_path)
    elif file_path.endswith('.pdf'):
        return read_pdf_as_string(file_path)
    elif file_path.endswith('.docx'):
        return read_docx_as_string(file_path)
    elif file_path.startswith('http://') or file_path.startswith('https://'):
        return url_to_markdown(file_path)
    elif file_path.endswith('.xlsx') or file_path.endswith('.xls'):
        return excel_to_csv_string(file_path)
    else:
        raise ValueError(f"Unsupported file format: {file_path}")