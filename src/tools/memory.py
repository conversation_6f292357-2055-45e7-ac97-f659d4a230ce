import uuid
from langgraph.store.memory import InMemoryStore
from langgraph.store.postgres import PostgresStore
from langmem import create_memory_manager
import os
import json
from crewai.tools import tool
# CREATE TABLE IF NOT EXISTS store (
#     prefix TEXT NOT NULL,
#     key TEXT NOT NULL,
#     value JSONB NOT NULL,
#     created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
#     updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
#     expires_at TIMESTAMP WITH TIME ZONE,  -- Expiration timestamp
#     ttl_minutes INTEGER,                  -- TTL duration in minutes
#     PRIMARY KEY (prefix, key)
# );

def setup_store():
    store = PostgresStore.from_conn_string(
        os.environ['MEMORY_STORE_URL'],
    )
    # Initializes necessary tables and schemas
    # store.create_tables()
    return store
#in_memory_store = InMemoryStore()
# Namespace for the memory to save
user_id = "1"
namespace_for_memory = (user_id, "memories")

# Save a memory to namespace as key and value
key = str(uuid.uuid4())

# The value needs to be a dictionary  
#value = {"food_preference" : "I like pizza"}

# Save the memory
#in_memory_store.put("agent", key, value)
#memories = in_memory_store.search(namespace_for_memory)
#print(memories[0].key, memories[0].value)

###
# Compare this snippet from jarvis_tools/memory.py:

from pydantic import BaseModel, Field, ConfigDict
from typing import Type
from crewai.tools import BaseTool
from engine.prebuilt_llms import prebuilt_llms
# from google.cloud import aiplatform
DEFAULT_KEY = "info"
# try:
#     PROJECT_ID = "spotmax"
#     LOCATION = "us-central1"  
#     DEFAULT_KEY = "info"
#     aiplatform.init(project=PROJECT_ID, location=LOCATION)

#     from langchain_google_vertexai import VertexAIEmbeddings

#     embeddings = VertexAIEmbeddings(model_name="text-embedding-004")
# except Exception as e:
#     print("Error initializing Vertex AI:", e)
#     embeddings = None


def update_memory(llm, org_preference:str, new_input:str)-> str:  
    prompt = f"""here's the original memory info {org_preference}:\n
    ---
    {org_preference}
    ---
    Now, we got the new info following.
    ---
    {new_input} 
    ---
    update and the memory info with the new info to avoid the conflict and duplicate.
    Always make the memory info more clear and concise.
    Only return the updated memory info.
    """
    ret = llm.call(prompt)
    return ret


class MemToolInput(BaseModel):
    category_of_content: str = Field(..., description="the category of the content")
    content_need_to_store: str = Field(..., description="the content needed to store")

class MemTool(BaseTool):
    user_id: str = "agent"
    model_config = ConfigDict(arbitrary_types_allowed=True)
    name: str = "long-term memory"
    space: str = "default"
    description: str = """
    store the info into the long-term memory.
    """
    args_schema: Type[BaseModel] = MemToolInput

    llm: str = "gpt-4.1"
    # in_memory_store: InMemoryStore = InMemoryStore(
    #     # index={
    #     # "dims": 768, 
    #     # "embed": embeddings.embed_query,
    #     # }    
    # )
   

    def _run(self,category_of_content: str, content_need_to_store: str) -> str:
        print("invoking the memory\n")
        namespace = (self.user_id, self.space)
        with setup_store() as in_memory_store: 
            print("update preference for category: " + category_of_content + "\n")
            org_info_json = in_memory_store.get(namespace, category_of_content)
            if not org_info_json:
                print("store:" + category_of_content + ":" + content_need_to_store +"\n")
                dict_info = {DEFAULT_KEY: content_need_to_store}
                store_info= json.dumps(dict_info)
                in_memory_store.put(namespace, category_of_content, store_info)      
            else:
                org_info = org_info_json.value[DEFAULT_KEY]
                print("orginal:", org_info ,"\n")
                #org_info_obj = json.loads(org_info_json.value[0].value)
                updated = update_memory(self.llm, org_info,content_need_to_store)
                print("updated preference is " + updated + "\n")
                dict_updated = {DEFAULT_KEY: updated}
                store_info=json.dumps(dict_updated)                  
                in_memory_store.put(namespace, category_of_content, store_info)
        return "stored"


class MemExtractingTool:

    def __init__(self, user_id, space, category ):
        self.space = space
        self.category = category
        self.user_id = user_id

    def extract(self) -> str:
        print("invoking the memory\n")
        
        namespace = (self.user_id,self.space)
        print("memory in : ", namespace, self.category , "\n")
        with setup_store() as in_memory_store:
            memories = in_memory_store.get(namespace,self.category)
            if not memories:
                print("No memories found!")
                return ""
            else:
                print("memories found!")
                print(memories.value[DEFAULT_KEY])
                return memories.value[DEFAULT_KEY]
            
   
# namespace = ("agent","memory")
# with setup_store() as in_memory_store:
#    memories = in_memory_store.get(("agent","report_creation"),"user_preference")
#    if not memories:
#     print("No memories found!")
#    print(memories.key, memories.value)


# def llm_test():        
#     llm = prebuilt_llms["gpt-4o"]
#     prompt = f"""
#     Compare the Java and Python.
#     """
#     ret = llm.call(prompt)
#     print("\nupdated preference is " + ret + "\n")

# llm_test()


class MemExtractorInput:
    category_of_content: str = Field(..., description="the category of the content")
    content_need_to_store: str = Field(..., description="the content needed to store")


class MemExtractor(BaseTool):

    description: str = """
    extract the content from the long-term memory.
    """
    args_schema: Type[BaseModel] = MemToolInput

    llm: str = "gpt-4.1"
    # in_memory_store: InMemoryStore = InMemoryStore(
    #     # index={
    #     # "dims": 768, 
    #     # "embed": embeddings.embed_query,
    #     # }    
    # )
   
    def __init__(self, user_id, space, llm):
        pass
    def _run(self,category: str) -> str:
        print("Extracting the memory\n")
        extractor = MemExtractingTool(user_id, self.space, "category")
        return "stored"
