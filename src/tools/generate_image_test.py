from typing import Any, Callable, Optional

from crewai import LLM, Agent, Crew, Task, TaskOutput
from crewai.agents.parser import AgentAction, AgentFinish
from crewai.tools.tool_types import ToolResult
from dotenv import load_dotenv
from generate_image import generate_image_by_image4

load_dotenv("/Users/<USER>/workspace/Jarvis/jarvis/.env")

llm = LLM(
    model="vertex_ai/gemini-2.0-flash-exp",
    max_completion_tokens=8192,
    max_tokens=8192,
    temperature=0,
    seed=1,
)


def test_generate_image_by_image4(
    task_callback: Optional[Any] = None, step_callback: Optional[Any] = None
):
    researcher = Agent(
        role="绘图",
        goal="""image4 提示词专家 可以使用google image4绘图""",
        backstory="""绘图专家""",
        tools=[generate_image_by_image4],
        allow_delegation=False,
        llm=llm,
    )

    task = Task(
        description=""" 帮我用 google image4 文生图模型 绘制一个符合中国中秋节氛围的图片""",
        expected_output="""直接输出图片地址""",
        agent=researcher,
    )

    crew = Crew(agents=[researcher], tasks=[task], verbose=True)

    result = crew.kickoff()
    print(result)


test_generate_image_by_image4()
