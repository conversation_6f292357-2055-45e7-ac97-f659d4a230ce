from crewai.tools import tool
import time
import os
def write_file(body: str) -> str:
    """Simulate sending an email by writing the report to a file."""
    filename = "report.txt"
    with open(filename, "w") as f:
        f.write(f"{body}")
    return f"Report written to {filename}"

def read_file() -> str:
    """Simulate reading an email reply by reading from a reply file."""
    reply_filename = "report_reply.txt"
    feedback = ""
    while True:
        if os.path.exists(reply_filename):
            with open(reply_filename, "r") as f:
                feedback = f.read().strip()
            # Optionally clear the file after reading to simulate marking as read
            os.remove(reply_filename)
            return feedback
        else:
            time.sleep(10)

    
@tool("user feedback collecting tool in async way")
def collect_user_feedback_asyn(content_need_to_get_user_feedback: str) -> str:
    """
    Ask for the feedback on your work result. 
    The input is the content you want to send to the user for feedback and its format is a plain text string (not json).
    """
    print(write_file(content_need_to_get_user_feedback))
    return "user's feedback: "+ read_file()