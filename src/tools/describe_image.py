import json
import logging
from typing import Dict

import requests
from crewai.tools import tool
from google import genai
from google.genai import types


def get_image(image_file):
    if isinstance(image_file, str) and image_file.startswith(('http://', 'https://')):
        response = requests.get(image_file)
        response.raise_for_status()
        return response.content
    with open(image_file, "rb") as image:
        image_bytes = image.read()
    return image_bytes


def get_image_extension(image_bytes):
    # Dictionary of image file signatures
    signatures = {
        b'\xFF\xD8\xFF': 'jpeg',
        b'\x89\x50\x4E\x47\x0D\x0A\x1A\x0A': 'png',
        b'\x47\x49\x46\x38': 'gif',
        b'\x42\x4D': 'bmp'
    }

    # Determine the file extension
    for signature, extension in signatures.items():
        if image_bytes.startswith(signature):
            return extension
    return 'unknown'


class Result:
    def __init__(self, success: bool, result=None, error=None):
        self.success = success
        self.result = result
        self.error = error


class GCPGenai:
    def __init__(self, model_version: str = "gemini-2.5-flash", config: Dict[str, str] = None):
        self.model_version = model_version
        self.client = genai.Client(
            vertexai=True,
            project="mobvista-prod",
            location="us-central1",
        )

        thinking_budget = 5000
        if config is not None and config.get("thinking_budget") is not None:
            budget = config.get("thinking_budget", "0")
            if budget.isdigit():
                thinking_budget = int(budget)
                if thinking_budget > 5000:
                    thinking_budget = 5000
                elif thinking_budget < 1024:
                    thinking_budget = 0

        self.generate_content_config = types.GenerateContentConfig(
            temperature=0,
            max_output_tokens=8192,
            thinking_config=types.ThinkingConfig(
                thinking_budget=thinking_budget,
            ),
            safety_settings=[types.SafetySetting(
                category=types.HarmCategory.HARM_CATEGORY_HATE_SPEECH,
                threshold=types.HarmBlockThreshold.OFF
            ), types.SafetySetting(
                category=types.HarmCategory.HARM_CATEGORY_DANGEROUS_CONTENT,
                threshold=types.HarmBlockThreshold.OFF
            ), types.SafetySetting(
                category=types.HarmCategory.HARM_CATEGORY_SEXUALLY_EXPLICIT,
                threshold=types.HarmBlockThreshold.OFF
            ), types.SafetySetting(
                category=types.HarmCategory.HARM_CATEGORY_HARASSMENT,
                threshold=types.HarmBlockThreshold.OFF
            )]
        )

    def preprocess_image(self, image_bytes: bytes):
        image_type = get_image_extension(image_bytes)
        image_obj = types.Part.from_bytes(
            mime_type="image/" + image_type,
            data=image_bytes)
        return image_obj

    def generate(self, prompt: str, images: list[str]) -> Result:
        image_objs = []
        for image in images:
            images_bytes = get_image(image)
            image_obj = self.preprocess_image(images_bytes)
            image_objs.append(image_obj)
        image_objs.append(prompt)

        try:
            responses = self.client.models.generate_content_stream(
                model=self.model_version,
                contents=image_objs,
                config=self.generate_content_config,
            )

            final_response = ""
            input_tokens = 0
            output_tokens = 0
            for response in responses:
                if response.text is not None:
                    final_response = final_response + response.text
                if response.usage_metadata.prompt_token_count is not None:
                    input_tokens += response.usage_metadata.prompt_token_count
                if response.usage_metadata.total_token_count is not None:
                    output_tokens += response.usage_metadata.total_token_count

            return Result(success=True, result={
                "text": final_response,
                "input_tokens": input_tokens,
                "output_tokens": output_tokens,
                "model_version": self.model_version,
            }, error=None)
        except Exception as e:
            logging.error(e)
            return Result(success=False, result=None, error={e})


@tool("describe_image_with_gemini")
def describe_image_with_gemini(image_url: str, user_request: str) -> str:
    """
    decribe the given image according to the user request
    Args:
        image_url (str): The URL of the image to be described.
        user_request (str): The user's request or prompt for the description.

    """

    if not user_request or not image_url:
        return "parameter is empty prompt and image_url is Required"
    print(f"describe_image_with_gemini: '{user_request}',' {image_url}'")
    gcPGenai = GCPGenai()
    # gcPGenai = GCPGenai(model_version="gemini-2.5-flash-preview-04-17",
    #                     config={"thinking_budget": "5000",
    #                             "max_output_tokens": "9000"}
    #                     )
    result = gcPGenai.generate(user_request, [image_url])
    if result.success:
        return result.result["text"]
    else:
        return result.error
