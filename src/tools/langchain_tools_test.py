from crewai import Crew, Task, Agent, LLM
from dotenv import load_dotenv
from langchain_tools import wikipedia, asknews, arxiv

load_dotenv("/Users/<USER>/workspace/Jarvis/jarvis/.env")

llm = LLM(
    model='vertex_ai/gemini-2.0-flash-exp',
    max_completion_tokens=8192,
    max_tokens=8192,
    temperature=0,
    seed=1,
)


def test_wiki():
    # 创建一个使用 web_search 工具的 Agent
    researcher = Agent(
        role='Researcher',
        goal='Gather information about a given topic.',
        backstory='An expert researcher with years of experience.',
        tools=[wikipedia],
        allow_delegation=False,
        llm=llm)

    # 你不能直接调用 researcher.web_search("特朗普是谁")
    # 你需要让 agent 执行任务并使用工具
    # 例如，创建一个 Crew 并让 agent 执行任务

    task = Task(description=
                "Who is <PERSON> and what are his major accomplishments?",
                expected_output="""直接输出文案内容""",
                agent=researcher)

    crew = Crew(agents=[researcher], tasks=[task], verbose=True)

    result = crew.kickoff()
    print(result)


# test_wiki()


def test_asknews():
    researcher = Agent(role='记者',
                       goal="""采集、撰写和编辑高质量的新闻报道
进行实地采访,与消息源建立并维护良好关系
跟踪热点话题,发掘有价值的新闻线索
参与编辑会议,提出报道建议
制作多媒体内容,包括文字、图片、音视频等
在截稿期限内完成分配的任务
确保报道的准确性和公正性
配合其他部门,参与跨平台内容制作""",
                       backstory="""新闻学、传播学、媒体相关专业学历
扎实的写作和编辑能力
熟悉新闻采访技巧和伦理规范
了解媒体法律法规
具备数字媒体素养,能够使用相关软件和工具
对时事政治、经济、文化等领域有基本了解
具有批判性思维和信息分析能力
良好的人际交往和沟通能力
能够在压力下工作,适应不规律的工作时间
有相关实习或工作经验者优先""",
                       tools=[asknews],
                       allow_delegation=False,
                       llm=llm)

    # 你不能直接调用 researcher.web_search("特朗普是谁")
    # 你需要让 agent 执行任务并使用工具
    # 例如，创建一个 Crew 并让 agent 执行任务

    task = Task(description="美国对等关税",
                expected_output="""直接输出文案内容""",
                agent=researcher)

    crew = Crew(agents=[researcher], tasks=[task], verbose=True)

    result = crew.kickoff()
    print(result)


# test_asknews()


def test_arxiv():
    researcher = Agent(
        role='研究员',
        goal="""Gather information about a given topic.""",
        backstory="""An expert researcher with years of experience.""",
        tools=[arxiv],
        allow_delegation=False,
        llm=llm)

    task = Task(description="What is the latest research on climate change?",
                expected_output="""直接输出文案内容""",
                agent=researcher)

    crew = Crew(agents=[researcher], tasks=[task], verbose=True)

    result = crew.kickoff()
    print(result)

test_arxiv()
