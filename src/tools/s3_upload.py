import hashlib
import os
import time
import uuid

from crewai.tools import tool

from service.s3_service import bucket_name, upload_to_s3


@tool("upload_file_to_s3")
def upload_file_to_s3_tool(local_file_path: str) -> str:
    """
    upload file to S3.
    The input is the local path of the file need to upload. The input of the tool should be a plain text path, such as: "sales_trend.png", not JSON format string.
    The output is the link which can be used to refer to the uploaded file/image
    """
    return upload_file_to_s3(local_file_path)


def upload_file_to_s3(local_file_path: str) -> str:
    # 获取文件名和后缀
    file_name = os.path.basename(local_file_path)
    file_base, file_ext = os.path.splitext(file_name)

    # 对文件名进行MD5处理
    # 确保 file_base 是字符串类型，再进行编码操作
    if isinstance(file_base, bytes):
        md5_hash = hashlib.md5(file_base).hexdigest()
    else:
        md5_hash = hashlib.md5(file_base.encode()).hexdigest()

    uuid_str = str(uuid.uuid4())
    # 获取当前日期并格式化为yyyymmdd
    current_date = time.strftime("%Y/%m/%d")
    # 拼接新的S3 key
    s3_key = f"{bucket_name}/{current_date}/{uuid_str}/{md5_hash}/{file_base}{file_ext}"

    upload_to_s3(local_file_path, s3_key)

    return f"https://maxagent-api.spotmaxtech.com/api/v1/s3_file/?s3_key={s3_key}"
