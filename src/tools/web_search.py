from crewai.tools import tool
from langchain_community.utilities import GoogleSearchAPIWrapper


@tool("web search")
def web_search(question: str) -> str:
    """
    invoke it only when you need to answer questions about current info/event. 
    The input should be a search query and use the text description directly instead of the json format string.
    """
    search = GoogleSearchAPIWrapper()
    return search.run(question)
