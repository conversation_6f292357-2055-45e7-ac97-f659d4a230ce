import base64
import json
import time

from crewai.tools import tool

from common.message_queue import read_message_queue, write_message_queue


def ask_human(body: str) -> str:
    content = base64.b64encode(str(body).encode('utf-8')).decode('utf-8')
    message_json = f"""{{"type":"human","source":"print","content":"{content}"}}"""
    write_message_queue.put(message_json)
    return message_json


def feedback() -> str:
    start_time = time.time()
    timeout = 180  # 3分钟超时时间（秒）

    while True:
        if not read_message_queue.empty():
            message = read_message_queue.get(block=False)
            print("Raw message:", message)
            try:
                message = json.loads(message)
                print("Parsed JSON message:", message)
                if message["type"] == "human":
                    decoded_content = base64.b64decode(
                        message["content"]).decode('utf-8')
                    print("Decoded content:", decoded_content)
                    return decoded_content
            except (json.<PERSON><PERSON>r, KeyError,
                    base64.binascii.Error) as e:
                print(f"Error processing message: {e}")
                return None
        else:
            # 检查是否超时
            if time.time() - start_time > timeout:
                print("等待反馈超时：已经等待超过3分钟")
                return "等待反馈超时，系统自动结束"
            time.sleep(10)


@tool("user feedback collecting tool in async way")
def collect_user_feedback_websocket(content_need_to_get_user_feedback: str) -> str:
    """
    Ask for the feedback on your work result. 
    The input is the content you want to send to the user for feedback and its format is a plain text string (not json).
    """
    ask_human(content_need_to_get_user_feedback)
    return "user's feedback: " + feedback()


# class HumanFeedBack(BaseTool):
#     name: str = "User's feedback collecting"
#     description: str = ("""
#         Provide your work result to the user and ask the feedback.
#         """)

#     def _run(self, argument: str) -> str:
#         ask_human(argument)
#         return feedback()
