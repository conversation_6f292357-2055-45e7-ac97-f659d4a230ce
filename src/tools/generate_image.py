import math
import os
import time
import typing
import uuid

from crewai.tools import tool
from google import genai
from google.genai import types
from PIL import Image as PIL_Image
from PIL import ImageOps as PIL_ImageOps

from common.redirect_root import agent_config_home_path
from tools.s3_upload import upload_file_to_s3

image4_resolutions = ["1*1", "9*16", "16*9", "3*4", "4*3"]


import os

generation_model = "imagen-4.0-generate-preview-06-06"
generation_model_fast = "imagen-4.0-fast-generate-preview-06-06"
generation_model_ultra = "imagen-4.0-ultra-generate-preview-06-06"


@tool("generate_image_by_image4")
def generate_image_by_image4(prompt: str, width: int = 1080, height: int = 1920) -> str:
    """
    使用Google Imagen 4.0模型生成图像。

    参数:
        prompt (str): 图像生成的文本描述，支持中英文，格式为纯文本字符串（非JSON格式）
        width (int, optional): 期望的图像宽度，默认1080像素
        height (int, optional): 期望的图像高度，默认1920像素

    返回:
        str: 生成图像上传到S3后的URL链接

    注意:
        - 实际生成的图像尺寸会根据Imagen 4.0支持的宽高比进行调整
        - 支持的宽高比包括: 1:1, 9:16, 16:9, 3:4, 4:3
        - 生成的图像会自动上传到S3存储并返回访问链接
        - 使用安全过滤器阻止中等及以上级别的不当内容
    """
    try:
        url = generate_image(prompt=prompt, width=width, height=height)
        return url
    except Exception as e:
        print(e)
        return ""


def generate_image(prompt: str, width: int = 1080, height: int = 1920) -> str:
    print(f"generate_image_by_image4 prompt:{prompt}")
    start_time = time.time()

    image_file = f"{agent_config_home_path}/{uuid.uuid4()}.png"
    image_directory = os.path.dirname(image_file)
    os.makedirs(image_directory, exist_ok=True)

    project = "mobvista-prod"

    location = os.environ.get("GOOGLE_CLOUD_REGION", "us-central1")

    client = genai.Client(vertexai=True, project=project, location=location)

    closest_width, closest_height = find_closest_resolution(width, height)
    ratio = f"{closest_width}:{closest_height}"

    image_response = client.models.generate_images(
        model=generation_model,
        prompt=prompt,
        config=types.GenerateImagesConfig(
            aspect_ratio=ratio,
            number_of_images=1,
            safety_filter_level="BLOCK_MEDIUM_AND_ABOVE",
            person_generation="ALLOW_ADULT",
        ),
    )

    image = image_response.generated_images[0].image

    end_time = time.time()
    execution_time = end_time - start_time
    print(
        f"image4 generate image total time: {execution_time} seconds, image_file:{image_file}"
    )
    # print(image)

    pil_image = typing.cast(PIL_Image.Image, image._pil_image)
    if pil_image.mode != "RGB":
        # RGB is supported by all Jupyter environments (e.g. RGBA is not yet)
        pil_image = pil_image.convert("RGB")
    image_width, image_height = pil_image.size
    if width < image_width or height < image_height:
        # Resize to display a smaller notebook image
        pil_image = PIL_ImageOps.contain(pil_image, (width, height))
    pil_image.save(image_file)
    url = upload_file_to_s3(image_file)
    return url


def find_closest_resolution(target_width, target_height):
    target_ratio = target_width / target_height
    target_pixels = target_width * target_height
    closest_width = None
    closest_height = None
    min_difference = float("inf")

    image4_resolutions = ["1*1", "9*16", "16*9", "3*4", "4*3"]

    for resolution in image4_resolutions:
        width, height = map(int, resolution.split("*"))
        ratio = width / height
        pixels = width * height

        # 计算比例差异和像素差异
        ratio_diff = abs(ratio - target_ratio)
        pixel_diff = abs(pixels - target_pixels)

        # 使用欧几里得距离来综合考虑比例和像素差异
        difference = math.sqrt(ratio_diff**2 + (pixel_diff / target_pixels) ** 2)

        if difference < min_difference:
            min_difference = difference
            closest_width = width
            closest_height = height

    return closest_width, closest_height


if __name__ == "__main__":
    generate_image_by_image4("a cat", 1024, 1024)
