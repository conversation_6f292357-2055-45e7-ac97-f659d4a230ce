from crewai.tools import tool
from io import StringIO
import sys

@tool("code interpreter")
def code_interpreter(command: str) -> str:
    """A Python shell. Use this to execute python commands. 
        Input should be a valid python command.
        If you expect output it should be printed out.
        For example: to verify the the following python function
        ---
        def add(a, b):
            return (a+b)
        ---
        we can invoke the tool with the input 
        "
        def add(a, b):
            return (a+b)
        print (add(1,2))
        
        "
        """
    command = '\n'.join(line for line in command.split('\n') if not line.startswith('```'))
    """Run command and returns anything printed."""
    # sys.stderr.write("EXECUTING PYTHON CODE:\n---\n" + command + "\n---\n")
    old_stdout = sys.stdout
    sys.stdout = mystdout = StringIO()
    try:
        exec(command, globals())
        sys.stdout = old_stdout
        output = mystdout.getvalue()
    except Exception as e:
        sys.stdout = old_stdout
        output = str(e)
    # sys.stderr.write("PYTHON OUTPUT: \"" + output + "\"\n")
    return output
