from typing import Any, Callable, Optional

from crewai import LLM, Agent, Crew, Task, TaskOutput
from crewai.agents.parser import AgentAction, AgentFinish
from crewai.tools.tool_types import ToolResult
from describe_image import describe_image_with_gemini
from dotenv import load_dotenv

load_dotenv("/Users/<USER>/workspace/Jarvis/jarvis/.env")

llm = LLM(
    model='vertex_ai/gemini-2.0-flash-exp',
    max_completion_tokens=8192,
    max_tokens=8192,
    temperature=0,
    seed=1,
)


def test_describe_image_with_gemini(task_callback : Optional[Any] = None, step_callback: Optional[Any] = None):
    researcher = Agent(
        role='Visual Art Director',
        goal="""describe image""",
        backstory="""Master's degree or higher in Visual Design or Fine Arts
At least 8 years of experience in graphic design, illustration, or visual art direction
Mastery of Adobe Creative Suite
Deep foundation in art history and design theory
Professional understanding of various artistic styles (from classical to modern digital art)
Strong visual language analysis skills and experience applying design principles
Understanding of visual expression differences across cultural backgrounds
Expertise in image composition principles, perspective rules, and visual rhythm analysis""",
        tools=[describe_image_with_gemini],
        allow_delegation=False,
        llm=llm)

    task = Task(description="""analyze and describe this image ( https://resource-spotmaxtech-com.oss-cn-beijing.aliyuncs.com/temp/23333.jpg) in detail, including all visual elements and marketing-related information. Your description will be used to guide AI in generating a similar image. Note that the image may have special artistic styles like pixel art or hand-drawn sketches. Provide a comprehensive description focusing on the following aspects, but the description should avoid including any text, Slogans, buttons, logos, flags, titles, branding, and names.""",
                expected_output="""直接输出文案内容""",
                agent=researcher)


    crew = Crew(agents=[researcher], tasks=[task],
                verbose=True, task_callback=task_callback,step_callback=step_callback)

    result = crew.kickoff()
    print(result)


def task_callback(x:TaskOutput):

    print("-----------1-----------------")
    print(x.raw)
    print("------------2----------------")
    print(type(x))
    print("------------3----------------")

def step_callback(x:Any ):
    # 判断x类型是否为 crewai.agents.parser.AgentAction
    if isinstance(x, AgentFinish):
        print("------------AgentFinish----------------")
        print(x.thought)
        print(x.output)
        print(x.text)
        print("------------AgentFinish----------------")
    elif isinstance(x, AgentAction):
        print("------------AgentAction----------------")
        print(x.thought)
        print(x.tool)
        print(x.tool_input)
        print(x.result)
        print(x.text)
        print("------------AgentAction----------------")
    elif isinstance(x, ToolResult):
        print("------------ToolResult----------------")
        print(x.result)
        print("------------ToolResult----------------")
    else:
        print("------------else----------------")
        print(x)
        print("------------else----------------")
    

test_describe_image_with_gemini(task_callback, step_callback)
