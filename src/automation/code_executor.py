from smolagents.local_python_executor import LocalPythonExecutor

authorized_imports = [
    "pandas",
    "seaborn",
    "matplotlib",
    "matplotlib.*",
    "openpyxl",
    "numpy",
    "math",
    "scipy",
    "scipy.*",
    "scikit_learn",
    "scikit_learn.*",
    "stat",
    "pandas.*",
    "seaborn.*",
    "time",
    "datetime",
    "json", 
]

class CodeExecutor:

    def __init__(self, authorized_imports: list[str] = authorized_imports):
        """
        Initialize the CodeExecutor with optional authorized imports.

        Args:
            authorized_imports: List of module names allowed for import within executed code.
        """
        self.authorized_imports = authorized_imports if authorized_imports is not None else []
    def run_code_with_executor(self, code: str, local_vars: dict = None) -> dict:
        """
        Execute code using LocalPythonExecutor similarly to exec(code, {}, local_vars).

        Args:
            code: Python code to execute.
            local_vars: dict to be used and updated as the local namespace.
            authorized_imports: list of module names allowed for import within code.

        Returns:
            Updated local_vars after execution.
        """
        if local_vars is None:
            local_vars = {}
        
        # Initialize executor with allowed imports
        executor = LocalPythonExecutor(self.authorized_imports)

        # Optionally, enable built-in functions and tools
        executor.send_tools({})

        # Populate executor's internal state with local_vars
        executor.state.update(local_vars)

        # Run code (captures new variables inside executor.state)
        executor(code)

        # Retrieve updated variables and return to caller
        # You may want to exclude tool-related internals if needed
        result_vars = dict(executor.state)
        return result_vars