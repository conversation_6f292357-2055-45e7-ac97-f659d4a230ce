import hashlib
import json
import requests
from typing import Optional
from requests.exceptions import RequestException

magics = {
    "1234":"1234",
    "14592":"14592"
}
def get_magic(usr_id: str) -> str:
    """获取 magic 字符串"""
    magic = magics.get(usr_id)
    if magic:
        return magic
    else:
        return "default"
        # raise ValueError(f"No magic string found for user_id: {usr_id}")

def generate_vcode(magic: str, body: dict) -> str:
    payload_str = json.dumps(body, separators=(',', ':'), sort_keys=True)  # 保证稳定的 JSON 结构
    base_string = magic + payload_str
    computed_hash = hashlib.sha256(base_string.encode('utf-8')).hexdigest()
    return computed_hash

def build_headers(user_id: str, magic: str, vcode: str) -> dict:
    return {
        "userid": user_id,
        "magic": magic,
        "vcode": vcode,
        "Content-Type": "application/json"
    }


def invoke_service(url: str, user_id: str,  data:dict):
    magics = get_magic(user_id)
    if not magics:
        raise ValueError(f"No magic string found for user_id: {user_id}")
    vcode = generate_vcode(magics, data)
    print("user_id:", user_id)
    headers = build_headers(user_id, magics, vcode)
    response = requests.post(url, headers=headers, json=data)
    return response
