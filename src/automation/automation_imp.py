
import json
from pydantic import BaseModel
import argparse
import os
from automation.code_executor import CodeExecutor
from engine.llm_tracker import TokenMonitor
from typing import Any, Optional
os.environ["CREWAI_DISABLE_TELEMETRY"] = "true"
os.environ["OTEL_SDK_DISABLED"] = "true"
import sys
from engine.common import read_file_as_string
from service.tool_service import ToolService

# 定义需要检测的反编译工具模块名
FORBIDDEN_MODULES = {
    "uncompyle6",   # 常见反编译工具
    "decompyle3",   # 另一个反编译库
    "xdis",         # 反编译依赖库
    "pycdc",        # 其他可能的工具
    "decompiler"    # 扩展检测范围
}

# 检查是否导入了危险模块
for mod in FORBIDDEN_MODULES:
    if mod in sys.modules:
        print("[Security Alert] 检测到反编译工具: ", mod)
        print("程序已终止。")
        sys.exit(1)

from engine.team_loader import load_team as load_agent_team
import traceback
import re
try:
    from connector_client import invoke_service
except ImportError:
    from automation.connector_client import invoke_service
from engine.mcp_tools_loader import MCP_ToolLoader
# export PYTHONPATH=/Users/<USER>/git/jarvis/src

def extract_braced_text(s):
    match = re.search(r"\{([^}]*)\}", s)
    if match:
        return match.group(1)
    return None

def extract_quoted_text(s):
    match = re.search(r"\[([^}]*)\]", s)
    if match:
        return match.group(1)
    return None

agent_team_cache = {}

import re

def run_code_with_placeholders(code_str: str, values: dict):
    """
    将代码字符串中的 <key> 替换为 values 中对应的值，并执行代码。
    如果执行结果中有字典包含 "path-tag"，返回其值。

    参数:
        code_str (str): 包含占位符（形如 <key>）的 Python 代码字符串。
        values (dict): 用于替换占位符的键值对。

    返回:
        包含 "path-tag" 的值（如果有），否则返回局部变量 dict 或错误信息。
    """
    try:
        # 使用正则查找并替换所有 <key>
        def replace_placeholder(match):
            key = match.group(1)
            if key not in values:
                raise KeyError(f"缺少占位符的值: <{key}>")
            return str(values[key])

        formatted_code = re.sub(r'<(\w+)>', replace_placeholder, code_str)


        code_executor = CodeExecutor()
        return code_executor.run_code_with_executor(formatted_code)
    except Exception as e:
        return {"error": str(e)}


class Automation:

    def __init__(self, automation_config, will_run_with_history_context=False, max_tokens=500000, task_callback : Optional[Any] = None, step_callback:Optional[Any] = None, tool_service: ToolService = None):
        self.automation_config = automation_config
        self.will_run_with_history_context = will_run_with_history_context
        self.history_context = ""
        self.mcp_tool_loader = MCP_ToolLoader(tool_service=tool_service)
        self.token_counter = 0
        self.max_tokens = max_tokens
        self.token_tracker = TokenMonitor(max_tokens)
        self.teams = {}
        self.last_task = None
        self.task_callback = task_callback
        self.step_callback = step_callback
    
    def get_last_task(self):
        if self.last_task is None:
            raise ValueError("No last task found. Please run the automation first.")
        return self.last_task
    def get_team_by_name(self, team_name):
        if team_name in self.teams:
            return self.teams[team_name]
        else:
            raise ValueError("The team is not found in the loaded teams.")

    def process_node_by_name(self, user_id, node_name, nodes, runtime_params, result):
        for node in nodes:
            if node["name"] == node_name:
                if node["type"] == "connector":
                    config = node["config"]
                    
                    # Create headers with user_id if available
                    headers = {}
            
                    headers["userid"] = user_id
                    
                    # Prepare request data as a flat JSON
                    data = {}
                    
                    # Include params from the current connector configuration if available
                    if "params" in config:
                        for param_entry in config["params"]:
                            if "param" in param_entry:
                                param = param_entry["param"]
                                data[param["name"]] = param["value"]
                                if param["value"] == "{result}":
                                    data[param["name"]] = result
                
                    response = invoke_service(config["restful_url"], user_id, data)
                    response.raise_for_status()
                    response_data = response.text
                    print("Response from connector:\n", response_data)
                    result[node_name] = response_data

                    path_tag = response.headers.get("path-tag")
                    if path_tag:
                        print(f"Found path-tag in headers: {path_tag}")
                        # You can store it in result or handle it as needed
                        if "router"  in config:
                            for path_entry in config["router"]:
                                if "path" in path_entry:
                                    path_cfg = path_entry["path"]
                                    if path_tag == path_cfg["path_tag"]:
                                        next_node_name = path_cfg["next_node"]
                                        return self.process_node_by_name(user_id, next_node_name, nodes, runtime_params, result)
                            print(f"Path tag '{path_tag}' not found in router configuration.")

                    if  ("next_node" not in node) or (node["next_node"] is None):
                        print("no next node defined and the process is finished.")
                        return False, response_data

                    next_node_name = node["next_node"]
                    return self.process_node_by_name(user_id, next_node_name, nodes, runtime_params, result)
                elif node["type"] == "python":
                    config = node["config"]
                    code_path = config["code_file_path"]
                    # Prepare the values for the placeholders
                    values = {}
                    if "params" in config:
                        for param_entry in config["params"]:
                            if "param" in param_entry:
                                param = param_entry["param"]
                                values[param["name"]] = param["value"]
                                if param["value"].startswith("{"):
                                    values[param["name"]] = result[extract_braced_text(param["value"])]
                                if param["value"].startswith("["):
                                    values[param["name"]] = runtime_params[extract_quoted_text(param["value"])]
                    print("values:", values)
                    code = read_file_as_string(code_path)
                    
                    local_vars = run_code_with_placeholders(code, values)
                    print("code result:", local_vars)
                    ret_key = config.get("result_var", None)
                    if ret_key:
                        if ret_key in local_vars:
                            result[node_name] = local_vars[ret_key]
                            print(f"Found return key '{ret_key}' in execution result: {local_vars[ret_key]}")
                        else:
                            raise ValueError(f"Return key '{ret_key}' not found in the execution result.")
                    path_key = config.get("path_tag_var", None)
                    if path_key:
                        if path_key in local_vars:
                            path_tag = local_vars[path_key]
                        
                   
                    print(f"Found path-tag in execution result: {path_tag}")
                    # You can store it in result or handle it as needed
                    if "router"  in config:
                        for path_entry in config["router"]:
                            if "path" in path_entry:
                                path_cfg = path_entry["path"]
                                if path_tag == path_cfg["path_tag"]:
                                    next_node_name = path_cfg["next_node"]
                                    return self.process_node_by_name(user_id, next_node_name, nodes, runtime_params, result)
                        print(f"Path tag '{path_tag}' not found in router configuration.")
                    
                    if  ("next_node" not in node) or (node["next_node"] is None):
                        print("no next node defined and the process is finished.")
                        return True, local_vars
                    next_node_name = node["next_node"]
                    return self.process_node_by_name(user_id, next_node_name, nodes, runtime_params, result)
                elif node["type"] == "agent":
                    if "name" not in node:
                        raise ValueError("Agent node must have a 'name' field.")
                    name = node["name"]
                    config = node["config"]
                    data = {}
                    if "params" in config:
                        for param_entry in config["params"]:
                            if "param" in param_entry:
                                param = param_entry["param"]
                                data[param["name"]] = param["value"]
                                if param["value"].startswith("{"):
                                    data[param["name"]] = result[extract_braced_text(param["value"])]
                                if param["value"].startswith("["):
                                    data[param["name"]] = runtime_params[extract_quoted_text(param["value"])]
                    # team = agent_team_cache.get(name)
                    # print(f"team:{name}", team is None,"\n\n")
                    # if team is None:
                    #     print(f"Loading agent {name} with config path: {config['config_path']}")
                    #     team = load_agent_team(user_id, config["config_path"])
                    #     agent_team_cache[name] = team
                    # print("Agent received result: ", data.keys())
        
                    team, team_cfg = load_agent_team(user_id, config["config_path"], self.mcp_tool_loader,
                                            task_callback=self.task_callback,
                                            step_callback=self.step_callback)
                    self.teams[name] = team
                    self.last_task = team_cfg.get_tasks()[-1]
                    ret = team.kickoff(inputs=data)
                    self.token_counter += ret.token_usage.total_tokens
                    print(f"Token usage for agent {name}: {ret.token_usage.total_tokens}\n")
                    if self.token_counter > self.max_tokens:
                        print(f"❗Token limit exceeded: {self.token_counter} > {self.max_tokens}")
                        return True, ret     
                    if isinstance(ret, BaseModel):
                        ret = ret.model_dump_json()
                        ret_object = json.loads(ret)
                        if "raw" in ret_object:
                            ret = ret_object["raw"]
                    print(f"Agent {name} processed data: \n", ret)
                    result[node_name] = ret
                    # self.history_context += "\n\n"+"request: " + str(data) + "\n\n" + "response: " + str(ret)
                    if  ("next_node" not in node) or (node["next_node"] is None):
                        print("no next node defined and the process is finished.")
                        return True, result
                    next_node_name = node["next_node"]
                    return self.process_node_by_name(user_id, next_node_name, nodes, runtime_params, result)
                raise ValueError(f"Unknown node type: {node['type']}")
        raise ValueError(f"Node with name {node_name} not found in the configuration.")
        

    def process_nodes(self, user_id, nodes, runtime_params):
        current_node_index = 0
        result = {}  # Initialize as empty dict
        current_node = nodes[current_node_index]
        node_name = current_node["name"]
        return self.process_node_by_name(user_id, node_name, nodes, runtime_params, result)
               
    def load_config_from_file(self, config_file):
        """Load configuration from a JSON file."""
        try:
            if not os.path.exists(config_file):
                raise FileNotFoundError(f"Configuration file not found: {config_file}")
                
            with open(config_file, 'r') as f:
                config = json.load(f)
            
            # Validate that the config contains nodes
            if "nodes" not in config:
                raise ValueError("Invalid configuration file: 'nodes' key is missing")
            
            return config
        except json.JSONDecodeError as e:
            raise ValueError(f"Invalid JSON in configuration file: {str(e)}")
        
    def run_automation(self, user_id, runtime_params):
        """Main function to run the automation."""

        # Load configuration from file
        config = self.load_config_from_file(self.automation_config)
        
        # Process nodes according to configuration
        success, result = self.process_nodes(user_id, config["nodes"], runtime_params)
        
        if success:
            print("Automation completed successfully.")   
            return result
        else:
            print("Automation failed.")
            return None

    def __enter__(self):
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        self.mcp_tool_loader.clean_mcp_tool_instances()
        print("Cleaned up MCP tool instances.")
    
    def on_task_complete(self, task_output):
    # access token usage
        usage = task_output.token_usage or {}
        total = usage.get("total_tokens") or task_output.__dict__.get("usage_metrics", {}).get("total_tokens")
        token_counter = token_counter + total
        print(f"[Task] {task_output.description}: total_tokens = {total}")
        if total and total > self.max_tokens:
            raise RuntimeError(f"❗Token limit exceeded: {total} > {self.max_tokens}")

def run_config(user_id, config_file, params, is_multi_turn : bool = False, task_callback : Optional[Any] = None, step_callback:Optional[Any] = None, tool_service: ToolService = None):
    try:
        with Automation(config_file,is_multi_turn,task_callback=task_callback, step_callback=step_callback,tool_service=tool_service) as automation:
            result = automation.run_automation(user_id, params)
            print("Automation completed.")
            return result
    except Exception as e:
        print(f"Error running configuration: {e}")
        raise e