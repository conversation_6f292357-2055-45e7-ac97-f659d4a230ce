import os
import time
from crewai import Agent, Task, Crew
from crewai_tools import tool

# Custom tools
@tool("SendFileTool")
def send_file(subject: str, body: str) -> str:
    """Simulate sending an email by writing the report to a file."""
    filename = "report.txt"
    with open(filename, "w") as f:
        f.write(f"Subject: {subject}\n\n{body}")
    return f"Report written to {filename}"

@tool("ReadFileTool")
def read_file() -> str:
    """Simulate reading an email reply by reading from a reply file."""
    reply_filename = "report_reply.txt"
    if os.path.exists(reply_filename):
        with open(reply_filename, "r") as f:
            feedback = f.read().strip()
        # Optionally clear the file after reading to simulate marking as read
        # os.remove(reply_filename)
        return feedback
    return "No new feedback received yet."

# Agents
report_writer = Agent(
    role="Report Writer",
    goal="Write and revise a report based on task request and user feedback",
    backstory="You’re an expert writer tasked with creating detailed reports and refining them based on feedback.",
    verbose=True
)

file_handler = Agent(
    role="File Handler",
    goal="Simulate email interactions by writing reports to files and reading feedback",
    backstory="You’re a simulation specialist managing file-based communication.",
    tools=[send_file, read_file],
    verbose=True
)

# Initial task request (example)
task_request = "Write a report on the latest trends in AI agents for 2025."

# Tasks
write_task = Task(
    description=f"Write a report based on this request: {task_request}",
    expected_output="A detailed report in plain text",
    agent=report_writer
)

send_task = Task(
    description="Simulate sending the report by writing it to a file",
    expected_output="Confirmation that the report was written to a file",
    agent=file_handler,
    tools=[send_file],
    context=[write_task],
    human_input=False
)

revise_task = Task(
    description="Revise the report based on the latest user feedback from the reply file",
    expected_output="A revised report in plain text",
    agent=report_writer,
    human_input=True  # Allows for feedback integration
)

# Crew setup
crew = Crew(
    agents=[report_writer, file_handler],
    tasks=[write_task, send_task],
    verbose=2
)

# Main execution with feedback loop
def run_with_feedback_loop():
    # Initial run
    result = crew.kickoff()
    initial_report = write_task.output.raw  # Assuming raw output is accessible
    send_file("Your AI Agent Report", initial_report)

    while True:
        # Wait for feedback (e.g., 10 seconds polling for testing)
        time.sleep(10)
        feedback = read_file()
        
        if "satisfied" in feedback.lower():
            print("User is satisfied. Process complete.")
            break
        elif feedback and "No new feedback" not in feedback:
            # Update revise task with feedback
            revise_task.description = f"Revise the report based on this feedback: {feedback}"
            crew.tasks = [revise_task, send_task]  # Reset tasks for revision
            result = crew.kickoff()
            revised_report = revise_task.output.raw
            send_file("Revised Report", revised_report)
        else:
            print("Waiting for user feedback...")

# Run the process
if __name__ == "__main__":
    run_with_feedback_loop()