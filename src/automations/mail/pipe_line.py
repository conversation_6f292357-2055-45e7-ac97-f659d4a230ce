from automations.mail.mail_thread_extraction import get_next_unprocessed_mail
from engine.team_loader import load_team

def main():
    # Load the team configuration
    team = load_team("automations/mail/agents")
    
    # Get the next unprocessed email
    success, markdown = get_next_unprocessed_mail()
    
    if success:
        # Process the email with the crew
        result = team.kickoff(inputs={"customer_email": markdown})
        print(result)
    else:
        print(markdown)

if __name__ == "__main__":
    main()