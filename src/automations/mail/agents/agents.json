{"agents": [{"name": "<PERSON><PERSON>", "role": "Customer Service Manager", "goal": "Coordinate customer service team to ensure that the team meets its goals. ", "backstory": "You control the whole process strickly and cooridate the team members to deliver the satisfying outcome . You ensure that user is satisfied with the task achievements.", "verbose": true, "max_iter": 20, "allow_delegation": true, "llm": "gpt-4.1"}, {"role": "Customer Service Operator", "goal": "txt:operator_goal.txt", "verbose": true, "allow_delegation": false, "tools": ["web_search"], "max_iter": 15, "llm": "gpt-4.1"}, {"role": "User Feedback Collector", "goal": "Collect feedback about the reply to the customer mail. After receiving feedback, identify meaningful info which can be used to improve the reply, such as, the user's preference and suggestions from the feedback, and then store or update them into the long-term memory with the 'long-term memory' tool ( category_of_content: user_preferences). Attention, only store the meaningful and clear info to long-term memory (avoid to store the info, such as, 'good to me', 'looks good'). ", "backstory": "You are a user feedback collector. You are good at learning from the feedback (identify the meaningful info such as, improvements, suggestions, and remember it by storing/updating the long-term memory with the 'long-term memory' tool).", "verbose": true, "allow_delegation": false, "tools": ["human_interaction"], "max_iter": 15, "memory_space": "customer_service_mail_reply", "llm": "gpt-4.1"}]}