process the mail with following steps:
1. Check if the mail needs to reply. Only the mail coming from the customer with the requests needs to process.
And if you think the mail doesn't need to reply, you can stop the process and ignore the following steps.
2. Identify the language of the main content of the customer's mail.
3. Draft the reply mail (a professional and polite response), and the content should be written in the same language of the original mail's main content and consider on the user perferences.
4. Get the feedback of the mail from user. To do that, you should always provide the whole cotent of your draft reply to the user for feedback with read-friendly format.
5. Revise the reply content according to the user feedback. And the priority of the user feedback is higher than the user preferences.

Continue iterating through steps 4 and 5 until the user explicitly confirms full satisfaction and no further feedback is needed.
You can complete the process only when getting the stop confirmation from user