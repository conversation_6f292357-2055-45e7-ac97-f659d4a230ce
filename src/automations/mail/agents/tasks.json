{"struct": "txt:reply.py", "tasks": [{"description": "Process the given mail according to the instructions. \n Given the mail:\n\n---\n\n{customer_email}", "knowledge": ["txt:instruction.txt"], "expected_output": "After confirmed by user, output the result as: \ndoes_need_to_reply: <Y/N>\n reason_for_no_need_reply:\n<reason_for_no_need_reply>\n\nreply: \n<your_revised_reply>", "agent": "Customer Service Manager", "output_struct": "MailReply", "memory_extraction": {"space": "customer_service_mail_reply", "category": "user_preferences"}}]}