# 邮件主题：Re: IAA ROAS - setup assistance to start UA (KooplyGames)

## 邮件 1：Sun, 22 Jun 2025 12:23:57 +0300 来自 <PERSON><PERSON> <PERSON> <<EMAIL>>

```
Hi,

Sorry for the late reply.

We recently started passing revenue data from our Monetization Mediation
directly through the Mintegral SDK.

I'm trying to verify the data to make sure Mintegral are
receiving ad-revenue data as instructed here - "Mintegral SDK | Mediation
ROAS data postback Tutorial
<https://alidocs.dingtalk.com/i/p/ZR2PmK51081zvpO72AmoVJRLyneA8Xdb?utm_medium=dingdoc_doc_plugin_url&utm_source=dingdoc_doc&dontjump=true>"
but nothing comes up.
so far, we're unable to see any revenue data in either the "Performance
Monitor" or "Cohort Report" in the Mintegral portal.

I would very much appreciate it if you could assist us in this regard so we
can start target ROAS campaigns for our IAA titles with you.

Thanks !
Assi
[image: image.png]
[image: image.png]


On Thu, Jun 12, 2025 at 11:01 AM AppGrowth <<EMAIL>> wrote:

> Hi team,
>
> Thanks for your patience.
> Could you please provide the specific offer name so that we can help to
> optimize?
>
> Sincerely,
> Mintegral Team
>
> <https://www.facebook.com/mintegral.official/>
> <https://www.linkedin.com/company/mintegral-mobilemarketing/mycompany/>
> <https://twitter.com/MintegralInc?lang=en>
> <https://www.youtube.com/channel/UCa08lcTP24YK7PUoM-QCkWg>
>
> This e-mail and any attachment are confidential and may contain trade
> secrets and may also be legally privileged or otherwise protected from
> disclosure. If you have received it in error, you are herewith informed
> about its status. Please notify us immediately by reply e-mail and then
> delete this e-mail and any attachment from your system. You are prohibited
> from making use of or copying this e-mail or any attachment or disclosing
> the contents to any other person.
>
> 本电子邮件及任何附件均属保密性质，可能包含商业秘密，也可能具有法律上的特权或以其他方式不被泄露。如果您收到它的错误通知，系统则会将其状态告知您。请立即回复电子邮件通知我们，然后从您的系统中删除此邮件和任何附件。禁止您使用或复制本电子邮件或任何附件，或向任何人披露相关内容。
>
> ------------------------------------------------------------------
> From:Assi De-Razon <<EMAIL>>
> Send Time:2025年6月9日(周一) 17:42
> To:"Self-service"<<EMAIL>>
> Subject:IAA ROAS - setup assistance to start UA (KooplyGames)
>
> Hello,
>
> I'd like to ask for assistance please.
>
> We'd like to launch and test IAA target ROAS campaigns for our game titles.
>
> As we're using MMP Singular and we did the setup exactly per the
> instructions here
> <https://adv.mintegral.com/doc/en/integration/postbackSetting.html>.
> (please see attached screenshot)
> In addition, we recently also integrated Mintegral SDK to share our
> revenue postback data directly from our monetization mediation partners.
>
> But unfortunately, we're unable to see any Ad-Revenue data in Mintegral
> portal. (checked in the "Cohort Reports")
>
> I've also recently created the following event in the portal (which I
> wasn't aware of) but unsure whether this is correct or not and how to
> validate it ?
> [image: image.png]
>
>
>    - *Our account username is - KooplyGames*
>
>
> We'd appreciate if you could assist us to start UA activity
>
> Thanks
>
> --
> *Assi De-Razon*
> Head of User Acquisition
> M: +972.507.304955 | kooply.com <http://www.kooply.com/>
>
>
```

## <AUTHOR> <EMAIL>

```
Hello,

I'd like to ask for assistance please.

We'd like to launch and test IAA target ROAS campaigns for our game titles.

As we're using MMP Singular and we did the setup exactly per the
instructions here
<https://adv.mintegral.com/doc/en/integration/postbackSetting.html>.
(please see attached screenshot)
In addition, we recently also integrated Mintegral SDK to share our revenue
postback data directly from our monetization mediation partners.

But unfortunately, we're unable to see any Ad-Revenue data in Mintegral
portal. (checked in the "Cohort Reports")

I've also recently created the following event in the portal (which I
wasn't aware of) but unsure whether this is correct or not and how to
validate it ?
[image: image.png]


   - *Our account username is - KooplyGames*


We'd appreciate if you could assist us to start UA activity

Thanks

-- 
*Assi De-Razon*
Head of User Acquisition
M: +972.507.304955 | kooply.com <http://www.kooply.com/>
```

## <AUTHOR> <EMAIL>

```
Hi Team,

Thank you for the information.

Please see my notes below, highlighted in yellow, It would be great to have
your feedback on them:

   1.

   To transmit ad revenue data via the Mintegral SDK, you must use the
   latest SDK versions that support aggregated monetization data transmission:
   iOS v7.4.1 or above, and Android v16.4.81 or above. Detailed version
   requirements can be found in the developer documentation.
   2.

   Developers need to call the correct ROAS methods during integration to
   ensure Mintegral accurately receives the returned data. For specific
   operational guidelines, please refer to the official Mintegral developer
   documentation:
   https://dev.mintegral.com/doc/index.html?file=sdk-m_sdk-ios&lang=en.
     Our developers implemented the *ROAS Unity Plugin Integration Guide *as
   instructed in the following link, would that work as well ? -->
   https://dev.mintegral.com/doc/index.html?file=sdk-m_sdk-unity&lang=en

   3.

   When verifying data on the Mintegral platform, navigate to the
   Performance Monitor, select all campaigns associated with the relevant
   package name, and check the D0 LTV and D0 ROAS dimensions to confirm
   whether revenue data is displayed.
   Currently I see no data under the Performance Monitor tab, but I don't
   know if this is because there are no active campaigns at the moment or
   because the SDK integration is faulty ? (I dont want to activate a
   campaigns before being able to validate as-revenue is properly reported to
   Mintegral, is that correct ?

If no data is displayed in the Performance Monitor, ensure that ad revenue
events are correctly mapped in the event management section. For IAA
applications, ad revenue transmission must be configured instead of relying
solely on paid events.

   5.

   During data verification, compare the D0 LTV data in the Performance
   report with similar data from the mediation monetization platform. A
   reasonable discrepancy range should be within ±5%. If the discrepancy is
   significant, please contact the Mintegral team for assistance.
   6.

   Ensure that the package name has received sufficient event
   transmissions. In similar cases, missing data is often caused by unmapped
   ad revenue events, which need to be configured as Mintegral standard events
   in the Mintegral event management section.  Concerning the Mintegral
   event management, I have set the following as seen in the screenshot below
   but I don't know if this is correct or how / where I can validate this
   setup ??
   [image: image.png]
   7.





On Tue, Jun 24, 2025 at 12:53 PM AppGrowth <<EMAIL>> wrote:

> Hi team,
>
>
> Based on your inquiry and the provided information, here are the accurate
> factual statements regarding the verification of Mintegral SDK ad revenue
> data transmission:
>
>    1.
>
>    To transmit ad revenue data via the Mintegral SDK, you must use the
>    latest SDK versions that support aggregated monetization data transmission:
>    iOS v7.4.1 or above, and Android v16.4.81 or above. Detailed version
>    requirements can be found in the developer documentation.
>    2.
>
>    Developers need to call the correct ROAS methods during integration to
>    ensure Mintegral accurately receives the returned data. For specific
>    operational guidelines, please refer to the official Mintegral developer
>    documentation:
>    https://dev.mintegral.com/doc/index.html?file=sdk-m_sdk-ios&lang=en.
>    3.
>
>    When verifying data on the Mintegral platform, navigate to the
>    Performance Monitor, select all campaigns associated with the relevant
>    package name, and check the D0 LTV and D0 ROAS dimensions to confirm
>    whether revenue data is displayed.
>
> If no data is displayed in the Performance Monitor, ensure that ad revenue
> events are correctly mapped in the event management section. For IAA
> applications, ad revenue transmission must be configured instead of relying
> solely on paid events.
>
>    5.
>
>    During data verification, compare the D0 LTV data in the Performance
>    report with similar data from the mediation monetization platform. A
>    reasonable discrepancy range should be within ±5%. If the discrepancy is
>    significant, please contact the Mintegral team for assistance.
>    6.
>
>    Ensure that the package name has received sufficient event
>    transmissions. In similar cases, missing data is often caused by unmapped
>    ad revenue events, which need to be configured as Mintegral standard events
>    in the Mintegral event management section.
>
> If the issue persists, we will further verify the integration
> configuration or data discrepancies. Alternatively, you can contact the
> Mintegral technical support team via <NAME_EMAIL> for
> additional assistance.
>
> Sincerely,
> Mintegral Team
>
> <https://www.facebook.com/mintegral.official/>
> <https://www.linkedin.com/company/mintegral-mobilemarketing/mycompany/>
> <https://twitter.com/MintegralInc?lang=en>
> <https://www.youtube.com/channel/UCa08lcTP24YK7PUoM-QCkWg>
>
> This e-mail and any attachment are confidential and may contain trade
> secrets and may also be legally privileged or otherwise protected from
> disclosure. If you have received it in error, you are herewith informed
> about its status. Please notify us immediately by reply e-mail and then
> delete this e-mail and any attachment from your system. You are prohibited
> from making use of or copying this e-mail or any attachment or disclosing
> the contents to any other person.
>
> 本电子邮件及任何附件均属保密性质，可能包含商业秘密，也可能具有法律上的特权或以其他方式不被泄露。如果您收到它的错误通知，系统则会将其状态告知您。请立即回复电子邮件通知我们，然后从您的系统中删除此邮件和任何附件。禁止您使用或复制本电子邮件或任何附件，或向任何人披露相关内容。
>
> ------------------------------------------------------------------
> From:Assi De-Razon <<EMAIL>>
> Send Time:2025年6月22日(周日) 17:24
> To:"Self-service"<<EMAIL>>
> Subject:Re: IAA ROAS - setup assistance to start UA (KooplyGames)
>
> Hi,
>
> Sorry for the late reply.
>
> We recently started passing revenue data from our Monetization Mediation
> directly through the Mintegral SDK.
>
> I'm trying to verify the data to make sure Mintegral are
> receiving ad-revenue data as instructed here - "Mintegral SDK | Mediation
> ROAS data postback Tutorial
> <https://alidocs.dingtalk.com/i/p/ZR2PmK51081zvpO72AmoVJRLyneA8Xdb?utm_medium=dingdoc_doc_plugin_url&utm_source=dingdoc_doc&dontjump=true>"
> but nothing comes up.
> so far, we're unable to see any revenue data in either the "Performance
> Monitor" or "Cohort Report" in the Mintegral portal.
>
> I would very much appreciate it if you could assist us in this regard so
> we can start target ROAS campaigns for our IAA titles with you.
>
> Thanks !
> Assi
> [image: image.png]
> [image: image.png]
>
>
> On Thu, Jun 12, 2025 at 11:01 AM AppGrowth <<EMAIL>> wrote:
> Hi team,
>
> Thanks for your patience.
> Could you please provide the specific offer name so that we can help to
> optimize?
>
> Sincerely,
> Mintegral Team
>
> <https://www.facebook.com/mintegral.official/>
> <https://www.linkedin.com/company/mintegral-mobilemarketing/mycompany/>
> <https://twitter.com/MintegralInc?lang=en>
> <https://www.youtube.com/channel/UCa08lcTP24YK7PUoM-QCkWg>
>
> This e-mail and any attachment are confidential and may contain trade
> secrets and may also be legally privileged or otherwise protected from
> disclosure. If you have received it in error, you are herewith informed
> about its status. Please notify us immediately by reply e-mail and then
> delete this e-mail and any attachment from your system. You are prohibited
> from making use of or copying this e-mail or any attachment or disclosing
> the contents to any other person.
>
> 本电子邮件及任何附件均属保密性质，可能包含商业秘密，也可能具有法律上的特权或以其他方式不被泄露。如果您收到它的错误通知，系统则会将其状态告知您。请立即回复电子邮件通知我们，然后从您的系统中删除此邮件和任何附件。禁止您使用或复制本电子邮件或任何附件，或向任何人披露相关内容。
>
> ------------------------------------------------------------------
> From:Assi De-Razon <<EMAIL>>
> Send Time:2025年6月9日(周一) 17:42
> To:"Self-service"<<EMAIL>>
> Subject:IAA ROAS - setup assistance to start UA (KooplyGames)
>
> Hello,
>
> I'd like to ask for assistance please.
>
> We'd like to launch and test IAA target ROAS campaigns for our game titles.
>
> As we're using MMP Singular and we did the setup exactly per the
> instructions here
> <https://adv.mintegral.com/doc/en/integration/postbackSetting.html>.
> (please see attached screenshot)
> In addition, we recently also integrated Mintegral SDK to share our
> revenue postback data directly from our monetization mediation partners.
>
> But unfortunately, we're unable to see any Ad-Revenue data in Mintegral
> portal. (checked in the "Cohort Reports")
>
> I've also recently created the following event in the portal (which I
> wasn't aware of) but unsure whether this is correct or not and how to
> validate it ?
> [image: image.png]
>
>
>    - *Our account username is - KooplyGames*
>
>
> We'd appreciate if you could assist us to start UA activity
>
> Thanks
>
> --
> *Assi De-Razon*
> Head of User Acquisition
> M: +972.507.304955 | kooply.com <http://www.kooply.com/>
>
>
```

## <AUTHOR> <EMAIL>

```
Hi Team,

Thank you for the information.

Please see my notes below, highlighted in yellow, It would be great to have
your feedback on them:

   1.

   To transmit ad revenue data via the Mintegral SDK, you must use the
   latest SDK versions that support aggregated monetization data transmission:
   iOS v7.4.1 or above, and Android v16.4.81 or above. Detailed version
   requirements can be found in the developer documentation.
   2.

   Developers need to call the correct ROAS methods during integration to
   ensure Mintegral accurately receives the returned data. For specific
   operational guidelines, please refer to the official Mintegral developer
   documentation:
   https://dev.mintegral.com/doc/index.html?file=sdk-m_sdk-ios&lang=en.
     Our developers implemented the *ROAS Unity Plugin Integration Guide *as
   instructed in the following link, would that work as well ? -->
   https://dev.mintegral.com/doc/index.html?file=sdk-m_sdk-unity&lang=en

   3.

   When verifying data on the Mintegral platform, navigate to the
   Performance Monitor, select all campaigns associated with the relevant
   package name, and check the D0 LTV and D0 ROAS dimensions to confirm
   whether revenue data is displayed.
   Currently I see no data under the Performance Monitor tab, but I don't
   know if this is because there are no active campaigns at the moment or
   because the SDK integration is faulty ? (I dont want to activate a
   campaigns before being able to validate as-revenue is properly reported to
   Mintegral, is that correct ?

If no data is displayed in the Performance Monitor, ensure that ad revenue
events are correctly mapped in the event management section. For IAA
applications, ad revenue transmission must be configured instead of relying
solely on paid events.

   5.

   During data verification, compare the D0 LTV data in the Performance
   report with similar data from the mediation monetization platform. A
   reasonable discrepancy range should be within ±5%. If the discrepancy is
   significant, please contact the Mintegral team for assistance.
   6.

   Ensure that the package name has received sufficient event
   transmissions. In similar cases, missing data is often caused by unmapped
   ad revenue events, which need to be configured as Mintegral standard events
   in the Mintegral event management section.  Concerning the Mintegral
   event management, I have set the following as seen in the screenshot below
   but I don't know if this is correct or how / where I can validate this
   setup ??
   [image: image.png]
   7.





On Tue, Jun 24, 2025 at 12:53 PM AppGrowth <<EMAIL>> wrote:

> Hi team,
>
>
> Based on your inquiry and the provided information, here are the accurate
> factual statements regarding the verification of Mintegral SDK ad revenue
> data transmission:
>
>    1.
>
>    To transmit ad revenue data via the Mintegral SDK, you must use the
>    latest SDK versions that support aggregated monetization data transmission:
>    iOS v7.4.1 or above, and Android v16.4.81 or above. Detailed version
>    requirements can be found in the developer documentation.
>    2.
>
>    Developers need to call the correct ROAS methods during integration to
>    ensure Mintegral accurately receives the returned data. For specific
>    operational guidelines, please refer to the official Mintegral developer
>    documentation:
>    https://dev.mintegral.com/doc/index.html?file=sdk-m_sdk-ios&lang=en.
>    3.
>
>    When verifying data on the Mintegral platform, navigate to the
>    Performance Monitor, select all campaigns associated with the relevant
>    package name, and check the D0 LTV and D0 ROAS dimensions to confirm
>    whether revenue data is displayed.
>
> If no data is displayed in the Performance Monitor, ensure that ad revenue
> events are correctly mapped in the event management section. For IAA
> applications, ad revenue transmission must be configured instead of relying
> solely on paid events.
>
>    5.
>
>    During data verification, compare the D0 LTV data in the Performance
>    report with similar data from the mediation monetization platform. A
>    reasonable discrepancy range should be within ±5%. If the discrepancy is
>    significant, please contact the Mintegral team for assistance.
>    6.
>
>    Ensure that the package name has received sufficient event
>    transmissions. In similar cases, missing data is often caused by unmapped
>    ad revenue events, which need to be configured as Mintegral standard events
>    in the Mintegral event management section.
>
> If the issue persists, we will further verify the integration
> configuration or data discrepancies. Alternatively, you can contact the
> Mintegral technical support team via <NAME_EMAIL> for
> additional assistance.
>
> Sincerely,
> Mintegral Team
>
> <https://www.facebook.com/mintegral.official/>
> <https://www.linkedin.com/company/mintegral-mobilemarketing/mycompany/>
> <https://twitter.com/MintegralInc?lang=en>
> <https://www.youtube.com/channel/UCa08lcTP24YK7PUoM-QCkWg>
>
> This e-mail and any attachment are confidential and may contain trade
> secrets and may also be legally privileged or otherwise protected from
> disclosure. If you have received it in error, you are herewith informed
> about its status. Please notify us immediately by reply e-mail and then
> delete this e-mail and any attachment from your system. You are prohibited
> from making use of or copying this e-mail or any attachment or disclosing
> the contents to any other person.
>
> 本电子邮件及任何附件均属保密性质，可能包含商业秘密，也可能具有法律上的特权或以其他方式不被泄露。如果您收到它的错误通知，系统则会将其状态告知您。请立即回复电子邮件通知我们，然后从您的系统中删除此邮件和任何附件。禁止您使用或复制本电子邮件或任何附件，或向任何人披露相关内容。
>
> ------------------------------------------------------------------
> From:Assi De-Razon <<EMAIL>>
> Send Time:2025年6月22日(周日) 17:24
> To:"Self-service"<<EMAIL>>
> Subject:Re: IAA ROAS - setup assistance to start UA (KooplyGames)
>
> Hi,
>
> Sorry for the late reply.
>
> We recently started passing revenue data from our Monetization Mediation
> directly through the Mintegral SDK.
>
> I'm trying to verify the data to make sure Mintegral are
> receiving ad-revenue data as instructed here - "Mintegral SDK | Mediation
> ROAS data postback Tutorial
> <https://alidocs.dingtalk.com/i/p/ZR2PmK51081zvpO72AmoVJRLyneA8Xdb?utm_medium=dingdoc_doc_plugin_url&utm_source=dingdoc_doc&dontjump=true>"
> but nothing comes up.
> so far, we're unable to see any revenue data in either the "Performance
> Monitor" or "Cohort Report" in the Mintegral portal.
>
> I would very much appreciate it if you could assist us in this regard so
> we can start target ROAS campaigns for our IAA titles with you.
>
> Thanks !
> Assi
> [image: image.png]
> [image: image.png]
>
>
> On Thu, Jun 12, 2025 at 11:01 AM AppGrowth <<EMAIL>> wrote:
> Hi team,
>
> Thanks for your patience.
> Could you please provide the specific offer name so that we can help to
> optimize?
>
> Sincerely,
> Mintegral Team
>
> <https://www.facebook.com/mintegral.official/>
> <https://www.linkedin.com/company/mintegral-mobilemarketing/mycompany/>
> <https://twitter.com/MintegralInc?lang=en>
> <https://www.youtube.com/channel/UCa08lcTP24YK7PUoM-QCkWg>
>
> This e-mail and any attachment are confidential and may contain trade
> secrets and may also be legally privileged or otherwise protected from
> disclosure. If you have received it in error, you are herewith informed
> about its status. Please notify us immediately by reply e-mail and then
> delete this e-mail and any attachment from your system. You are prohibited
> from making use of or copying this e-mail or any attachment or disclosing
> the contents to any other person.
>
> 本电子邮件及任何附件均属保密性质，可能包含商业秘密，也可能具有法律上的特权或以其他方式不被泄露。如果您收到它的错误通知，系统则会将其状态告知您。请立即回复电子邮件通知我们，然后从您的系统中删除此邮件和任何附件。禁止您使用或复制本电子邮件或任何附件，或向任何人披露相关内容。
>
> ------------------------------------------------------------------
> From:Assi De-Razon <<EMAIL>>
> Send Time:2025年6月9日(周一) 17:42
> To:"Self-service"<<EMAIL>>
> Subject:IAA ROAS - setup assistance to start UA (KooplyGames)
>
> Hello,
>
> I'd like to ask for assistance please.
>
> We'd like to launch and test IAA target ROAS campaigns for our game titles.
>
> As we're using MMP Singular and we did the setup exactly per the
> instructions here
> <https://adv.mintegral.com/doc/en/integration/postbackSetting.html>.
> (please see attached screenshot)
> In addition, we recently also integrated Mintegral SDK to share our
> revenue postback data directly from our monetization mediation partners.
>
> But unfortunately, we're unable to see any Ad-Revenue data in Mintegral
> portal. (checked in the "Cohort Reports")
>
> I've also recently created the following event in the portal (which I
> wasn't aware of) but unsure whether this is correct or not and how to
> validate it ?
> [image: image.png]
>
>
>    - *Our account username is - KooplyGames*
>
>
> We'd appreciate if you could assist us to start UA activity
>
> Thanks
>
> --
> *Assi De-Razon*
> Head of User Acquisition
> M: +972.507.304955 | kooply.com <http://www.kooply.com/>
>
>
```