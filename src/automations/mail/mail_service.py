import imaplib
import email
from email.header import decode_header
from collections import deque
import os
import re
import tempfile
import json
from datetime import datetime
from PyPDF2 import PdfReader
from docx import Document
from flask import Flask, request, jsonify, Response
from mail_thread_extraction import get_next_unprocessed_mail
import hashlib

magics = {
    "1234":"1234",
    "14592":"14592"
}
def get_magic(usr_id: str) -> str:
    """获取 magic 字符串"""
    print("user_id:", usr_id)
    magic = magics.get(usr_id)
    if magic:
        return magic
    else:
        return "default"
        # raise ValueError(f"No magic string found for user_id: {usr_id}")

app = Flask(__name__)

# ---------- VCode Validation ----------

def validate_vcode(magic: str, body: dict, vcode: str) -> bool:
    """计算 magic + body 的 JSON 字符串的 SHA256，并与 vcode 比较"""
    try:
        payload_str = json.dumps(body, separators=(',', ':'), sort_keys=True)  # 保证稳定的 JSON 结构
        base_string = magic + payload_str
        computed_hash = hashlib.sha256(base_string.encode('utf-8')).hexdigest()
        return computed_hash == vcode
    except Exception as e:
        return False

# ---------- HTTP Endpoint ----------

@app.route('/get_next_mail', methods=['POST'])
def next_mail():
    user_id = request.headers.get('userid', 'default')
    magic = get_magic(user_id)
    if not magic:
        return Response("Invalid user_id", status=400, mimetype='text/plain')
    vcode = request.headers.get('vcode', '')

    try:
        data = request.json or {}
    except Exception as e:
        return Response(f"Invalid JSON payload: {str(e)}", status=400, mimetype='text/plain')

    # 校验 vcode
    # if not validate_vcode(magic, data, vcode):
    #     return Response("Invalid parameters: VCode mismatch", status=400, mimetype='text/plain')

    search_criteria = data.get('search_criteria', 'ALL')
    success, content = get_next_unprocessed_mail(user_id, search_criteria)

    if success:
        response = Response(content, status=200, mimetype='text/plain')
        response.headers['path-tag'] = 'success'
        return response
    else:
        response = Response(content, status=404, mimetype='text/plain')
        response.headers['path-tag'] = 'not_found'
        return response

if __name__ == '__main__':
    app.run(host='0.0.0.0', port=5100)
