{"tasks": [{"description": "Process the given mail according to the instructions. \n Given the mail:\n\n---\n\n{customer_email}", "knowledge": ["txt:instruction1.txt"], "expected_output": "output the result as the following  format: \ndoes_need_to_reply: <Y/N>\n reason_for_no_need_reply:\n<reason_for_no_need_reply>\n\n", "agent": "Customer Service Operator"}, {"description": "Follow the conclusion of the previous task. Write the reply to customer. ", "knowledge": ["txt:instruction2.txt"], "expected_output": "After receiving confirmation of the user's satisfaction, output the result as the following  format: \ndoes_need_to_reply: <Y/N>\n reason_for_no_need_reply:\n<reason_for_no_need_reply>\n\n", "agent": "Customer Service Manager", "memory_extraction": {"space": "customer_service_mail_reply", "category": "user_preference"}}]}