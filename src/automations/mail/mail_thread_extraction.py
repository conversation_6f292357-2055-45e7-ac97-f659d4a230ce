
import imaplib
import email
from email.header import decode_header
from collections import deque
import os
import re
import tempfile
import json
from datetime import datetime
from PyPDF2 import PdfReader
from docx import Document

# ---------- 配置 ----------
EMAIL = os.environ["MAIL_BOX"]
PASSWORD = os.environ["MB_PASS"]
IMAP_SERVER = "imap.qiye.aliyun.com"
IMAP_PORT = 993
MAILBOX = "INBOX"
OUTPUT_FILE = "email_thread.md"
ATTACHMENT_TYPES = ('.pdf', '.txt', '.docx')
PROCESSED_EMAILS_FILE = "_processed_emails.json"  # File to store processed email IDs

# ---------- 帮助函数 ----------

def load_processed_emails(user_id):
    """Load the list of already processed email IDs from file."""
    if os.path.exists(user_id+PROCESSED_EMAILS_FILE):
        with open(user_id+PROCESSED_EMAILS_FILE, 'r') as f:
            try:
                return json.load(f)
            except json.JSONDecodeError:
                return {"processed_ids": []}
    return {"processed_ids": []}

def save_processed_email(user_id, email_id, message_id):
    """Save the email ID as processed."""
    data = load_processed_emails(user_id)
    data["processed_ids"].append({
        "email_id": email_id.decode() if isinstance(email_id, bytes) else str(email_id),
        "message_id": message_id,
        "processed_at": datetime.now().isoformat()
    })
    with open(user_id + PROCESSED_EMAILS_FILE, 'w') as f:
        json.dump(data, f, indent=2)

def is_email_processed(user_id,email_id, message_id):
    """Check if the email has already been processed."""
    data = load_processed_emails(user_id)
    email_id_str = email_id.decode() if isinstance(email_id, bytes) else str(email_id)
    
    for item in data["processed_ids"]:
        if item["email_id"] == email_id_str or item["message_id"] == message_id:
            return True
    return False

def clean_subject(subject):
    if subject is None:
        return "无主题"
    if isinstance(subject, bytes):
        subject = subject.decode(errors='ignore')
    
    decoded_parts = decode_header(subject)
    subject_text = ""
    
    for part, encoding in decoded_parts:
        # Handle the part based on its type and encoding
        if isinstance(part, bytes):
            try:
                # First try with the specified encoding
                if encoding:
                    decoded_part = part.decode(encoding, errors='replace')
                else:
                    # Try utf-8 first, then fall back to others
                    try:
                        decoded_part = part.decode('utf-8', errors='strict')
                    except UnicodeDecodeError:
                        try:
                            decoded_part = part.decode('gb2312', errors='replace')
                        except UnicodeDecodeError:
                            decoded_part = part.decode('latin1', errors='replace')
            except (LookupError, UnicodeDecodeError):
                # If specified encoding fails or doesn't exist, use a fallback
                decoded_part = part.decode('utf-8', errors='replace')
            subject_text += decoded_part
        else:
            # If it's already a string
            subject_text += part
    
    return subject_text.strip()

def fetch_email_by_id(mailbox, mail_id):
    result, data = mailbox.fetch(mail_id, "(RFC822)")
    if result != "OK":
        return None
    raw_email = data[0][1]
    return email.message_from_bytes(raw_email)

def find_thread_emails(mailbox, initial_msg):
    msg_id = initial_msg.get("Message-ID")
    refs = initial_msg.get("References", "").split()
    in_reply_to = initial_msg.get("In-Reply-To")
    
    id_queue = deque(refs + ([in_reply_to] if in_reply_to else []))
    seen = set()
    thread = [initial_msg]

    while id_queue:
        ref_id = id_queue.popleft()
        if not ref_id or ref_id in seen:
            continue
        seen.add(ref_id)
        
        search_criteria = f'(HEADER Message-ID "{ref_id.strip()}")'
        result, data = mailbox.search(None, search_criteria)
        if result != "OK" or not data[0]:
            continue
        msg_ids = data[0].split()
        for msg_id in msg_ids:
            msg = fetch_email_by_id(mailbox, msg_id)
            if msg:
                thread.insert(0, msg)
                if msg.get("In-Reply-To"):
                    id_queue.append(msg.get("In-Reply-To"))
    return thread

def extract_text_body(msg):
    if msg.is_multipart():
        for part in msg.walk():
            ctype = part.get_content_type()
            disp = str(part.get("Content-Disposition"))
            if ctype == "text/plain" and "attachment" not in disp:
                charset = part.get_content_charset() or 'utf-8'
                return part.get_payload(decode=True).decode(charset, errors="ignore")
    else:
        charset = msg.get_content_charset() or 'utf-8'
        return msg.get_payload(decode=True).decode(charset, errors="ignore")
    return "(无正文内容)"

def extract_attachment_text(part, filename):
    ext = os.path.splitext(filename)[1].lower()
    try:
        with tempfile.NamedTemporaryFile(delete=False, suffix=ext) as tmp_file:
            tmp_file.write(part.get_payload(decode=True))
            tmp_file_path = tmp_file.name

        if ext == ".pdf":
            with open(tmp_file_path, 'rb') as f:
                reader = PdfReader(f)
                return "\n".join(page.extract_text() or "" for page in reader.pages)
        elif ext == ".txt":
            with open(tmp_file_path, 'r', encoding='utf-8', errors='ignore') as f:
                return f.read()
        elif ext == ".docx":
            doc = Document(tmp_file_path)
            return "\n".join(p.text for p in doc.paragraphs)
    except Exception as e:
        return f"(读取附件失败: {e})"
    finally:
        if os.path.exists(tmp_file_path):
            os.unlink(tmp_file_path)
    return "(附件内容为空或不支持)"

def extract_attachments(msg):
    attachments = []
    for part in msg.walk():
        if part.get_content_disposition() == 'attachment':
            filename = part.get_filename()
            if filename:
                filename = decode_header(filename)[0][0]
                if isinstance(filename, bytes):
                    filename = filename.decode('utf-8', errors='ignore')
                if filename.lower().endswith(ATTACHMENT_TYPES):
                    content = extract_attachment_text(part, filename)
                    attachments.append((filename, content))
    return attachments

def generate_markdown(thread):
    if not thread:
        return "# 无邮件线程可生成。"
    
    subject = clean_subject(thread[-1].get("Subject"))
    md = f"# 邮件主题：{subject}\n\n"

    for idx, msg in enumerate(thread, 1):
        from_ = msg.get("From", "未知")
        date = msg.get("Date", "未知")
        body = extract_text_body(msg).strip()
        body = re.sub(r"\r\n|\r", "\n", body)

        md += f"## 邮件 {idx}：{date} 来自 {from_}\n\n"
        md += f"```\n{body}\n```\n\n"

        attachments = extract_attachments(msg)
        for fname, content in attachments:
            content = content.strip()
            content = re.sub(r"\r\n|\r", "\n", content)
            md += f"### 📎 附件：{fname}\n\n```\n{content}\n```\n\n"

    return md.strip()

# ---------- 主程序 ----------

def get_next_unprocessed_mail(user_id, search_criteria='ALL' ):
    # search_criteria , 'SINCE "01-Apr-2025"'
    with imaplib.IMAP4_SSL(IMAP_SERVER, IMAP_PORT) as mail:
        mail.login(EMAIL, PASSWORD)
        mail.select(MAILBOX)
        
        result, data = mail.search(None, 'SINCE "01-Apr-2025"')
        if result != "OK":
            print("Failed to search emails.")
            return False, "Failed to search emails."
        
        mail_ids = data[0].split()
        if not mail_ids:
            print("没有邮件。")
            return False, "No mail found."

        # Process the newest emails first
        for mail_id in reversed(mail_ids):
            email_obj = fetch_email_by_id(mail, mail_id)
            if email_obj:
                message_id = email_obj.get("Message-ID", "")
                
                # Skip if already processed
                if is_email_processed(user_id, mail_id, message_id):
                    # print(f"跳过已处理的邮件: {message_id}")
                    continue
                
                thread_emails = find_thread_emails(mail, email_obj)
                thread_emails.append(email_obj)
                
                markdown = generate_markdown(thread_emails)
                
                with open(OUTPUT_FILE, "w", encoding="utf-8") as f:
                    f.write(markdown)
                print(f"✅ Import the mail thead to Markdown: {OUTPUT_FILE}")
                
                # Mark as processed
                save_processed_email(user_id, mail_id, message_id)
                return True, markdown
                # Process only the first unprocessed email
                #print(load_team("automations/mail/agents").kickoff(inputs={"customer_email":markdown}))
               
        else:
            print("No new mail to process.")
            return False, "No new mail to process."


