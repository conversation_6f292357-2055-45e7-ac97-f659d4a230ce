import requests
import json
import sys
import os

def get_next_mail(user_id, search_criteria="ALL", base_url="http://localhost:5100"):
    """
    Call the email service to get the next unprocessed email.
    
    Args:
        user_id (str): User ID for tracking processed emails
        search_criteria (str): IMAP search criteria string
        base_url (str): Base URL of the email service
        
    Returns:
        tuple: (success, result) where success is a boolean and result is either the email content
               or an error message
    """
    headers = {
        "user_id": user_id,
        "Content-Type": "application/json"
    }
    
    payload = {
        "search_criteria": search_criteria
    }
    
    try:
        response = requests.post(
            f"{base_url}/get_next_mail", 
            headers=headers,
            json=payload
        )
        
        # Parse response
        response_json = response.json()
        
        if response.status_code == 200:
            return True, response_json.get("customer_email", "")
        else:
            error_msg = response_json.get("error", "Unknown error")
            return False, error_msg
            
    except requests.exceptions.RequestException as e:
        return False, f"Connection error: {str(e)}"
    except ValueError as e:
        return False, f"Invalid response format: {str(e)}"
    except Exception as e:
        return False, f"Unexpected error: {str(e)}"

def main():
    """Command line interface for the email client."""
    
    # Get parameters from command line or use defaults
    user_id = os.environ.get("EMAIL_USER_ID", "default_user")
    search_criteria = "SINCE \"01-Apr-2023\"" if len(sys.argv) <= 1 else sys.argv[1]
    base_url = os.environ.get("EMAIL_SERVICE_URL", "http://localhost:5000")
    
    print(f"Fetching emails for user: {user_id}")
    print(f"Search criteria: {search_criteria}")
    print(f"Service URL: {base_url}")
    
    # Call the service
    success, result = get_next_mail(user_id, search_criteria, base_url)
    
    if success:
        print("\n=== EMAIL CONTENT ===\n")
        print(result)
        
        # Save to file if requested
        if len(sys.argv) > 2 and sys.argv[2] == "--save":
            filename = f"email_{user_id}_{int(time.time())}.md"
            with open(filename, "w", encoding="utf-8") as f:
                f.write(result)
            print(f"\nEmail saved to {filename}")
    else:
        print(f"\nError: {result}")
        sys.exit(1)

# Simple usage example
if __name__ == "__main__":
    # Example: Use the function directly
    user_id = "test_user123"
    search_criteria = "SINCE \"01-Jan-2023\""
    
    # Command line usage
    if len(sys.argv) > 1 and sys.argv[1] == "--cli":
        main()
    else:
        # Direct function call example
        success, result = get_next_mail(user_id, search_criteria)
        
        if success:
            print("Successfully retrieved email:")
            print(result[:200] + "..." if len(result) > 200 else result)  # Show preview
        else:
            print(f"Error: {result}")
