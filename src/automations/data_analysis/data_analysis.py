from crewai import Agent, Task, Crew, Process
from engine.prebuilt_llms import prebuilt_llms
from tools.python_repl import code_interpreter as code_interpreter_tool
# Initialize the tool

llm = prebuilt_llms.get("gpt-4.1")()
# Define an agent that uses the tool
data_analyst = Agent(
    role="Data Analyst",
    goal="Analyze data using Python code",
    backstory="""You are an expert data analyst who specializes in using Python 
    to analyze and visualize data. You can write efficient code to process 
    large datasets and extract meaningful insights.""",
    tools=[code_interpreter_tool],
    verbose=True,
    llm = llm
)

# Create a task for the agent
analysis_task = Task(
    description="""
    Write Python code to:
    1. Generate a random data set of 100 x and y coordinate points
2. Calculate the correlation coefficient between x and y
3. Create a heat map of the data
4. Print the correlation coefficient and save the plot as a "scatter.png" file

Make sure to process all necessary imports and print the results.
   
    """,
    expected_output="The correlation coefficient and confirmation that the scatter plot has been saved.",
    agent=data_analyst,
)

# Run the task
crew = Crew(
    agents=[data_analyst],
    tasks=[analysis_task],
    verbose=True,
    process=Process.sequential,
)
result = crew.kickoff()