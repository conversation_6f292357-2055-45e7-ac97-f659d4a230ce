{"tasks": [{"name": "run_transaction", "description": "Try to use the tool to create a transaction", "expected_output": "output the result", "agent": "AI Assistant", "tools": ["mcp:some_streamable_tool"], "async_execution": true}, {"description": "[[txt:task.txt]]", "expected_output": "The result of the calculation", "agent": "AI Assistant", "tools": ["mcp:math_server"], "dependent_tasks": ["run_transaction"]}]}