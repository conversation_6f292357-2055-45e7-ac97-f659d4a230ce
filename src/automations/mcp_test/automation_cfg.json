{"nodes": [{"name": "MCP Test", "type": "agent", "config": {"config_path": "automations/mcp_test/agents", "params": [{"param": {"name": "video_link", "value": "https://www.youtube.com/watch?v=Yq0QkCxoTHM"}}]}, "next_node": "Python Test"}, {"name": "Python Test", "type": "python", "config": {"code_file_path": "automations/mcp_test/python_test.py", "result_var": "result", "path_tag_var": "path_tag", "params": [{"param": {"name": "init_value", "value": "{MCP Test}"}}]}}]}