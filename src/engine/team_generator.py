from crewai import Agent, Task, Crew
from engine.prebuilt_llms import prebuilt_llms

def get_tools_definitions():
    return """
    tool,description
google_search,Google Search
asknews,"AskNews是一款新闻增强工具,每日处理30万篇文章,提供自然语言查询接口。它翻译、总结、提取实体,索引到向量数据库,覆盖多国多语言,注重透明度。"
wikipedia,维基百科 是一个多语言的自由在线百科全书，由志愿者社区（称为维基人）通过开放协作和使用名为 MediaWiki 的 wiki 编辑系统编写和维护。 Wikipedia 是历史上规模最大、阅读量最多的参考作品。
arxiv,arXiv是一个收集物理学、数学、计算机科学、生物学与数理经济学的论文预印本的网站，由美国康奈尔大学维护。
human_feed_back,接收用户反馈
scrape_website_tool,Read website content
serper_dev_tool,Search the internet with Serper
safe_code_interpreter,python 代码解释器
mcp:mtg_data_query_tool,MTG 数据查询McpSse Tool
vision_tool,describe_image_with_gemini
mcp:mintegral_demand_portal_tool,MTG demand portal MCP 
mcp:servicego-api-mcp,组件化中台的mcp server
upload_file_to_s3,上传文件到s3
mcp:smartcut_tool,smartcut_tool
file_reader,"Read the content of a file and return it as a string.input is the file path or URL, and the output is a plain text string."
mcp:mtg_moss,moss of the mtg reporting system
    """

class TeamGenerator:
    
    def __init__(self, llm: str):
        
        self.Examples="""
            {
            "name": "Collaborative Tech Report Generation System",
            "agents": [
                {
                    "role": "Project Manager",
                    "goal": "Coordinate your team members to complete the tasks and make the user satisfied",
                    "backstory": "As a Project Manager at a tech company, your role is to ensure task deliverables meet user expectations. Always understand the tasks deeply, plan them and delegate the task/step to the right coworkers. Confirm the task achievements with the user (get the user's feedback) to ensure user satisfaction before completing the tasks.",
                    "verbose": true,
                    "allow_delegation": true,
                    "max_iter": 15,
                    "llm": "gpt-4.1"
                },
                {
                    "role": "Senior Research Writer",
                    "goal": "Uncover cutting-edge developments in AI and data science and write a high quality tech report/paper according to the requirements",
                    "backstory": "You are a Senior Research Analyst at a leading tech think tank. Your expertise lies in identifying emerging trends and technologies in AI and data science. You have a knack for dissecting complex data and presenting actionable insights. Write the tech report with your knowledge about the format and structure of a technical report",
                    "verbose": true,
                    "allow_delegation": false,
                    "max_iter": 15,
                    "llm": "gpt-4.1"
                },
                {
                    "role": "User Feedback Collector",
                    "goal": "Collect user feedback about the generated report and after receiving feedback, identify meaningful improvement suggestions  and store or update them into the long-term memory with the 'long-term memory' tool ( category_of_content: user_preferences). Provide the user feedback to the coworker correctly",
                    "backstory": "You are a user feedback collector. You are good at learning from the feedback (identify the meaningful info and remember it by storing/updating the long-term memory with the 'long-term memory' tool).",
                    "verbose": true,
                    "allow_delegation": false,
                    "tools": [
                        "human_feed_back"
                    ],
                    "memory_space": "report_creation",
                    "max_iter": 15,
                    "llm": "gpt-4.1"
                }
            ],
            "tasks": [
                {
                    "name": "Write a report",
                    "description": "Conduct a comprehensive analysis of \\{topic\\}. \nCompile the findings in a report.\nMake sure to check with the user if the report is good before finalizing your answer. \nAttention, only complete the task when getting the satisfaction from user.\nPlease, follow the steps:  \nThe following steps are to complete a user satisfied task:\n1. Write the draft report on the latest advancements. \n2. Get the feedback from the user about the report. \n3. Revise the report according to  the user feedback. \nRepeat the step 2 and 3. \nYou can only stop the process when getting the stop confirmation from user.",
                    "expected_output": "A comprehensive full report with markdwon formatting.",
                    "agent": "Project Manager",
                    "tools": [
                        "google_search"
                    ],
                    "output_file": "report.md"
                }
            ]
        }

    """
        self.llm = llm
        self.ai_agent_expert = Agent(
            role="multi-agent solution designer",
            goal="output the agent and task design for the multi-agent solution with the platform's configuration.",
            backstory="""
            You are an expert in AI Agent, especially, multi-agent solution. 
            You can design agent roles, goals, and backstories to achieve the best performance in multi-agent collaboration.
            And you can define the tasks and processes for the agents to work together effectively.
            """,
            verbose=True,
            allow_delegation=False,
            max_iter=5,
            memory=True,
            multimodal=False,
        )

        self.team_design_task = Task(
            description="""
            Design a multi-agent solution for the given requirement: {requirement}. 

            Now, we are using the MaxAgent platform, an advanced multi-agent environment that enables teams of specialized agents to collaborate on complex tasks.
        """
        +
        """
        The following is the tools can be assigned to the agent/task
         """
        +  get_tools_definitions() +
        """The following is an example about utilizing specialized agents to complete the user's task based on MaxAgent.
        The given task is about to write an AI research report according to the setting topic. And the generation process need to work with the user for revising according to the user's feedback util getting user's finalizing confirmation.
        The following is the configurations to leverage MaxAgent to complete the task. \n""" 

        + self.Examples ,   
            expected_output="The agent and task design for the multi-agent configuration for MaxAgent (only the JSON string).",
            agent=self.ai_agent_expert,
        )

    def generate_team(self, requirement: str) -> str:
        """
        Generate the team design for the given requirement.
        """
        self.ai_agent_expert.llm = prebuilt_llms.get(self.llm)()
        crew = Crew(
            agents=[self.ai_agent_expert],
            tasks=[self.team_design_task], 
            verbose=1,
        )
        return crew.kickoff(inputs={"requirement":requirement})

#team_generator = TeamGenerator("gpt-4.1")
#print(team_generator.generate_team("Design a IAP ROAS model for Programmatic Advertising to pursue the higher ROI."))  
