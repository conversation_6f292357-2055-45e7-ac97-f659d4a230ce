import os
import re
import PyPDF2
import docx
import requests
import time
import html2text
from io import BytesIO
import pdfplumber
import docx2txt
import pptx
from datetime import datetime
from tools.memory import MemExtractingTool
from dotenv import load_dotenv

def read_file_as_string(file_path) -> str:
    try:
        with open(file_path, 'r', encoding='utf-8') as file:
            content = file.read()
            return content
    except FileNotFoundError:
        print(f"File '{file_path}' not found.")
    except Exception as e:
        print(f"An error occurred: {e}")

def get_first_n_lines(input_string, n):
    lines = input_string.splitlines()
    return "\n".join(lines[:n])

def read_pdf_as_string(file_path) -> str:
    try:
       
        with open(file_path, 'rb') as file:
            reader = PyPDF2.PdfReader(file)
            text = ""
            for page in reader.pages:
                text += page.extract_text() + "\n\n"
            return text
    except FileNotFoundError:
        print(f"File '{file_path}' not found.")
    except Exception as e:
        print(f"An error occurred: {e}")

def read_docx_as_string(file_path) -> str:
    try:
     
        doc = docx.Document(file_path)
        text = []
        for paragraph in doc.paragraphs:
            text.append(paragraph.text)
        return "\n".join(text)
    except FileNotFoundError:
        print(f"File '{file_path}' not found.")
    except Exception as e:
        print(f"An error occurred: {e}")

def read_url_as_string(url) -> str:
    try:
        
        response = requests.get(url)
        if response.status_code == 200:
            return response.text
        else:
            print(f"Failed to retrieve URL: {url}")
    except Exception as e:
        print(f"An error occurred: {e}")

def load_content_from_memory(user_id, space, category):
    """
    Load content from memory based on space and category.
    
    Args:
        space (str): Memory space
        category (str): Memory category
    
    Returns:
        str: Content loaded from memory
    """
    # Placeholder for actual memory loading logic
    # This should be replaced with the actual implementation to load content from memory
    extractor = MemExtractingTool(user_id, space,category)
    try:
        memory = extractor.extract()

        return memory
    except Exception as e:
         print(f"Error extracting memory: {e}")


def get_contents_by_prefix(user_id, config_path, input_string:str) -> str:
    # According to the input string's load the contents from the different media;
    # For example, if the input string starts with "txt:", then read the file content
    # from the file path mentioned after "txt:".
    # If the input string starts with "url:", then scrape the content from the URL
    if input_string.startswith("txt:"):
        file_path = input_string[4:].strip()
        input_string = read_file_as_string(config_path+"/"+file_path)
    elif input_string.startswith("pdf:"):
        file_path = input_string[4:].strip()
        input_string = read_pdf_as_string(config_path+"/"+file_path)
    elif input_string.startswith("doc:"):
        file_path = input_string[4:].strip()
        input_string = read_docx_as_string(config_path+"/"+file_path)
    elif input_string.startswith("url:"):
        url = input_string[4:].strip()
        input_string = url_to_markdown(url)
    elif input_string.startswith("xlsx:"):
        file_path = input_string[5:].strip()
        input_string = excel_to_csv_string(config_path+"/"+file_path)
    elif input_string.startswith("mem:"):
        mem_path = input_string[4:].strip()
        path_parts = mem_path.split(".")
        if len(path_parts) != 2:
            raise ValueError("Invalid memory path format. Expected 'space.category'")
        input_string = load_content_from_memory(user_id, path_parts[0], path_parts[1])
    return input_string


# 加载 .env 文件
dotenv_path = os.getenv('DOT_PATH', ".env")
if not load_dotenv(dotenv_path, override=True):
    raise ValueError(f"无法加载环境变量文件: {dotenv_path}")

embeddings = {
                    "provider": "azure",
                    "config": {
                        "model": "text-embedding-3-small",
                        "api_version": "2024-12-01-preview",
                        "deployment_id": "text-embedding-3-small",
                        "api_base": os.environ["AZURE_OPENAI_ENDPOINT"],
                        "api_key": os.environ["AZURE_OPENAI_API_KEY"],
                    
                    }
     }
#  api_key=config.get("api_key"),
#             api_base=config.get("api_base"),
#             api_type=config.get("api_type", "azure"),
#             api_version=config.get("api_version"),
#             model_name=model_name,
#             default_headers=config.get("default_headers"),
#             dimensions=config.get("dimensions"),
#             deployment_id=config.get("deployment_id"),
#             organization_id=config.get("organization_id"),

def process_description_with_knowledge(input_text, process_knowledge, max_depth=10):
    """
    递归处理输入文本，替换所有由 {} 包裹的内容
    
    参数:
        input_text (str): 要处理的输入文本
        process_knowledge (function): 处理内容的函数，接受被 {} 包裹的内容作为参数
        max_depth (int): 最大递归深度，防止无限递归
    
    返回:
        str: 处理后的文本
    """
    # 定义正则表达式模式，匹配 {} 包裹的内容
    pattern = r'\[\[(.+?)\]\]'
    
    # 定义替换函数
    def replace_match(match):
        # 提取 {} 中的内容
        content = match.group(1)
        # 调用 process_knowledge 函数处理内容
        processed = process_knowledge(content)
        # 返回处理后的结果用于替换
        return str(processed)
    
    # 初始处理
    processed_text = re.sub(pattern, replace_match, input_text)
    
    # 检查是否还有 {} 需要处理，并且没有超过最大递归深度
    if max_depth > 0 and re.search(pattern, processed_text):
        return process_description_with_knowledge(processed_text, process_knowledge, max_depth - 1)
    
    return processed_text



def url_to_markdown(url, max_retries=3, chunk_size=8192):
    """
    Convert content from a URL to Markdown format.
    Supports: HTML, PDF, DOCX, PPTX, TXT
    
    Args:
        url (str): URL to fetch content from
        max_retries (int): Maximum download attempts (default: 3)
        chunk_size (int): Download chunk size in bytes (default: 8192)
    
    Returns:
        str: Content converted to Markdown
    
    Raises:
        Exception: If conversion fails or unsupported content type
    """
    headers = {
        'User-Agent': 'Mozilla/5.0',
        'Accept': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document, ' +
                 'application/vnd.openxmlformats-officedocument.presentationml.presentation, ' +
                 'application/pdf, text/html, text/plain'
    }
    
    # Try downloading with retries
    content = None
    response = None
    for attempt in range(max_retries):
        try:
            response = requests.get(url, headers=headers, stream=True)
            response.raise_for_status()
            
            # Read content into memory
            content = BytesIO()
            for chunk in response.iter_content(chunk_size=chunk_size):
                if chunk:
                    content.write(chunk)
            content.seek(0)
            break
                
        except (requests.exceptions.RequestException, 
               requests.exceptions.ChunkedEncodingError) as e:
            if attempt == max_retries - 1:
                raise Exception(f"Failed to download after {max_retries} attempts: {str(e)}")
            time.sleep(2 ** attempt)

    if content is None or response is None:
        raise Exception("Failed to download content")
    
    # Check if we got HTML when expecting a document
    content_type = response.headers.get('Content-Type', '').split(';')[0].lower()
    if 'html' in content_type and any(url.lower().endswith(ext) for ext in ['.docx', '.pptx', '.pdf', '.txt']):
        raise Exception(f"Server returned HTML instead of the expected document. The URL may require manual download: {url}")
    
    # PDF Processing
    if 'pdf' in content_type or url.lower().endswith('.pdf'):
        try:
            with pdfplumber.open(content) as pdf:
                pages = []
                for page in pdf.pages:
                    text = page.extract_text()
                    if text:
                        pages.append(text)
                return "\n\n".join(pages).strip()
        except Exception as e:
            raise Exception(f"PDF conversion failed: {str(e)}")
    
    # DOCX Processing
    elif 'docx' in content_type or url.lower().endswith('.docx'):
        try:
            
            content.seek(0)
            text = docx2txt.process(content)
            return text.replace('\n', '  \n').strip()
        except Exception as e:
            raise Exception(f"DOCX conversion failed: {str(e)}")
    
    # PPTX Processing
    elif 'pptx' in content_type or url.lower().endswith('.pptx'):
        try:
            # Verify it's a valid PPTX
            # try:
            #     with zipfile.ZipFile(content) as z:
            #         if 'ppt/presentation.xml' not in z.namelist():
            #             raise Exception("Not a valid PPTX file")
            # except:
            #     raise Exception("File is not a valid PPTX (ZIP format error)")
            
            content.seek(0)
            pres = pptx.Presentation(content)
            markdown = []
            for i, slide in enumerate(pres.slides):
                slide_content = []
                for shape in slide.shapes:
                    if shape.has_text_frame:
                        for paragraph in shape.text_frame.paragraphs:
                            text = paragraph.text.strip()
                            if text:
                                slide_content.append(text)
                if slide_content:
                    markdown.append(f"## Slide {i+1}\n\n" + "\n\n".join(slide_content))
            return "\n\n".join(markdown).strip()
        except Exception as e:
            raise Exception(f"PPTX conversion failed: {str(e)}")
    
    # HTML Processing
    elif 'html' in content_type:
        try:
            converter = html2text.HTML2Text()
            converter.ignore_links = False
            converter.ignore_images = False
            content.seek(0)
            return converter.handle(content.read().decode('utf-8')).strip()
        except Exception as e:
            raise Exception(f"HTML conversion failed: {str(e)}")
    
    # Plain Text
    elif 'text/plain' in content_type or url.lower().endswith('.txt'):
        try:
            content.seek(0)
            return content.read().decode('utf-8').strip()
        except Exception as e:
            raise Exception(f"Text processing failed: {str(e)}")
    
    # Unsupported type
    else:
        raise Exception(f"Unsupported content type: {content_type}")




import pandas as pd
from io import StringIO

def excel_to_csv_string(filepath):
    # Read all sheets into a dictionary
    xls = pd.read_excel(filepath, sheet_name=None, engine='openpyxl')

    output = []

    for sheet_name, df in xls.items():
        csv_buffer = StringIO()
        df.to_csv(csv_buffer, index=False)
        csv_string = csv_buffer.getvalue()
        output.append(f"----sheet:{sheet_name}------\n{csv_string}")

    return "\n".join(output)

def load_dynamic_pydantic_model(model_code: str, struct_name: str ) -> type:
    """
    Dynamically loads a Pydantic model from a string of code.
    
    Args:
        model_code (str): The code defining the Pydantic model.
        
    Returns:
        Type[BaseModel]: The dynamically loaded Pydantic model class.
    """
    eval_globals={}
    exec(model_code, eval_globals)
    print ("mode definitions: ",eval_globals.keys())
    return eval_globals[struct_name]  


def replace_placeholders(text):
    """
    Replaces placeholders {{date}}, {{time}}, and {{datetime}} in the input string with current values.
    
    Args:
        text (str): The input string containing placeholders.
    
    Returns:
        str: The string with placeholders replaced.
    """
    now = datetime.now()
    date_str = now.strftime("%Y-%m-%d")
    time_str = now.strftime("%H:%M:%S")
    datetime_str = now.strftime("%Y-%m-%d %H:%M:%S")

    replaced = (
        text
        .replace("{{date}}", date_str)
        .replace("{{time}}", time_str)
        .replace("{{datetime}}", datetime_str)
    )

    return replaced