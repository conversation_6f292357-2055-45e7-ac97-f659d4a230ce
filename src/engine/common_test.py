import pytest
from engine.common import url_to_markdown
from engine.common import excel_to_csv_string
# 测试用例
TEST_CASES = [
    # (URL, 预期结果特征)
    ("https://example.com", "Example Domain"),  # HTML
    ("https://www.w3.org/WAI/ER/tests/xhtml/testfiles/resources/pdf/dummy.pdf", "Dummy PDF file"),  # PDF
    ("https://accessibility.huit.harvard.edu/sites/g/files/omnuum1101/files/2025-04/Example%20Word%20Doc%20with%20Accessible%20Tips.docx", "Lorem ipsum"),  # DOCX
    ("https://goldcopd.org/wp-content/uploads/2025/03/GOLD-2025_teaching-slides-Chinese-version.pptx", "Slide Title"),  # PPTX
    ("https://www.gutenberg.org/files/1342/1342-0.txt", "Pride and Prejudice"),  # 纯文本
]

# @pytest.mark.parametrize("url, expected", TEST_CASES)
# def test_url_to_markdown(url, expected):
#     """
#     测试url_to_markdown函数是否能正确处理各种URL类型
#     """
#     try:
#         result = url_to_markdown(url)
#         assert expected in result, f"结果中应包含'{expected}'"
#     except Exception as e:
#         pytest.fail(f"处理{url}时出错: {str(e)}")

# # 错误情况测试
# def test_invalid_url():
#     """
#     测试无效URL的处理
#     """
#     with pytest.raises(Exception):
#         url_to_markdown("https://thisurldoesnotexist.invalid")

# def test_unsupported_type():
#     """
#     测试不支持的文件类型
#     """
#     with pytest.raises(Exception):
#         # 使用一个Excel文件的测试URL
#         url_to_markdown("https://file-examples.com/wp-content/storage/2017/02/file_example_XLS_10.xls")

def test_excel_to_csv_string():
   
    file = "automations/data_analysis/mtg_report.xlsx"
    try:
        result = excel_to_csv_string(file)
        print(result)  # 打印前200个字符
    except Exception as e:
        pytest.fail(f"处理Excel文件时出错: {str(e)}")

if __name__ == "__main__":
    # 手动运行测试(不使用pytest时)
    # print("===== 开始测试 =====")
    # for url, expected in TEST_CASES:
    #     print(f"\n测试URL: {url}")
    #     try:
    #         result = url_to_markdown(url)
    #         print(f"成功! 结果中包含: {expected}")
    #         print("示例输出(前200字符):", result[:200])
    #     except Exception as e:
    #         print(f"失败: {str(e)}")
    
    # print("\n===== 测试完成 =====")
    test_excel_to_csv_string()