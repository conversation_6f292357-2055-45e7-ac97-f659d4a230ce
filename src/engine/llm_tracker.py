from langchain.callbacks.base import BaseCallbackHandler

class TokenMonitor(BaseCallbackHandler):
    def __init__(self, max_tokens):
        self.max_tokens = max_tokens
    def on_llm_start(self, serialized, prompts, **kwargs):
        print(f"LLM start: {prompts}")

    def on_llm_end(self, response, **kwargs):
        print(f"LLM end: {response}")

    def on_llm_error(self, error, **kwargs):
        print(f"LLM error: {error}")