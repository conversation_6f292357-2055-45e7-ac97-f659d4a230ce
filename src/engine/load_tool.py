# from engine.prebuilt_tools import prebuilt_tools


# def load_lazy_tool(input_str):
#     """
#     Checks and parses a string in the format "function_name(arguments)", then calls the corresponding function from fn_cache

#     Args:
#         input_str: String to parse, should be in format "function_name(arguments)"
#         fn_cache: Dictionary containing callable functions, with function names as keys

#     Returns:
#         Result of the function call, or None if format is invalid or function doesn't exist
#     """
#     if not (input_str.endswith(")") and "(" in input_str):
#         return None

#     # Separate function name and arguments part
#     try:
#         func_name, args_part = input_str.split("(", 1)
#         args_part = args_part.rstrip(")")  # Remove trailing )

#         # Safely evaluate arguments using eval
#         # Note: In production, consider a safer argument parsing approach
#         args = []
#         kwargs = {}
#         if args_part:
#             # Parse arguments as a tuple
#             parsed_args = eval(f"({args_part},)", {"__builtins__": None}, {})
#             if parsed_args and isinstance(parsed_args, tuple):
#                 args = parsed_args

#         # Get function from cache
#         if func_name not in prebuilt_tools:
#             print(f"Function '{func_name}' not found.")
#             return None

#         func = prebuilt_tools[func_name]

#         # Call the function
#         return func(*args)

#     except Exception as e:
#         print(f"Error parsing or calling: {e}")
#         raise e


# def load_tools(tool_names, mcp_tools_loader):
#     including_tools = []
#     for tool_name in tool_names:
#         if tool_name.startswith("mcp:"):
#             mcp_tool_name = tool_name[4:].strip()
#             mcp_tools = mcp_tools_loader.get_mcp_tool(mcp_tool_name)
#             including_tools = including_tools + mcp_tools
#             continue

#         if tool_name.endswith(")") and "(" in tool_name:
#             # Lazy loading
#             tool = load_lazy_tool(tool_name)
#             if tool:
#                 including_tools.append(tool)
#                 continue

#         tool = prebuilt_tools.get(tool_name)

#         if not tool:
#             print(f"Tool '{tool_name}' not found.")
#             raise RuntimeError("Tool '{tool_name}' not found.")

#         including_tools.append(tool)

#     return including_tools
