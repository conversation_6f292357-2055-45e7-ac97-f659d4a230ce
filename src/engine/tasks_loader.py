import json
from engine.common import read_file_as_string
from crewai import Task
from engine.agents_loader import load_agents
from engine.common import get_contents_by_prefix, replace_placeholders
from engine.common import process_description_with_knowledge
from tools.memory import MemExtractingTool
from engine.prebuilt_tools import load_tools
from engine.common import load_dynamic_pydantic_model
def load_tasks(user_id, config_path: str, agents: dict, mcp_tool_loader, runtime_params: dict) -> tuple[list[Task], list[str]]:
    task_cfgs=[]
    with open(config_path+"/tasks.json", 'r', encoding='utf-8') as file:
        tasks_config = json.load(file)

    struct_definition_code = tasks_config.get("struct", None)
    if struct_definition_code:
        struct_definition_code = get_contents_by_prefix(user_id, config_path , struct_definition_code)
    print("struct_definition_code:", struct_definition_code)
    tasks_list = []
    named_tasks = {}
    for task_cfg in tasks_config.get("tasks", []):
        description_str = task_cfg.get("description", "")
        description_str = replace_placeholders(description_str)
        if  description_str == "":
            print(task_cfg)
            raise RuntimeError("missing description in task config")       
        description = get_contents_by_prefix(user_id, config_path, description_str)
        def process_knowledge(content):
            return get_contents_by_prefix(user_id, config_path, content)
        description = process_description_with_knowledge(description_str, process_knowledge)
        expected_output_str = task_cfg.get("expected_output", "")
        if expected_output_str=="":
            print(task_cfg)
            raise RuntimeError("missing expected_output in task config") 
             
        expected_output = get_contents_by_prefix(user_id, config_path, expected_output_str)

        knowledgeItems = task_cfg.get("knowledge", [])  
        knowledge = ""
        for k in knowledgeItems:
            k = get_contents_by_prefix(user_id, config_path, k)
            knowledge = knowledge + k + "\n"

        agent_name = task_cfg.get("agent", "")
        if agent_name:
            agent = agents.get(agent_name)
        else:
            raise RuntimeError(f"Agent '{agent_name}' not found.")  
        
        description = str(description) + "\n--- \n To complete the given task, please, refer to the following content: \n " + str(knowledge)

        long_term_memory_extraction = task_cfg.get("memory_extraction")
        if long_term_memory_extraction:
             space = long_term_memory_extraction["space"]
             category = long_term_memory_extraction["category"]
             extractor = MemExtractingTool(user_id, space,category)
             try:
                instructions = extractor.extract()
                
                if instructions:
                    print("user's preference: " + str(instructions))
                    description = description + "\n--- \n user's preference: \n" + instructions + "\n"
             except Exception as e:
                print(f"Error extracting memory: {e}")
                    
        output_file = task_cfg.get("output_file")    
        task = Task(
            description=description,
            expected_output=expected_output,
            agent=agent  
        )
        task_cfgs.append(description)
        if output_file:
            task.output_file = output_file
        tasks_list.append(task)

        output_struct = task_cfg.get("output_struct", None)
        print("output_struct:", output_struct)
        if struct_definition_code and output_struct:
            print("Loading dynamic pydantic model for output struct")
            task.output_pydantic = load_dynamic_pydantic_model(struct_definition_code,output_struct)

        tool_names = task_cfg.get("tools", [])
        including_tools = load_tools(tool_names, mcp_tool_loader)
        
        task.tools = including_tools
        task.async_execution = task_cfg.get("async_execution", False)
        if task.async_execution:
            print("Async Task: ",task_cfg)
        task_name = task_cfg.get("name", None)
        if task_name:
            named_tasks[task_name] = task
        dependent_task_names = task_cfg.get("dependent_tasks", [])
        dependent_tasks=[]
        if dependent_task_names and len(dependent_task_names) > 0:
            for i, c in enumerate(dependent_task_names):
                dependent_task = named_tasks.get(c)
                if dependent_task:
                    dependent_tasks.append(dependent_task)
                else:
                    raise NameError(f"Warning: Task '{c}' not found for context in task '{task_name}'.")
            task.context = dependent_tasks

        run_tasks = runtime_params.get("run_tasks", [])
        run_tasks_list = []
        if run_tasks and len(run_tasks) > 0:
            
            for i, c in enumerate(run_tasks):
                run_task = named_tasks.get(c)
                if run_task:
                    tasks_list.append(run_task)
                    print(f"Run task '{c}' is found")
                else:
                    raise NameError(f"Warning: Task '{c}' not found for run task in task '{task_name}'.")
        if run_tasks_list and len(run_tasks_list) > 0:
            tasks_list = run_tasks_list
    return tasks_list,task_cfgs