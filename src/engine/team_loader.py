from crewai import  Crew, Process
from engine.agents_loader import load_agents
from engine.tasks_loader import load_tasks
import os
from typing import Any, Optional
import json
from engine.prebuilt_llms import prebuilt_llms
os.environ["OTEL_SDK_DISABLED"] = "true"

MAX_TOKENS = 10000

class TeamConfig:
    def __init__(self,  tasks):
        self.tasks = tasks

    def get_tasks(self):
        return self.tasks

def load_team(user_id, config_path, mcp_tool_loader, runtime_params, tracker=None, task_callback : Optional[Any] = None, step_callback:Optional[Any] = None) ->tuple[Crew, TeamConfig]:
    agents = load_agents(user_id, config_path, mcp_tool_loader, tracker)
    print ([(agent.role,agent.llm) for agent in agents.values()])
    tasks, task_cfgs = load_tasks(user_id, config_path, agents, mcp_tool_loader, runtime_params)
    print(tasks)
    with open(config_path+"/agents.json", 'r', encoding='utf-8') as file:
        config = json.load(file)
    team_config = config.get("team", {})
    is_planning = False
    if team_config:
        is_planning = team_config.get("is_planning", False)
        planning_llm_name = team_config.get("planning_llm", None)
    if is_planning and planning_llm_name:
        print("Planning is enabled for this team.")

        crew = Crew(agents=agents.values(), 
                    tasks=tasks,  
                    verbose=1,
                    planning=is_planning,
                    planning_llm=prebuilt_llms.get(planning_llm_name)(),
                    crewai_disable_telemetry=True,
                    memory=False,
                    task_callback=task_callback,
                    step_callback=step_callback)
        return crew, TeamConfig( task_cfgs)
    crew = Crew(agents=agents.values(), 
                tasks=tasks,  
                verbose=1,               
                crewai_disable_telemetry=True,
                memory=False,
                task_callback=task_callback,
                step_callback=step_callback)
    return crew, TeamConfig(task_cfgs)
#print (load_team("engine/examples/team_demo").kickoff(inputs={"topic":"the latest AI agent and multi-agent advancements"}))
