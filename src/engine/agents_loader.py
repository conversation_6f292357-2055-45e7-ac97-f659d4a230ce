import json
from crewai import Agent
from engine.prebuilt_tools import prebuilt_tools
from prebuilt_agents.prebuild_agents import prebuilt_agents
from engine.prebuilt_llms import prebuilt_llms 
from engine.common import read_file_as_string
from engine.common import embeddings
from crewai.knowledge.source.crew_docling_source import CrewDoclingSource
from crewai.knowledge.source.text_file_knowledge_source import TextFileKnowledgeSource
from crewai.knowledge.source.pdf_knowledge_source import PDFKnowledgeSource
from engine.prebuilt_tools import load_tools
from engine.common import get_contents_by_prefix
from crewai.knowledge.source.excel_knowledge_source import ExcelKnowledgeSource
from crewai.knowledge.source.csv_knowledge_source import CSVKnowledgeSource

def load_llm_with_name(llm_name:str, tracker):
    if llm_name == "":
        raise RuntimeError(f"agent's llm is not defined")
    llm = None
    if llm_name:
        llm = prebuilt_llms.get(llm_name)()
        print(f"llm_name: {llm_name}")
        print(f"llm: {llm}")     
        print("\n\n")
        if not llm:
            print(f"LLM '{llm_name}' not found.")
            raise RuntimeError(f"LLM '{llm_name}' not found.")
    return llm


def load_agents(user_id, config_path: str, mcp_tool_loader, tracker):
    
    with open(config_path+"/agents.json", 'r', encoding='utf-8') as file:
        agents_config = json.load(file)
    
    agents_dict = {}
    for agent_cfg in agents_config.get("agents", []):
        prebuilt_agent_name=agent_cfg.get("prebuilt")
        if prebuilt_agent_name:
            agent_fn = prebuilt_agents.get(prebuilt_agent_name)
            if not agent_fn:
                print(f"Prebuilt agent '{prebuilt_agent_name}' not found.")
                raise RuntimeError(f"Prebuilt agent '{prebuilt_agent_name}' not found.")
            llm_name = agent_cfg.get("llm", "")
            memory_space = agent_cfg.get("memory_space","")              
            prebuilt_agent = agent_fn(user_id, load_llm_with_name(llm_name),memory_space)
            # if memory_space and memory_category:
            #     prebuilt_agent.memory_tool.space=memory_space
            #     prebuilt_agent.memory_tool.category=memory_category
            agents_dict[prebuilt_agent_name] = prebuilt_agent
            continue    

        role = agent_cfg.get("role")
        if not role:
            print(agent_cfg)
            raise RuntimeError("missing role in agent config")
        goal_str = agent_cfg.get("goal", "")
        goal = get_contents_by_prefix(user_id, config_path, goal_str)
        backstory_str = agent_cfg.get("backstory", "")
        backstory = get_contents_by_prefix(user_id, config_path, backstory_str)
        multimodal = agent_cfg.get("multimodal", False)
        print("multimodal:", multimodal)
        agent = Agent(
            role=role,
            goal=goal,
            backstory=backstory,
            verbose=agent_cfg.get("verbose", False),
            allow_delegation=agent_cfg.get("allow_delegation", False),
            max_iter=agent_cfg.get("max_iter", 10),
            memory=agent_cfg.get("memory", True),
            multimodal=multimodal,
        )
        tool_names = agent_cfg.get("tools", [])
        including_tools = load_tools(tool_names, mcp_tool_loader)
        if multimodal:
            from crewai.tools.agent_tools.add_image_tool import AddImageTool
            including_tools.append(AddImageTool())
        agent.tools = including_tools
        llm_name=agent_cfg.get("llm", "")
        agent.llm = load_llm_with_name(llm_name, tracker)
 
        print ("tools",agent.tools)
        try:
            knowledge_list = load_knowledge(agent_cfg.get("knowledge", []))
            agent.knowledge_sources = knowledge_list
            agent.embedder = embeddings
        except Exception as e:
            print(f"Error loading knowledge for agent '{role}': {e}")
            raise e
        
        if agent_cfg.get("memory_space"):
            memory_tool = prebuilt_tools["long_memory"]
            memory_tool.user_id = user_id
            memory_tool.space = agent_cfg.get("memory_space")
            memory_tool.llm = agent.llm
            including_tools.append(memory_tool)
            print ("\n\n\n")
            print("appending memory tool",including_tools)
        agent.tools = including_tools
        agents_dict[role] = agent
    return agents_dict


def load_knowledge(knowledgeSources):
    # knowledgeSources is a list of knowledge sources, such as "txt:knowledge.txt",
    # "url:https://example.com", "pdf:knowledge.pdf".
    # the first part is the type of the knowledge source, and the second part is the path or url.
    knowledge_list=[]
    for ks in knowledgeSources:
        if ks.startswith("txt:"):
            file_path = ks[4:].strip()
            knowledge = TextFileKnowledgeSource(
                file_paths=[file_path],
                embeddings=embeddings,
                )
        elif ks.startswith("url:"):
            url = ks[4:].strip()
            knowledge = CrewDoclingSource(
                file_paths=[url],
                embeddings= embeddings,
            )
        elif ks.startswith("pdf:"):
            pdf_path = ks[4:].strip()
            print("pdf_path", pdf_path)
            knowledge = PDFKnowledgeSource(
                file_paths=[pdf_path],
                embeddings=embeddings,
            )  
        elif ks.startswith("xls:"):
            file_path = ks[4:].strip()
            knowledge = ExcelKnowledgeSource(
                file_paths=[file_path], embeddings=embeddings)
        elif ks.startswith("csv:"):
            file_path = ks[4:].strip()
            knowledge = CSVKnowledgeSource(
                file_paths=[file_path], embeddings=embeddings)
        else:
            print(f"Unknown knowledge source type: {ks}")
            continue
        knowledge_list.append(knowledge)
    return knowledge_list

