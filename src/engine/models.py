import os

from crewai import LLM

#os.environ["DEEPSEEK_API_KEY"] = os.environ["DEEPSEEK_KEY"]

MaxCompletionTokens = 8192
MaxTokens = 8192
Temperature = 0

class Models:
    def __init__(self):
        pass

    def gpt_4o(self):   
        model_name="azure/gpt-4o"
        if os.environ["AZURE_OPENAI_API_KEY"].startswith("sk-"):
            model_name="gpt-4o"
        print("model_name", model_name)
        return LLM(
            model=model_name,
            api_base=os.environ["AZURE_OPENAI_ENDPOINT"],
            api_key=os.environ["AZURE_OPENAI_API_KEY"],
            api_version="2024-12-01-preview",
            max_completion_tokens=MaxCompletionTokens,
            max_tokens = MaxTokens,
            temperature=0,
            )
  

    def gpt_4o_mini(self):  
        model_name="azure/gpt-4o-mini"
        if os.environ["AZURE_OPENAI_API_KEY"].startswith("sk-"):
            model_name="gpt-4o-mini"
        return LLM(
                model=model_name,
                api_base=os.environ["AZURE_OPENAI_ENDPOINT"],
                api_key=os.environ["AZURE_OPENAI_API_KEY"],
                api_version="2024-12-01-preview",
                max_completion_tokens=MaxCompletionTokens,
                max_tokens = MaxTokens,
                temperature=0,
            )



    def gpt_4_1_mini(self):   
        model_name="azure/gpt-4.1-mini"
        if os.environ["AZURE_OPENAI_API_KEY_4_1"].startswith("sk-"):
            model_name="gpt-4.1-mini"
        return LLM(
            model=model_name,
            api_base=os.environ["AZURE_OPENAI_ENDPOINT_4_1"],
            api_key=os.environ["AZURE_OPENAI_API_KEY_4_1"],
            api_version="2024-12-01-preview",
            max_completion_tokens=MaxCompletionTokens,
            max_tokens = MaxTokens,
            temperature=0,
            )


    def gpt_4_1(self,tracker=None):   
        model_name="azure/gpt-41"
        if os.environ["AZURE_OPENAI_API_KEY_4_1"].startswith("sk-"):
            model_name="gpt-4.1"
        print("Track is ", tracker)
        return LLM(
                model=model_name,
                api_base=os.environ["AZURE_OPENAI_ENDPOINT_4_1"],
                api_key=os.environ["AZURE_OPENAI_API_KEY_4_1"],
                api_version="2024-12-01-preview",
                max_completion_tokens=MaxCompletionTokens,
                max_tokens = MaxTokens,
                callbacks=[tracker] if tracker else [],
                temperature=0,
            )


    # def gemini_2_pro_exp(self):
    #     api_key=""
    #     api_base=""
    #     try:
    #         api_base=os.environ["GEMINI_API_ENDPOINT_2_PRO"]
    #         api_key=os.environ["GEMINI_API_KEY_2_PRO"]
    #     except KeyError as e:
    #         print(f"Environment variable {e} not set. Using default values.")
    #     if api_base == "" or api_key == "":
    #         return LLM(
    #             model='vertex_ai/gemini-2.0-pro-exp-02-05',
    #             max_completion_tokens=MaxCompletionTokens,
    #             max_tokens = MaxTokens,
    #             temperature=0,
    #             seed=1,
    #         )
    #     model_name="vertex_ai/gemini-2.0-pro-exp-02-05"
    #     if os.environ["GEMINI_API_KEY_2_PRO"].startswith("sk-"):
    #         model_name="gemini-2.0-pro-exp-02-05"
    #     return LLM(
    #         api_base=api_base,
    #         api_key=api_key,
    #         model=model_name,
    #         max_completion_tokens=MaxCompletionTokens,
    #         max_tokens = MaxTokens,
    #         temperature=0,
    #         seed=1,
    #     )
    
    def gemini_2_flash_exp(self):
        api_key=""
        api_base=""
        try:
            api_base=os.environ["GEMINI_API_ENDPOINT_2_PRO"]
            api_key=os.environ["GEMINI_API_KEY_2_PRO"]
        except KeyError as e:
            print(f"Environment variable {e} not set. Using default values.")
        if api_base == "" or api_key == "":
            return LLM(
                model='vertex_ai/gemini-2.0-flash-exp',
                max_completion_tokens=MaxCompletionTokens,
                max_tokens = MaxTokens,
                temperature=0,
                seed=1,
            )
        model_name="vertex_ai/gemini-2.0-flash-exp"
        if os.environ["GEMINI_API_KEY_2_PRO"].startswith("sk-"):
            model_name="gemini-2.0-flash-exp"
        return LLM(
            api_base=api_base,
            api_key=api_key,
            model=model_name,
            max_completion_tokens=MaxCompletionTokens,
            max_tokens = MaxTokens,
            temperature=0,
            seed=1,
        )

    def gemini_2_flash_001(self):
        api_base=""
        api_key=""
        try:
            api_base=os.environ["GEMINI_API_ENDPOINT_2_FLASH_EXP"]
            api_key=os.environ["GEMINI_API_KEY_2_FLASH_EXP"]
        except KeyError as e:
            print(f"Environment variable {e} not set. Using default values.")
        if api_base == "" or api_key == "":
            return LLM(
                model='vertex_ai/gemini-2.0-flash-001',
                max_completion_tokens=MaxCompletionTokens,
                max_tokens = MaxTokens,
                temperature=0,
                seed=1,
            )
        model_name="vertex_ai/gemini-2.0-flash-001"
        if os.environ["GEMINI_API_KEY_2_FLASH_EXP"].startswith("sk-"):
            model_name="gemini-2.0-flash-001"
    
        return LLM(
            api_base=api_base,
            api_key=api_key,
            model=model_name,
            max_completion_tokens=MaxCompletionTokens,
            max_tokens = MaxTokens,
            temperature=0,
            seed=1,
        )

    def gemini_2_5_pro_pre(self):
        api_base=""
        api_base=""
        try:
            api_base=os.environ["GEMINI_API_ENDPOINT_2_5_PRO_PRE"]
            api_key=os.environ["GEMINI_API_KEY_2_5_PRO_PRE"]
        except KeyError as e:
            print(f"Environment variable {e} not set. Using default values.")

        if api_base == "" or api_key == "":
            return LLM(
                model='vertex_ai/gemini-2.5-pro',
                max_completion_tokens=MaxCompletionTokens,
                max_tokens = MaxTokens,
                temperature=0,
                seed=1,
            )
        return LLM(
            api_base=api_base,
            api_key=api_key,
            model='vertex_ai/gemini-2.5-pro',
            max_completion_tokens=MaxCompletionTokens,
            max_tokens = 8192*2,
            temperature=0,
            seed=1,
        )
    def gemini_2_5_flash(self):
        api_base=""
        api_base=""
        try:
            api_base=os.environ["GEMINI_API_ENDPOINT_2_5_PRO_PRE"]
            api_key=os.environ["GEMINI_API_KEY_2_5_PRO_PRE"]
        except KeyError as e:
            print(f"Environment variable {e} not set. Using default values.")

        if api_base == "" or api_key == "":
            return LLM(
                model='vertex_ai/gemini-2.5-flash',
                max_completion_tokens=MaxCompletionTokens,
                max_tokens = MaxTokens,
                temperature=0,
                seed=1,
            )
        return LLM(
            api_base=api_base,
            api_key=api_key,
            model='vertex_ai/gemini-2.5-flash',
            max_completion_tokens=MaxCompletionTokens,
            max_tokens = 8192*2,
            temperature=0,
            seed=1,
        )

    def claude_35_v2(self):
        api_base=""
        api_key=""
        try:
            api_base=os.environ["CLAUDE_API_ENDPOINT_35_V2"]
            api_key=os.environ["CLAUDE_API_KEY_35_V2"]
        except KeyError as e:
            print(f"Environment variable {e} not set. Using default values.")
        if api_base == "" or api_key == "":
            os.environ["AWS_ACCESS_KEY_ID"] = os.environ[
                "AWS_BEDROCK_ACCESS_KEY_ID"]
            os.environ["AWS_SECRET_ACCESS_KEY"] = os.environ[
                "AWS_BEDROCK_SECRET_ACCESS_KEY"]
            return LLM(
                model='bedrock/us.anthropic.claude-3-5-sonnet-20241022-v2:0',
                max_completion_tokens=MaxCompletionTokens,
                max_tokens = MaxTokens,
                temperature=0,
                seed=1,
            )
        if os.environ["CLAUDE_API_KEY_35_V2"].startswith("sk-"):
            model_name="openai/claude-3-5-sonnet-20240620"
        llm = LLM(
            api_base=api_base,
            api_key=api_key,
            model=model_name,
            max_completion_tokens=MaxCompletionTokens,
            max_tokens = MaxTokens,
            temperature=0,
            seed=1
        )
        return llm

    def claude_4_v1(self):
        api_base=""
        api_key=""
        try:
            api_base=os.environ["CLAUDE_API_ENDPOINT_4"]
            api_key=os.environ["CLAUDE_API_KEY_4"]
        except KeyError as e:
            print(f"Environment variable {e} not set. Using default values.")
        if api_base == "" or api_key == "":
            os.environ["AWS_ACCESS_KEY_ID"] = os.environ[
                "AWS_BEDROCK_ACCESS_KEY_ID"]
            os.environ["AWS_SECRET_ACCESS_KEY"] = os.environ[
                "AWS_BEDROCK_SECRET_ACCESS_KEY"]
            return LLM(
                model='bedrock/us.anthropic.claude-sonnet-4-20250514-v1:0',
                max_completion_tokens=MaxCompletionTokens*3,
                max_tokens = MaxTokens*3,
                temperature=0,
                seed=1,
            )
        if os.environ["CLAUDE_API_KEY_4"].startswith("sk-"):
            model_name="openai/claude-4"
        llm = LLM(
            api_base=api_base,
            api_key=api_key,
            model=model_name,
            max_completion_tokens=MaxCompletionTokens*3,
            max_tokens = MaxTokens*3,
            temperature=0,
            seed=1
        )
        return llm