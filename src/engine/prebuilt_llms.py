from engine.models import Models

models = Models()

def lazy_gemini_2_flash_exp():
    return models.gemini_2_flash_exp()
def lazy_gemini_2_flash_001():
    return models.gemini_2_flash_001()
# def lazy_gemini_2_pro_exp():
#     return models.gemini_2_pro_exp()

def lazy_gemini_2_5_flash():
    return models.gemini_2_5_flash()
def lazy_gpt_4_1():
    return models.gpt_4_1()
def lazy_gpt_4_1_mini():
    return models.gpt_4_1_mini()
def lazy_claude_35_v2():
    return models.claude_35_v2() 
def lazy_claude_4_v1():
    return models.claude_4_v1()
def lazy_gpt_4o():
    return models.gpt_4o()

def lazy_gpt_4o_mini():
    return models.gpt_4o_mini()

def lazy_gemini_2_5_pro_pre():
    return models.gemini_2_5_pro_pre()
def lazy_gemini_2_5_pro_flash():
    return models.gemini_2_5_pro_flash()

prebuilt_llms = {
    "gemini_2_flash_exp": lazy_gemini_2_flash_exp,
    "gemini_2_flash_001": lazy_gemini_2_flash_001,
    "gpt-4.1": lazy_gpt_4_1,
    "gpt-4.1-mini": lazy_gpt_4_1_mini,
    # "gpt-4o": lazy_gpt_4o,
    # "gpt-4o-mini": lazy_gpt_4o_mini,
    # "gemini_2_exp": lazy_gemini_2_pro_exp,
    "claude_35_v2": lazy_claude_35_v2,
    "claude_4_v1": lazy_claude_4_v1,
    "gemini_2.5_pro": lazy_gemini_2_5_pro_pre,
    "gemini_2.5_flash": lazy_gemini_2_5_flash,
}
