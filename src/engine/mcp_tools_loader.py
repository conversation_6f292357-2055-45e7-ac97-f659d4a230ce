import json
from typing import List
import os



from mcp import StdioServerParameters

from crewai_tools import MCPServerAdapter
from service.tool_service import ToolService

MCP_CONFIG_PATH = "mcp_tools_cfg/mcp_tools_cfg.json"

def load_mcp_tool_params(config_path: str):
    tool_params = {}
    try:
        with open(config_path, "r") as f:
            # print("================FFFF==================")
            # print(f.read())
            # print("================FFFF==================")
            tools_cfg = json.load(f)
            # print("===============tools_cfg=============")
            # print(tools_cfg)
            # print("===============tools_cfg=============")
            for tool in tools_cfg["tools"]:
                name = tool["name"]
                print(f"Loading tool: {name} \n")
                description = tool["description"]
                
                if "stdio_server_parameters" in tool:
                    stdio_params = tool["stdio_server_parameters"]
                    # 创建 StdioServerParameters 实例
                    stdio_server_params = StdioServerParameters(
                        command=stdio_params["command"],
                        args=stdio_params["args"]
                    )
                    tool_params[name] = stdio_server_params
                else:
                    server_params = tool["server_parameters"]
                    if server_params:                     
                        server_tool_params = {
                            "url": server_params["url"]                 
                        }
                        if "transport" not in server_params:
                            raise RuntimeError("Server parameters must contain 'transport' field.")
                        server_tool_params["transport"] = server_params["transport"]
                        if "header" in server_params:
                            server_tool_params["header"] = server_params["header"] 
                        if "params" in server_params:
                            server_tool_params["params"] = server_params["params"]
                        tool_params[name] = server_tool_params
                        print(f"Tool {name} has server parameters.")
                    else:
                        print(f"Tool {name} does not have valid parameters.")
    except Exception as e:    
        print(f"Error loading MCP tool parameters: {e}")
    return tool_params

# mcp_tool_params = load_mcp_tool_params(MCP_CONFIG_PATH)
# print("--------------------mcp_tool_params----------------")
# print(mcp_tool_params)
# print("--------------------mcp_tool_params----------------")


class MCP_ToolLoader:
    def __init__(self,tool_service: ToolService = None):
        self.used_mcp_tools_instances = []
        self.created_tools ={}
        self.tool_service = tool_service

 

    
    def create_mcp_tools(self, tool_params: dict, used_tool_name: str):
        params = tool_params.get(used_tool_name)

        if not params:
            raise RuntimeError("Tool "+used_tool_name +" not found.")
    

            # 创建 MCPAdapt 实例
        tool_instance = MCPServerAdapter(params)
        self.used_mcp_tools_instances.append(tool_instance)
        print(f"Created {used_tool_name} instance: {tool_instance}")
        tools = tool_instance.__enter__()
        self.created_tools[used_tool_name] = tools

        return tools


    def clean_mcp_tool_instances(self):
        for instance in self.used_mcp_tools_instances:
            print(f"Cleaning up {instance}")
            instance.__exit__(None, None, None)


    def load_mcp_tool_from_db(self, mcp_tool_params):
        if not self.tool_service:
            return mcp_tool_params
        tool_models = self.tool_service.load_mcp_tools()
        if not tool_models:
            return mcp_tool_params
        for tool_model in tool_models:
            if tool_model.tool_key in mcp_tool_params:
                continue
            tool_key = tool_model.tool_key
            if tool_key.startswith("mcp:"):
                tool_key = tool_key[4:]
            mcp_tool_params[tool_key] = {
                "url": tool_model.url,
                "transport": tool_model.transport
            }
        return mcp_tool_params
            
            

    def get_mcp_tool(self,used_tool_name: str):
        print(f"Getting {used_tool_name} instance")
        tools = self.created_tools.get(used_tool_name)
        # print("============tools=============")
        # print(tools)
        # print("============tools=============")
        if not tools:
            mcp_tool_params = load_mcp_tool_params("/maxagent/mcp_tools_cfg/mcp_tools_cfg.json")
            # mcp_tool_params = load_mcp_tool_params("/Users/<USER>/workspace/Jarvis/jarvis/mcp_tools_cfg/mcp_tools_cfg.json")
            # print("--------------------mcp_tool_params----------------")
            # print(mcp_tool_params)
            # print("--------------------mcp_tool_params----------------")
            mcp_tool_params = self.load_mcp_tool_from_db(mcp_tool_params)
            return self.create_mcp_tools(mcp_tool_params,used_tool_name)
        return tools
