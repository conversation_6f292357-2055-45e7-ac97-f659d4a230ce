from crewai_tools import NL2<PERSON><PERSON><PERSON>ool, <PERSON><PERSON>e<PERSON>eb<PERSON><PERSON>ool, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, VisionTool

# from coding_agent.coding_agent import code_for_requirement

# from coding_agent.coding_agent import code_for_requirement
from engine.prebuilt_llms import prebuilt_llms
from src.tools.file_reader import get_contents_from_file
from tools.describe_image import describe_image_with_gemini
from tools.human_feedback import collect_user_feedback
from tools.human_feedback_long_duration import collect_user_feedback_asyn
from tools.human_feedback_websocket import collect_user_feedback_websocket
from tools.langchain_tools import arxiv, asknews, wikipedia
from tools.memory import MemTool
from tools.python_repl import code_interpreter
from tools.s3_upload import upload_file_to_s3_tool
from tools.safe_code_executor import safe_code_interpreter
from tools.web_search import web_search
from tools.generate_image import generate_image_by_image4

def get_vision_tool(llm):
    return VisionTool(llm=prebuilt_llms[llm]())


prebuilt_tools = {
    "web_search": web_search,
    "human_interaction": collect_user_feedback,
    "human_interaction_async": collect_user_feedback_asyn,
    "long_memory": Mem<PERSON>ool(),
    "python_code_interpreter": code_interpreter,
    "read_webpage": ScrapeWebsiteTool(),
    "sql_gen": lambda db_uri: NL2SQLTool(db_uri=db_uri),
    "safe_code_interpreter": safe_code_interpreter,
    "vision_tool": describe_image_with_gemini,
    # "code_for_requirement": code_for_requirement,
    "file_reader": get_contents_from_file,
    # gjw 后面为MaxAgent新增
    # "FileReadTool": FileReadTool(),
    "asknews": asknews,
    "wikipedia": wikipedia,
    "arxiv": arxiv,
    "google_search": web_search,
    "human_feed_back": collect_user_feedback_websocket,
    # https://docs.crewai.com/tools/scrapewebsitetool
    "scrape_website_tool": ScrapeWebsiteTool(),
    # https://docs.crewai.com/tools/serperdevtool#description
    # 需要从 https://serper.dev/ 申请key ，目前的key 免费使用2500次
    # SERPER_API_KEY
    "serper_dev_tool": SerperDevTool(),
    "upload_file_to_s3": upload_file_to_s3_tool,
    "text_to_image_imagen4": generate_image_by_image4
}


def load_lazy_tool(input_str):
    """
    Checks and parses a string in the format "function_name(arguments)", then calls the corresponding function from fn_cache

    Args:
        input_str: String to parse, should be in format "function_name(arguments)"
        fn_cache: Dictionary containing callable functions, with function names as keys

    Returns:
        Result of the function call, or None if format is invalid or function doesn't exist
    """
    if not (input_str.endswith(")") and "(" in input_str):
        return None

    # Separate function name and arguments part
    try:
        func_name, args_part = input_str.split("(", 1)
        args_part = args_part.rstrip(")")  # Remove trailing )

        # Safely evaluate arguments using eval
        # Note: In production, consider a safer argument parsing approach
        args = []
        kwargs = {}
        if args_part:
            # Parse arguments as a tuple
            parsed_args = eval(f"({args_part},)", {"__builtins__": None}, {})
            if parsed_args and isinstance(parsed_args, tuple):
                args = parsed_args

        # Get function from cache
        if func_name not in prebuilt_tools:
            print(f"Function '{func_name}' not found.")
            return None

        func = prebuilt_tools[func_name]

        # Call the function
        return func(*args)

    except Exception as e:
        print(f"Error parsing or calling: {e}")
        raise e


def load_tools(tool_names, mcp_tools_loader):
    including_tools = []
    for tool_name in tool_names:
        if tool_name.startswith("mcp:"):
            mcp_tool_name = tool_name[4:].strip()
            mcp_tools = mcp_tools_loader.get_mcp_tool(mcp_tool_name)
            including_tools = including_tools + mcp_tools
            continue

        if tool_name.endswith(")") and "(" in tool_name:
            # Lazy loading
            tool = load_lazy_tool(tool_name)
            if tool:
                including_tools.append(tool)
                continue

        tool = prebuilt_tools.get(tool_name)

        if not tool:
            print(f"Tool '{tool_name}' not found.")
            raise RuntimeError("Tool '{tool_name}' not found.")

        including_tools.append(tool)

    return including_tools
