from pydantic import BaseModel
from typing import List
from automation.automation_imp import Automation
import json
import argparse
from engine.common import read_file_as_string

EvaluationAutomationConfig = "automations/task_assessment/automation_cfg.json"

class Sample(BaseModel):
    task_parameters: dict
    agent_anwser: str=""
    user_feedback: str=""
 

class Dataset(BaseModel):
    samples: List[Sample]


def parse_json_to_dataset(json_str: str) -> Dataset:
    return Dataset.model_validate_json(json_str)



class QualityAppraiser:
    def __init__(self, user_id: str, automation_config: str, training_samples_cfg: str, run_tasks=None):
        self.user_id = user_id
        self.automation_config = automation_config
        self.training_samples = parse_json_to_dataset(read_file_as_string(training_samples_cfg))
        self.automation = Automation(self.automation_config)
        self.assessment_automation = Automation(EvaluationAutomationConfig)
        self.run_tasks = run_tasks
    def preprocess_samples(self, samples: Dataset):
        for sample in samples.samples:
            params = sample.task_parameters
            if self.run_tasks:
                params["run_tasks"] = self.run_tasks
            ret = self.automation.run_automation(self.user_id, params)
            # Here you would implement the logic to train with the sample
            sample.agent_anwser = str(ret)
            if str.strip(sample.user_feedback) == "" or sample.user_feedback is None:
                sample.user_feedback = self.get_user_feedback(sample.task_parameters , sample.agent_anwser)
        return samples
    def get_user_feedback(self, question: str, ai_ans: str) -> str:
        # Placeholder for user feedback logic
        print("Getting user feedback for AI answer:\n")
        print(f"Question: {question}\nAI Answer: {ai_ans}\n")
        return input("Your feedback for the AI answer:")
    
    def run(self):
        print("Running LastTaskOptimizer...")
        training_samples = self.preprocess_samples(self.training_samples)
 
        task_def = self.automation.get_last_task()

        samples = str(training_samples.model_dump_json(indent=2)) 
        ret = self.assessment_automation.run_automation(self.user_id, {
            "task_def": task_def,
            "samples": samples
        })
        print("Evaluation result:", ret)
def main():
    # Set up command line argument parsing
    parser = argparse.ArgumentParser(description='Process nodes from a configuration file.')
    parser.add_argument('config_file', help='Path to the automation configuration file')
    parser.add_argument('training_set', help='Path to the training set JSON file')
    parser.add_argument('user_id', help='current user id')
    parser.add_argument('--run_tasks', required=False, nargs='+', help='List of tasks to run')
    args = parser.parse_args()
    run_tasks = None
    if args.run_tasks:
        print("Running specific tasks:", args.run_tasks)
        run_tasks = args.run_tasks
    opt=   QualityAppraiser(args.user_id, args.config_file, args.training_set, run_tasks)
    opt.run()

if __name__ == "__main__":
    main()