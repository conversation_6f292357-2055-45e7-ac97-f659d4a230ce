from setuptools import setup, Extension
from Cython.Build import cythonize
import glob

ext_modules = cythonize([
    "automation/*.py",
    "engine/*.py",
    "tools/*.py",  
    
], compiler_directives={"language_level": "3"}, build_dir="../target/temp")

setup(
    name="maxagent",
    ext_modules=ext_modules,
    packages=["automation", "engine","tools"],
    script_args=["build_ext", "--build-lib", "../target/lib"]
)


