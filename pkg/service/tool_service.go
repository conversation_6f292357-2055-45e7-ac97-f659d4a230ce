package service

import (
	"context"
	"fmt"
	"jarvis_api/pkg/db"
	"jarvis_api/pkg/model"
	"jarvis_api/tools"

	"jarvis_api/pkg/vo"

	"github.com/aws/smithy-go/ptr"
	"github.com/jinzhu/copier"
)

var ToolService = &toolService{}

type toolService struct {
}

func (s *toolService) List(ctx context.Context) ([]vo.ToolRes, error) {
	userId := tools.GetUserId(ctx)
	toolModels, err := db.ToolManager.List(ctx, userId)
	if err != nil {
		return nil, err
	}

	list := []vo.ToolRes{}
	err = copier.Copy(&list, &toolModels)
	if err != nil {
		return nil, err
	}
	return list, nil
}

func (s *toolService) Create(ctx context.Context, createReq *vo.CreateToolReq) (int64, error) {
	toolModel := &model.ToolModel{}
	err := copier.Copy(toolModel, createReq)
	if err != nil {
		return 0, err
	}

	toolModel.Id = tools.GenId()
	toolModel.CreateUserId = ptr.Int64(tools.GetUserId(ctx))
	toolModel.ToolKey = ptr.String(fmt.Sprintf("mcp:%v", toolModel.Id))

	err = db.ToolManager.Create(ctx, toolModel)
	if err != nil {
		return 0, err
	}
	return toolModel.Id, nil
}

// Update
func (s *toolService) Update(ctx context.Context, updateReq *vo.UpdateToolReq) error {
	toolModel := &model.ToolModel{}
	err := copier.Copy(toolModel, updateReq)
	if err != nil {
		return err
	}

	err = db.ToolManager.Update(ctx, toolModel)
	if err != nil {
		return err
	}
	return nil
}
