package service

import (
	"bytes"
	"context"
	"crypto/md5"
	"encoding/json"
	"fmt"
	"io"
	"jarvis_api/tools"
	"net/http"
	"reflect"
	"regexp"
	"sort"
	"strconv"
	"strings"
)

type ReportVo struct {
	Source   string `json:"source"`    // 来源
	Purpose  string `json:"purpose"`   // 用途
	Language string `json:"language"`  // 语言
	CodeLine int    `json:"code_line"` // 代码行
	Email    string `json:"email"`     // 用户邮箱
	Sha      string `json:"sha"`       // sha
}

func report_usage_data(ctx context.Context, answer string) {
	_, log := tools.GenContext(ctx)
	purpose := "chat"
	if checkUMLTags(answer) {
		purpose = "uml"
	}
	if checkImageTags(answer) {
		purpose = "img"
	}
	language, code_line := analyzeMarkdown(answer)
	if code_line > 0 {
		purpose = "code"
		language = ""
	}

	userEmail := tools.GetUserEmail(ctx)

	reportVo := &ReportVo{
		Source:   "MrMaxV2",
		Purpose:  purpose,
		Language: language,
		CodeLine: code_line,
		Email:    userEmail,
	}
	sha := GetStructSign(*reportVo)
	reportVo.Sha = sha

	requestBody, err := json.Marshal(reportVo)
	if err != nil {
		log.Error(err)
		return
	}

	// 创建一个新的请求
	req, err := http.NewRequest("POST", "http://182.92.76.225:5050/python/measurement/llm_used/gpt_usage_data", bytes.NewBuffer(requestBody))
	if err != nil {
		log.Error(err)
		return
	}

	// 设置请求头
	req.Header.Set("Content-Type", "application/json")

	// 发送请求
	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		log.Error(err)
		return
	}
	defer resp.Body.Close()

	// 读取响应
	_, err = io.ReadAll(resp.Body)
	if err != nil {
		log.Error(err)
		return
	}

}

func checkUMLTags(document string) bool {
	return strings.Contains(document, "@startuml") && strings.Contains(document, "@enduml")
}

func checkImageTags(document string) bool {
	return strings.Contains(document, "![image](https://resource.spotmaxtech.com/mrmax")
}

func analyzeMarkdown(markdown string) (string, int) {
	codeBlockRegex := regexp.MustCompile("(?s)```(\\w+)\\n(.*?)```")
	matches := codeBlockRegex.FindAllStringSubmatch(markdown, -1)

	languageCount := make(map[string]int)
	totalLines := 0
	maxLines := 0
	maxLanguage := "Unknown"

	for _, match := range matches {
		language := match[1]
		if language == "" {
			language = "Unknown"
		}
		code := match[2]
		lines := len(strings.Split(code, "\n"))

		languageCount[language] += lines
		totalLines += lines

		if languageCount[language] > maxLines {
			maxLines = languageCount[language]
			maxLanguage = language
		}
	}

	return maxLanguage, totalLines
}

func GetStructSign(myStruct any) string {

	structValue := reflect.ValueOf(myStruct)
	structType := structValue.Type()

	var fieldNames []string
	for i := 0; i < structValue.NumField(); i++ {
		if structType.Field(i).Name == "sha" {
			continue
		}
		fieldNames = append(fieldNames, structType.Field(i).Name)
	}

	sort.Strings(fieldNames)

	var concatenatedString string
	for _, fieldName := range fieldNames {
		tagName := fieldName
		if field, ok := structType.FieldByName(fieldName); ok {
			tagName = field.Tag.Get("json")
		}

		fieldValue := structValue.FieldByName(fieldName)
		if fieldValue.Kind() == reflect.String {
			concatenatedString += tagName + fieldValue.String()
		} else if fieldValue.Kind() == reflect.Int {
			concatenatedString += tagName + strconv.FormatInt(fieldValue.Int(), 10)
		}
	}

	// fmt.Println(concatenatedString)
	// fmt.Println(util.Md5(concatenatedString))
	return Md5(concatenatedString)
}

func Md5(source string) string {
	sum := md5.Sum([]byte(source))
	return fmt.Sprintf("%x", sum)
}
