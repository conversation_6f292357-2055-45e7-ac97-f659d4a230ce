package service

import (
	"context"
	"jarvis_api/pkg/db"
	"jarvis_api/pkg/model"
	"jarvis_api/pkg/vo"

	"github.com/jinzhu/copier"
)

var LlmService = &llmService{}

type llmService struct {
}

func (s *llmService) ListLlms(ctx context.Context) ([]vo.LlmRes, error) {
	llmModels, err := db.LlmManager.ListLlms(ctx)
	if err != nil {
		return nil, err
	}

	llms := []vo.LlmRes{}
	err = copier.Copy(&llms, &llmModels)
	if err != nil {
		return nil, err
	}

	return llms, nil
}

// func (s *llmService) CreateLlm(ctx context.Context, createLlmReq *vo.CreateLlmReq) error {
// 	llmModel := &model.LlmModel{}
// 	err := copier.Copy(llmModel, createLlmReq)
// 	if err != nil {
// 		return err
// 	}
// 	llmModel.Id = tools.GenId()

// 	err = db.LlmManager.CreateLlm(ctx, llmModel)
// 	if err != nil {
// 		return err
// 	}
// 	return nil
// }

func (s *llmService) GetLlm(ctx context.Context, llmId int64) (*model.LlmModel, error) {
	llm, err := db.LlmManager.GetLlm(ctx, llmId)
	if err != nil {
		return nil, err
	}
	return llm, nil
}
