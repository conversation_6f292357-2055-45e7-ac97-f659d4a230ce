package service

import (
	"context"
	"fmt"
	"jarvis_api/pkg/db"
	"jarvis_api/pkg/model"
	"jarvis_api/pkg/vo"
	"jarvis_api/tools"
	"strconv"
	"strings"

	"github.com/jinzhu/copier"
	"github.com/mozillazg/go-pinyin"
	"gorm.io/gorm"
)

var UserService = &userService{}

type userService struct {
}

func (s *userService) ResetSecretKey(ctx context.Context, id int64) (string, error) {
	secretKey := tools.Md5(strconv.FormatInt(tools.GenId(), 10))
	err := db.UserManager.UpdateSecretKey(ctx, id, secretKey)
	return secretKey, err
}

func (s *userService) GetUserById(ctx context.Context, id int64) (*model.UserModel, error) {
	return db.UserManager.GetUserById(ctx, id)
}
func (s *userService) GetUserByIdList(ctx context.Context, ids []int64) (map[int64]model.UserModel, []model.UserModel, error) {
	users, err := db.UserManager.GetUserByIdList(ctx, ids)
	if err != nil {
		return nil, nil, err
	}

	userMaps := map[int64]model.UserModel{}
	for _, user := range users {
		userMaps[user.Id] = user
	}
	return userMaps, users, nil
}

// acs 登录成功后处理
// 1:查询用户表（用户中心ID）
// 3:如果用户不存在则插入
func (s *userService) AcsUserLogin(ctx context.Context, acsUserInfo *AcsUserInfo) (*model.UserModel, error) {
	_, log := tools.GenContext(ctx)
	// 使用用户中心ID查询用户
	user, err := s.GetUserById(ctx, acsUserInfo.Profiles.Id)
	if err != nil && err != gorm.ErrRecordNotFound {
		log.Error(err)
		return nil, err
	}
	if user == nil || user.Id == 0 {
		user, err = s.CreateUserFromAcsUser(ctx, acsUserInfo)
		if err != nil {
			log.Error(err)
			return nil, err
		}
	}
	return user, nil
}

func (s *userService) GetUserByAcsId(ctx context.Context, acsId int64) (*model.UserModel, error) {
	return db.UserManager.GetUserByAcsId(ctx, acsId)
}

func (s *userService) CreateUserFromAcsUser(ctx context.Context, acsUserInfo *AcsUserInfo) (*model.UserModel, error) {
	userModel := &model.UserModel{
		Id:         acsUserInfo.Profiles.Id,
		AcsId:      acsUserInfo.Profiles.Id,
		Nid:        acsUserInfo.Profiles.Nid,
		Username:   acsUserInfo.Profiles.Username,
		RealName:   acsUserInfo.Profiles.RealName,
		Email:      acsUserInfo.Profiles.Email,
		CompanyId:  1, // acs 过来的用户默认都为1（Mobvista）
		DeleteTime: 0,
		SecretKey:  tools.Md5(strconv.FormatInt(tools.GenId(), 10)),
	}
	err := db.UserManager.Createuser(ctx, userModel)
	if err != nil {
		return nil, err
	}
	return userModel, nil
}

func (s *userService) SearchUser(ctx context.Context, key string) ([]model.UserModel, error) {
	return db.UserManager.SearchUser(ctx, key)
}

func (s *userService) ListUser(ctx context.Context) ([]vo.UserRes, error) {

	users, err := db.UserManager.ListUser(ctx)
	if err != nil {
		return nil, err
	}

	listUserRes := []vo.UserRes{}
	for _, user := range users {
		userRes := &vo.UserRes{}
		err = copier.Copy(userRes, user)
		if err != nil {
			return nil, err
		}

		userRes.Pinyin = getPinyin(user.RealName)
		listUserRes = append(listUserRes, *userRes)
	}
	return listUserRes, nil
}

func (s *userService) GetUserListByProjectIdCacheKey(ctx context.Context, projectId int64) string {
	return fmt.Sprintf("GetUserListByProjectId:%d", projectId)
}

func (s *userService) GetUserListByProjectId(ctx context.Context, projectId int64) ([]vo.UserRes, error) {
	_, log := tools.GenContext(ctx)

	cacheKey := s.GetUserListByProjectIdCacheKey(ctx, projectId)

	cachedUsers, ok := SpeedCache.Get(cacheKey).([]vo.UserRes)
	if ok {
		return cachedUsers, nil
	}

	projectUserChan := make(chan []model.UserModel)
	go func(userChan chan []model.UserModel) {
		defer close(userChan)
		projectUsers, err := db.UserManager.GetUserListByProjectId(ctx, projectId)
		if err != nil {
			userChan <- []model.UserModel{}
			log.Error(err)
			return
		}
		userChan <- projectUsers
	}(projectUserChan)

	allUserChan := make(chan []model.UserModel)
	go func(allUserChan chan []model.UserModel) {
		defer close(allUserChan)
		users, err := db.UserManager.ListUser(ctx)
		if err != nil {
			allUserChan <- []model.UserModel{}
			log.Error(err)
			return
		}
		allUserChan <- users
	}(allUserChan)

	projectUsers := <-projectUserChan
	allUser := <-allUserChan

	projectUserId := map[int64]struct{}{}
	listUserRes := []vo.UserRes{}
	for _, user := range projectUsers {
		projectUserId[user.Id] = struct{}{}

		userRes := &vo.UserRes{}
		err := copier.Copy(userRes, user)
		if err != nil {
			return nil, err
		}

		userRes.Pinyin = getPinyin(user.RealName)
		listUserRes = append(listUserRes, *userRes)
	}

	for _, user := range allUser {
		if _, ok := projectUserId[user.Id]; ok {
			continue
		}

		userRes := &vo.UserRes{}
		err := copier.Copy(userRes, user)
		if err != nil {
			return nil, err
		}

		userRes.Pinyin = getPinyin(user.RealName)
		listUserRes = append(listUserRes, *userRes)
	}

	SpeedCache.Set(cacheKey, listUserRes)
	return listUserRes, nil
}

func getPinyin(str string) string {
	a := pinyin.NewArgs()
	a.Heteronym = true
	result := pinyin.Pinyin(str, a)

	pinyins := permute(result)
	if len(pinyins) > 0 {
		return strings.Join(pinyins, ",")
	}
	return ""
}

func permute(arr [][]string) []string {
	result := []string{}
	for i := 0; i < len(arr); i++ {
		row := arr[i]
		if len(result) == 0 {
			result = append(result, row...)
		} else {
			resultRowTemps := [][]string{}
			for _, v := range row {
				t := []string{}
				for j := 0; j < len(result); j++ {
					t = append(t, result[j])
				}
				for j := 0; j < len(t); j++ {
					t[j] = t[j] + v
				}
				resultRowTemps = append(resultRowTemps, t)
			}

			result = []string{}
			for _, v := range resultRowTemps {
				result = append(result, v...)
			}
		}
	}
	return result
}

func (s *userService) getUserNameMap(ctx context.Context) map[int64]string {
	userList, _ := s.GetUserListByProjectId(ctx, 1)
	uMap := make(map[int64]string, 2000)
	for _, info := range userList {
		uMap[info.Id] = info.RealName // 中文名称
	}
	return uMap
}

func (s *userService) GetUsersByProjectId(ctx context.Context, projectId int64) ([]model.UserModel, error) {
	// 优先取 关联表的信息
	projectUsers, err := db.UserManager.GetUsersByProjectMember(ctx, projectId)
	if err != nil && err != gorm.ErrRecordNotFound {
		return nil, err
	}
	if len(projectUsers) > 0 {
		return projectUsers, nil
	}
	// 取不到再取使用信息
	projectUsers, err = db.UserManager.GetUserListByProjectId(ctx, projectId)
	if err != nil && err != gorm.ErrRecordNotFound {
		return nil, err
	}
	return projectUsers, nil
}
