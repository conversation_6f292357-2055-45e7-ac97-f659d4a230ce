package service

import (
	"context"
	"encoding/json"
	"jarvis_api/constant"
	"jarvis_api/pkg/db"
	"jarvis_api/pkg/model"
	"jarvis_api/pkg/vo"
	"jarvis_api/tools"
	"time"

	"github.com/jinzhu/copier"
)

var RunnerService = &runnerService{}

type runnerService struct {
}

func (s *runnerService) Create(ctx context.Context, createReq *vo.CreateRunnerReq) (int64, error) {
	runnerModel := &model.RunnerModel{}
	err := copier.Copy(runnerModel, createReq)
	if err != nil {
		return 0, err
	}
	id := tools.GenId()
	runnerModel.Id = id
	runnerModel.CreateUserId = tools.GetUserId(ctx)

	runnerModel.TriggerParams = str2Json(createReq.TriggerParams)

	if len(createReq.AllowTasks) >= 0 && createReq.AllowTeamId > 0 {
		allowTasksByte, err := json.Marshal(createReq.AllowTasks)
		if err != nil {
			return 0, err
		}
		runnerModel.AllowTasks = string(allowTasksByte)
	}

	// 使用东八区时间
	loc, _ := time.LoadLocation("Asia/Shanghai")
	runnerModel.TriggerTime = time.Now().In(loc).Unix()
	runnerModel.TriggerMode = string(constant.TriggerModeWeb)
	runnerModel.ExecuteStatus = string(constant.ExecuteStatusInit)

	err = db.RunnerManager.Create(ctx, runnerModel)
	if err != nil {
		return 0, err
	}

	return id, nil
}

func (s *runnerService) Get(ctx context.Context, runnerId int64) (*model.RunnerModel, error) {
	obj, err := db.RunnerManager.Get(ctx, runnerId)
	if err != nil {
		return nil, err
	}
	return obj, nil
}

func (s *runnerService) List(ctx context.Context, runnerType constant.RunnerType, runnerTargetId int64) ([]vo.RunnerRes, error) {
	userId := tools.GetUserId(ctx)
	runnerList, err := db.RunnerManager.List(ctx, runnerType, userId, runnerTargetId)
	if err != nil {
		return nil, err
	}

	list := []vo.RunnerRes{}
	err = copier.Copy(&list, &runnerList)
	if err != nil {
		return nil, err
	}
	return list, nil
}
func (s *runnerService) ListRunnerV2(ctx context.Context, listRunnerReq *vo.ListRunnerReq) ([]vo.RunnerRes, error) {
	userId := tools.GetUserId(ctx)
	runnerList, err := db.RunnerManager.ListRunnerV2(ctx, userId, listRunnerReq)
	if err != nil {
		return nil, err
	}

	list := []vo.RunnerRes{}
	err = copier.Copy(&list, &runnerList)
	if err != nil {
		return nil, err
	}
	return list, nil
}
