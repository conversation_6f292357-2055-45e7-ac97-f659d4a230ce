package service

import (
	"context"
	"crypto/md5"
	"encoding/hex"
	"fmt"
	"jarvis_api/pkg/vo"
	"jarvis_api/tools"
	"net/http"
	"os"
	"sort"
	"strconv"
	"time"

	"github.com/go-resty/resty/v2"
)

var SsoService = &ssoService{
	Client: resty.New(),
}

type ssoService struct {
	Client *resty.Client
}

type SsoAccessToken struct {
	AccessToken  string `json:"access_token"`
	RefreshToken string `json:"refresh_token"`
	ExpireTime   int64  `json:"expire_time"`
	TokenType    string `json:"token_type"`
}

const SsoUserCenterUrl = "https://connect.spotmaxtech.com/api/v1" // localhost:9908

func (s *ssoService) GetAccessToken(ctx context.Context, input *vo.SsoLoginReq) (*SsoAccessToken, error) {
	_, log := tools.GenContext(ctx)

	ssoAppKey := os.Getenv("SSO_APP_KEY")
	ssoAppSecret := os.Getenv("SSO_APP_SECRET")

	ssoAccessToken := &SsoAccessToken{}
	reqMap := map[string]string{
		"code":      input.Code,
		"appkey":    ssoAppKey,
		"timestamp": strconv.FormatInt(time.Now().Unix(), 10),
	}
	reqMap["sign"] = s.generateSignature(reqMap, ssoAppSecret)

	response, err := s.Client.R().SetQueryParams(reqMap).SetResult(ssoAccessToken).Get(SsoUserCenterUrl + "/connect/token")
	if err != nil {
		log.Error(err)
		return nil, err
	}
	if response.StatusCode() != http.StatusOK {
		errorStatus := fmt.Sprintf("get sso token status error:%v", response.StatusCode())
		log.Error(errorStatus)
		return nil, fmt.Errorf(errorStatus)
	}
	return ssoAccessToken, nil
}

// Generated by https://quicktype.io
type SsoUserInfo struct {
	ThirdPartyInfo ThirdPartyInfoStr `json:"third_party_info"`
}

type ProfilesStr struct {
	Id       int64  `json:"id"`
	Nid      string `json:"nid"`
	Username string `json:"username"`
	RealName string `json:"real_name"`
	Email    string `json:"email"`
	Status   int64  `json:"status"`
	DdUserId string `json:"dd_user_id"`
}

type ThirdPartyInfoStr struct {
	Profiles ProfilesStr `json:"profiles"`
}

func (s *ssoService) GetUserInfo(ctx context.Context, ssoAccessToken *SsoAccessToken) (*SsoUserInfo, error) {
	_, log := tools.GenContext(ctx)

	ssoAppKey := os.Getenv("SSO_APP_KEY")
	ssoAppSecret := os.Getenv("SSO_APP_SECRET")

	url_tmp := SsoUserCenterUrl + "/connect/user_info"

	reqMap := map[string]string{
		"appkey":       ssoAppKey,
		"access_token": ssoAccessToken.AccessToken,
		"timestamp":    strconv.FormatInt(time.Now().Unix(), 10),
	}
	reqMap["sign"] = s.generateSignature(reqMap, ssoAppSecret)

	ssoUserInfo := &SsoUserInfo{}

	response, err := s.Client.R().SetQueryParams(reqMap).SetResult(ssoUserInfo).Get(url_tmp)

	if err != nil {
		log.Error(err)
		return nil, err
	}
	if response.StatusCode() != http.StatusOK {
		errorStatus := fmt.Sprintf("get acs token status error:%v", response.StatusCode())
		log.Error(errorStatus)
		return nil, fmt.Errorf(errorStatus)
	}

	return ssoUserInfo, nil
}

func (s *ssoService) generateSignature(params map[string]string, secret string) string {
	keys := make([]string, 0, len(params))
	for k := range params {
		keys = append(keys, k)
	}
	sort.Strings(keys)

	var kvList string
	for _, key := range keys {
		kvList += key + "=" + params[key]
	}
	originSign := kvList + secret

	hasher := md5.New()
	hasher.Write([]byte(originSign))
	return hex.EncodeToString(hasher.Sum(nil))
}
