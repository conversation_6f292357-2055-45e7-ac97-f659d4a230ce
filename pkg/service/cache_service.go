package service

import (
	"sync"
	"time"
)

var SpeedCache *LocalCache

func InitCacheHold() error {
	SpeedCache = NewLocalCache(10 * time.Minute)
	return nil
}

// LocalCache is a simple in-memory cache that uses a mutex to protect concurrent access.
type LocalCache struct {
	data   map[string]interface{} // Use interface{} to store any type of value
	mutex  sync.Mutex
	expire time.Duration
}

// NewLocalCache creates a new LocalCache with the specified expiration time.
func NewLocalCache(expire time.Duration) *LocalCache {
	return &LocalCache{
		data:   make(map[string]interface{}),
		mutex:  sync.Mutex{},
		expire: expire,
	}
}

// Get retrieves the value for the given key from the cache.
// If the key does not exist or the value has expired, it returns nil.
func (c *LocalCache) Get(key string) interface{} {
	c.mutex.Lock()
	defer c.mutex.Unlock()

	value, ok := c.data[key]

	if !ok || time.Unix(c.data[key+".expire"].(int64), 0).Before(time.Now()) {
		return nil
	}

	return value
}

// Set stores the value for the given key in the cache.
// It also sets an expiration time for the value.
func (c *LocalCache) Set(key string, value interface{}) {
	c.mutex.Lock()
	defer c.mutex.Unlock()

	c.data[key] = value
	c.data[key+".expire"] = time.Now().Add(c.expire).Unix()
}

func (c *LocalCache) Del(key string) {
	c.mutex.Lock()
	defer c.mutex.Unlock()

	c.data[key] = struct{}{}
	c.data[key+".expire"] = time.Now().Add(time.Second).Unix()
}
