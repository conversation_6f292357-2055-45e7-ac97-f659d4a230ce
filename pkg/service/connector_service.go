package service

import (
	"context"
	"encoding/json"
	"jarvis_api/pkg/db"
	"jarvis_api/pkg/model"
	"jarvis_api/pkg/vo"
	"jarvis_api/tools"

	"github.com/aws/smithy-go/ptr"
	"github.com/jinzhu/copier"
)

var ConnectorService = &connectorService{}

type connectorService struct {
}

func (s *connectorService) DeleteConnector(ctx context.Context, id int64) error {
	_, log := tools.GenContext(ctx)

	err := db.ConnectorManager.Delete(ctx, id)
	if err != nil {
		log.Error(err)
		return err
	}
	return nil
}

func (s *connectorService) UpdateConnector(ctx context.Context, id int64, updateReq *vo.UpdateConnectorReq) error {
	_, log := tools.GenContext(ctx)

	connector, err := db.ConnectorManager.Get(ctx, id)
	if err != nil {
		log.Error(err)
		return err
	}

	err = copier.Copy(connector, updateReq)
	if err != nil {
		log.Error(err)
		return err
	}

	connector.MacroList = stringPtr(macro2Str(updateReq.MacroList))
	connector.Router = stringPtr(routers2Str(updateReq.Routers))

	err = db.ConnectorManager.Update(ctx, connector)
	if err != nil {
		log.Error(err)
		return err
	}

	return nil
}

func (s *connectorService) GetConnector(ctx context.Context, id int64) (vo.ConnectorRes, error) {
	_, log := tools.GenContext(ctx)

	connector, err := db.ConnectorManager.Get(ctx, id)
	if err != nil {
		log.Error(err)
		return vo.ConnectorRes{}, err
	}

	var connectorRes vo.ConnectorRes
	err = copier.Copy(&connectorRes, &connector)
	if err != nil {
		log.Error(err)
		return vo.ConnectorRes{}, err
	}
	taskMacroList := []vo.TaskMacro{}
	err = json.Unmarshal([]byte(*connector.MacroList), &taskMacroList)
	if err != nil {
		log.Error(err)
		return vo.ConnectorRes{}, err
	}
	connectorRes.MacroList = taskMacroList

	routers := []vo.Router{}
	if connector.Router != nil && *connector.Router != "" {
		err = json.Unmarshal([]byte(*connector.Router), &routers)
		if err != nil {
			log.Error(err)
			return vo.ConnectorRes{}, err
		}
	}
	connectorRes.Routers = routers
	return connectorRes, nil
}

func (s *connectorService) ListConnector(ctx context.Context, appId int64) ([]vo.ConnectorRes, error) {
	_, log := tools.GenContext(ctx)

	connectorList, err := db.ConnectorManager.List(ctx, appId)
	if err != nil {
		log.Error(err)
		return nil, err
	}

	list := []vo.ConnectorRes{}
	for _, connector := range connectorList {
		var connectorRes vo.ConnectorRes
		err = copier.Copy(&connectorRes, &connector)
		if err != nil {
			log.Error(err)
			return nil, err
		}
		taskMacroList := []vo.TaskMacro{}
		err = json.Unmarshal([]byte(*connector.MacroList), &taskMacroList)
		if err != nil {
			log.Error(err)
			return nil, err
		}
		connectorRes.MacroList = taskMacroList

		routers := []vo.Router{}
		if connector.Router != nil && *connector.Router != "" {
			err = json.Unmarshal([]byte(*connector.Router), &routers)
			if err != nil {
				log.Error(err)
				return nil, err
			}
			connectorRes.Routers = routers
		}

		list = append(list, connectorRes)
	}

	return list, nil
}

func (s *connectorService) CreateConnector(ctx context.Context, createReq *vo.CreateConnectorReq) (int64, error) {
	_, log := tools.GenContext(ctx)

	connectorModel := &model.ConnectorModel{}

	err := copier.Copy(connectorModel, createReq)
	if err != nil {
		log.Error(err)
		return 0, err
	}

	id := tools.GenId()
	connectorModel.Id = id
	connectorModel.CreateUserId = ptr.Int64(tools.GetUserId(ctx))
	connectorModel.MacroList = stringPtr(macro2Str(createReq.MacroList))
	connectorModel.Router = stringPtr(routers2Str(createReq.Routers))

	err = db.ConnectorManager.Create(ctx, connectorModel)
	if err != nil {
		log.Error(err)
		return 0, err
	}

	return id, nil
}
