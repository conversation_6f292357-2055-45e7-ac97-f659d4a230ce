package service

import (
	"encoding/json"
	"fmt"
	"jarvis_api/pkg/vo"
	"testing"
)

func TestEncipher(t *testing.T) {
	a := vo.AzureLlmProvideAuth{
		BaseUrl:    "https://maxcloud.azure.com",
		Token:      "sadfafasdfasdfasdf",
		Model:      "gpt-4",
		ApiType:    "azure",
		ApiVersion: "2024-08-12-asdf",
	}
	azureLlmProvideAuth := a
	auth, err := json.Marshal(azureLlmProvideAuth)
	if err != nil {
		t.Error(err)
		return
	}
	encipher := Encipher(string(auth))
	fmt.Println(encipher)

	decipher := Decrypt(encipher)
	fmt.Println(decipher)
}
