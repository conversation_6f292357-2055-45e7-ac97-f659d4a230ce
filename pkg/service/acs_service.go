package service

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"jarvis_api/pkg/db"
	"jarvis_api/pkg/errs"
	"jarvis_api/pkg/model"
	"jarvis_api/pkg/vo"
	"jarvis_api/tools"
	"net/http"
	"os"
	"time"

	"github.com/go-resty/resty/v2"
)

var AcsService = &acsService{
	Client: resty.New(),
}

type acsService struct {
	Client *resty.Client
}

const AcsClientId = "1089"

// Generated by https://quicktype.io

type AcsAccessToken struct {
	AccessToken      string `json:"access_token"`
	RefreshToken     string `json:"refresh_token"`
	ExpiresIn        int64  `json:"expires_in"`
	TokenType        string `json:"token_type"`
	Error            string `json:"error"`
	ErrorDescription string `json:"error_description"`
}

const user_center_url = "https://accounts.mobvista.com/token"

func (s *acsService) GetAccessToken(ctx context.Context, input *vo.AcsLoginReq) (*AcsAccessToken, error) {
	_, log := tools.GenContext(ctx)

	clientSecret := os.Getenv("ACS_CLIENT_SECRET")

	acsAccessToken := &AcsAccessToken{}
	response, err := s.Client.R().SetFormData(map[string]string{
		"code":          input.Code,
		"client_id":     AcsClientId,
		"client_secret": clientSecret,
		"redirect_uri":  input.RedirectUri,
		"grant_type":    "authorization_code",
	}).SetResult(acsAccessToken).Post(user_center_url)
	if err != nil {
		log.Error(err)
		return nil, err
	}
	if response.StatusCode() != http.StatusOK {
		errorStatus := fmt.Sprintf("get acs token status error:%v", response.StatusCode())
		log.Error(errorStatus)
		return nil, errs.ErrorAcsAuth
	}

	return acsAccessToken, nil
}

// Generated by https://quicktype.io
type AcsUserInfo struct {
	Profiles         Profiles `json:"profiles"`
	Error            string   `json:"error"`
	ErrorDescription string   `json:"error_description"`
}

type Profiles struct {
	Id       int64  `json:"id"`
	Nid      string `json:"nid"`
	Username string `json:"username"`
	RealName string `json:"real_name"`
	Email    string `json:"email"`
	Status   int64  `json:"status"`
	DdUserId string `json:"dd_user_id"`
}

func (s *acsService) GetUserInfo(ctx context.Context, acsAccessToken *AcsAccessToken) (*AcsUserInfo, error) {
	_, log := tools.GenContext(ctx)

	url_tmp := "https://accounts.mobvista.com/userinfo?access_token=%v&fields=%v"

	url := fmt.Sprintf(url_tmp, acsAccessToken.AccessToken, "profiles")

	acsUserInfo := &AcsUserInfo{}
	response, err := s.Client.R().SetResult(acsUserInfo).Get(url)
	if err != nil {
		log.Error(err)
		return nil, err
	}
	if response.StatusCode() != http.StatusOK {
		errorStatus := fmt.Sprintf("get acs token status error:%v", response.StatusCode())
		log.Error(errorStatus)
		return nil, errors.New(errorStatus)
	}

	if acsUserInfo.Error != "" {
		log.Error(acsUserInfo.Error)
		return nil, errors.New(acsUserInfo.ErrorDescription)
	}

	return acsUserInfo, nil
}

// https://accounts.mobvista.com/v2/user/batch-base-info?time={时间戳}&client_id={client_id}&sign=23987c952de8f2ff52730865b4ffeb1d4adc1003
// 签名规则为：除了sign以外的所有字段名称的自然正序排序后的值连接后生成的字符串再连接client_secret的sha1哈希值。
func (s *acsService) SyncUserFromAcs(ctx context.Context) error {
	_, log := tools.GenContext(ctx)
	url := "https://accounts.mobvista.com/v2/user/batch-base-info?client_id=1089&sign=705389a7a58ab6ded81664ef00f806cd3d4288bd"
	response, err := s.Client.R().Get(url)
	if err != nil {
		log.Error(err)
		return err
	}
	if response.StatusCode() != http.StatusOK {
		errorStatus := fmt.Sprintf("SyncUserFromAcs status error:%v", response.StatusCode())
		log.Error(errorStatus)
		return errs.ErrorAcsAuth
	}

	userProfiles := []Profiles{}
	err = json.Unmarshal(response.Body(), &userProfiles)
	if err != nil {
		log.Error(err)
		return err
	}

	userModels := []model.UserModel{}
	for _, userProfile := range userProfiles {
		if userProfile.Status != 10 {
			continue
		}
		userModel := model.UserModel{
			Id:          userProfile.Id,
			AcsId:       userProfile.Id,
			Username:    userProfile.Username,
			RealName:    userProfile.RealName,
			Email:       userProfile.Email,
			Nid:         userProfile.Nid,
			DdUserId:    userProfile.DdUserId,
			CompanyId:   1,
			UpdatedTime: time.Now().UnixMilli(),
		}
		userModels = append(userModels, userModel)
	}

	err = db.UserManager.CreateUserBatch(ctx, userModels)
	if err != nil {
		log.Error(err)
		return err
	}
	return nil
}
