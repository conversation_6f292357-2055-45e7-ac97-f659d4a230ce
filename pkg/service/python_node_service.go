package service

import (
	"context"
	"encoding/json"
	"jarvis_api/pkg/db"
	"jarvis_api/pkg/model"
	"jarvis_api/pkg/vo"
	"jarvis_api/tools"

	"github.com/aws/smithy-go/ptr"
	"github.com/jinzhu/copier"
)

var PythonNodeService = &pythonNodeService{}

type pythonNodeService struct {
}

func (s *pythonNodeService) CreatePythonNode(ctx context.Context, id int64, createReq *vo.CreatePythonNodeReq) error {
	createPythonNodeModel := &model.PythonNodeModel{}
	err := copier.Copy(createPythonNodeModel, createReq)
	if err != nil {
		return err
	}
	createPythonNodeModel.Id = id
	createPythonNodeModel.CreateUserId = ptr.Int64(tools.GetUserId(ctx))
	createPythonNodeModel.MacroList = stringPtr(macro2Str(createReq.MacroList))
	createPythonNodeModel.Router = stringPtr(routers2Str(createReq.Routers))

	err = db.PythonNodeManager.Create(ctx, createPythonNodeModel)
	if err != nil {
		return err
	}
	return nil
}

func (s *pythonNodeService) ListPythonNode(ctx context.Context, appId int64) ([]vo.PythonNodeRes, error) {
	_, log := tools.GenContext(ctx)
	log.Info("ListPythonNode")

	userId := tools.GetUserId(ctx)
	pythonNodeModels, err := db.PythonNodeManager.ListPythonNode(ctx, userId, appId)
	if err != nil {
		log.Error(err)
		return nil, err
	}

	pythonNodeList := []vo.PythonNodeRes{}
	for _, pythonNodeModel := range pythonNodeModels {
		pythonNodeRes := &vo.PythonNodeRes{}
		err = copier.Copy(pythonNodeRes, &pythonNodeModel)
		if err != nil {
			log.Error(err)
			return nil, err
		}
		taskMacroList := []vo.TaskMacro{}
		err = json.Unmarshal([]byte(*pythonNodeModel.MacroList), &taskMacroList)
		if err != nil {
			log.Error(err)
			return nil, err
		}
		pythonNodeRes.MacroList = taskMacroList

		routers := []vo.Router{}
		if pythonNodeModel.Router != nil && *pythonNodeModel.Router != "" {
			err = json.Unmarshal([]byte(*pythonNodeModel.Router), &routers)
			if err != nil {
				log.Error(err)
				return nil, err
			}
		}
		pythonNodeRes.Routers = routers

		pythonNodeList = append(pythonNodeList, *pythonNodeRes)
	}
	return pythonNodeList, nil
}

func (s *pythonNodeService) GetPythonNode(ctx context.Context, id int64) (vo.PythonNodeRes, error) {
	_, log := tools.GenContext(ctx)
	log.Info("GetPythonNode")

	pythonNodeModel, err := db.PythonNodeManager.Get(ctx, id)
	if err != nil {
		log.Error(err)
		return vo.PythonNodeRes{}, err
	}

	var pythonNodeRes vo.PythonNodeRes
	err = copier.Copy(&pythonNodeRes, &pythonNodeModel)
	if err != nil {
		log.Error(err)
		return vo.PythonNodeRes{}, err
	}
	taskMacroList := []vo.TaskMacro{}
	err = json.Unmarshal([]byte(*pythonNodeModel.MacroList), &taskMacroList)
	if err != nil {
		log.Error(err)
		return vo.PythonNodeRes{}, err
	}
	pythonNodeRes.MacroList = taskMacroList

	routers := []vo.Router{}
	if pythonNodeModel.Router != nil && *pythonNodeModel.Router != "" {
		err = json.Unmarshal([]byte(*pythonNodeModel.Router), &routers)
		if err != nil {
			log.Error(err)
			return vo.PythonNodeRes{}, err
		}
	}
	pythonNodeRes.Routers = routers
	return pythonNodeRes, nil
}

func (s *pythonNodeService) UpdatePythonNode(ctx context.Context, updateReq *vo.UpdatePythonNodeReq) error {
	_, log := tools.GenContext(ctx)
	log.Info("UpdatePythonNode")

	pythonNodeModel, err := db.PythonNodeManager.Get(ctx, updateReq.Id)
	if err != nil {
		log.Error(err)
		return err
	}

	err = copier.Copy(pythonNodeModel, updateReq)
	if err != nil {
		log.Error(err)
		return err
	}

	pythonNodeModel.MacroList = stringPtr(macro2Str(*updateReq.MacroList))
	pythonNodeModel.Router = stringPtr(routers2Str(*updateReq.Routers))

	err = db.PythonNodeManager.Update(ctx, pythonNodeModel)
	if err != nil {
		log.Error(err)
		return err
	}
	return nil
}

func (s *pythonNodeService) DeletePythonNode(ctx context.Context, id int64) error {
	_, log := tools.GenContext(ctx)
	log.Info("DeletePythonNode")

	err := db.PythonNodeManager.Delete(ctx, id)
	if err != nil {
		log.Error(err)
		return err
	}
	return nil
}
