package service

import (
	"context"
	"encoding/json"
	"fmt"
	"jarvis_api/pkg/db"
	"jarvis_api/pkg/model"
	"jarvis_api/pkg/vo"
	"jarvis_api/tools"

	"github.com/jinzhu/copier"
	"gitlab.mobvista.com/spotmax/cncf_util/common"
)

var TaskService = &taskService{}

type taskService struct {
}

func macro2Str(list []vo.TaskMacro) string {
	if list == nil {
		list = make([]vo.TaskMacro, 0)
	}
	result, _ := json.Marshal(list)
	return string(result)
}
func routers2Str(list []vo.Router) string {
	if list == nil {
		list = make([]vo.Router, 0)
	}
	result, _ := json.Marshal(list)
	return string(result)
}
func dependentTasks2Str(list []string) string {
	if list == nil {
		list = make([]string, 0)
	}
	result, _ := json.Marshal(list)
	return string(result)
}

func (s *taskService) Create(ctx context.Context, createReq *vo.CreateTaskReq) (int64, error) {
	taskModel := &model.TaskModel{}
	err := copier.Copy(taskModel, createReq)
	if err != nil {
		return 0, err
	}
	id := tools.GenId()
	taskModel.Id = id
	taskModel.CreateUserId = tools.GetUserId(ctx)

	taskModel.ExtInfo = str2Json(createReq.ExtInfo)
	taskModel.MacroList = stringPtr(macro2Str(createReq.MacroList))
	taskModel.DependentTasks = stringPtr(dependentTasks2Str(createReq.DependentTasks))

	err = db.TaskManager.Create(ctx, taskModel)
	if err != nil {
		return 0, err
	}
	return id, nil
}

func taskModelToRes(obj *model.TaskModel) (*vo.TaskRes, error) {
	var res vo.TaskRes
	err := copier.Copy(&res, &obj)
	if err != nil {
		return nil, err
	}

	subList := make([]vo.TaskMacro, 0) // 必须分配内存，否则无法序列化为[]
	if obj.MacroList != nil {          // 早期数据可能为null，所以要判断
		err = json.Unmarshal([]byte(*obj.MacroList), &subList)
		if err != nil {
			return nil, err
		}
	}
	res.MacroList = subList
	dependentTasks := make([]string, 0)
	if obj.DependentTasks != nil {
		err = json.Unmarshal([]byte(*obj.DependentTasks), &dependentTasks)
		if err != nil {
			return nil, err
		}
	}
	res.DependentTasks = dependentTasks
	return &res, nil
}

func (s *taskService) Get(ctx context.Context, taskId int64) (*vo.TaskRes, error) {
	obj, err := db.TaskManager.Get(ctx, taskId)
	if err != nil {
		return nil, err
	}
	return taskModelToRes(obj)
}

func (s *taskService) List(ctx context.Context, teamIdInt int64) ([]*vo.TaskRes, error) {
	userId := tools.GetUserId(ctx)
	taskList, err := db.TaskManager.List(ctx, userId, teamIdInt)
	if err != nil {
		return nil, err
	}

	list := make([]*vo.TaskRes, 0, len(taskList))
	for _, obj := range taskList {
		res, err := taskModelToRes(&obj)
		if err != nil {
			return nil, err
		}
		list = append(list, res)
	}
	return list, nil
}

func (s *taskService) ListForce(ctx context.Context, teamIdInt int64) ([]*vo.TaskRes, error) {
	taskList, err := db.TaskManager.ListForce(ctx, teamIdInt)
	if err != nil {
		return nil, err
	}

	list := make([]*vo.TaskRes, 0, len(taskList))
	for _, obj := range taskList {
		res, err := taskModelToRes(&obj)
		if err != nil {
			return nil, err
		}
		list = append(list, res)
	}
	return list, nil
}

func (s *taskService) ownerCheck(ctx context.Context, taskId int64) error {
	if taskId <= 0 {
		return nil
	}

	task, err := s.Get(ctx, taskId)
	if err != nil {
		return err
	}
	count := db.AppCollaborationManager.GetAppCollaboration(ctx, task.AppId, tools.GetUserId(ctx))
	if count > 0 {
		return nil
	}

	otherIds, err := db.TaskManager.PickupOtherTasks(ctx, tools.GetUserId(ctx), []int64{taskId})
	if err != nil {
		return err
	}
	if len(otherIds) > 0 {
		return fmt.Errorf("禁止修改别人的ids: %s", common.PrettifyJson(otherIds, false))
	}
	return nil
}

func (s *taskService) Update(ctx context.Context, updateReq *vo.UpdateTaskReq) error {
	err := s.ownerCheck(ctx, updateReq.Id)
	if err != nil {
		return err
	}

	taskModel := &model.TaskModel{}
	err = copier.Copy(taskModel, updateReq)
	if err != nil {
		return err
	}
	if updateReq.MacroList != nil {
		taskModel.MacroList = stringPtr(macro2Str(*updateReq.MacroList))
	}
	if updateReq.DependentTasks != nil {
		taskModel.DependentTasks = stringPtr(dependentTasks2Str(*updateReq.DependentTasks))
	}

	err = db.TaskManager.Update(ctx, taskModel)
	if err != nil {
		return err
	}
	return nil
}

// 只删task，不删agent
func (s *taskService) Delete(ctx context.Context, deleteReq *vo.DeleteTaskReq) error {
	if len(deleteReq.Ids) == 0 {
		return nil
	}
	for _, taskId := range deleteReq.Ids {
		err := s.ownerCheck(ctx, taskId)
		if err != nil {
			return err
		}
	}
	return db.TaskManager.Delete(ctx, deleteReq.Ids)
}

func (s *taskService) Copy(ctx context.Context, copyReq *vo.CopyTaskReq) (newTaskId, newAgentId int64, err error) {
	taskModel, err := db.TaskManager.Get(ctx, copyReq.Id)
	if err != nil {
		return 0, 0, err
	}

	if tools.GetUserId(ctx) != taskModel.CreateUserId &&
		!copyReq.ForceCopy && (taskModel.Copyable == nil || !*taskModel.Copyable) {
		return 0, 0, fmt.Errorf("task '%d' copyable is false", copyReq.Id)
	}

	taskRes, err := taskModelToRes(taskModel)
	if err != nil {
		return 0, 0, err
	}

	createReq := &vo.CreateTaskReq{}
	err = copier.Copy(&createReq, &taskRes)
	if err != nil {
		return
	}

	// 新复制的task 默认不开启复制（不允许复制）
	createReq.Copyable = false

	// 1. copy name
	if copyReq.Name != "" {
		createReq.Name = copyReq.Name
	}
	createReq.AppId = copyReq.AppId
	createReq.AgentTeamId = copyReq.AgentTeamId
	if taskRes.AgentId > 0 {
		copyAgent := &vo.CopyAgentReq{
			Id:          taskRes.AgentId,
			Name:        "",
			AppId:       copyReq.AppId,
			AgentTeamId: copyReq.AgentTeamId,
			ForceCopy:   true,
		}
		newAgentId, err = AgentService.Copy(ctx, copyAgent)
		if err != nil {
			return
		}
		createReq.AgentId = newAgentId
	}

	if createReq.ToolIds == "" {
		createReq.ToolIds = "[]"
	}
	newTaskId, err = s.Create(ctx, createReq)
	if err != nil {
		return
	}
	return
}
