package service

import (
	"context"
	"io"
	"jarvis_api/tools"
	"os"
	"strings"

	"github.com/aws/aws-sdk-go-v2/aws"
	"github.com/aws/aws-sdk-go-v2/credentials"

	"github.com/aws/aws-sdk-go-v2/config"

	"github.com/aws/aws-sdk-go-v2/service/s3"
)

var S3Service = &s3Service{}

type s3Service struct {
}

const MAXAGENT_S3_REGION = "ap-northeast-1"
const MAXAGENT_S3_BUCKET = "spotmax-maxagent"

/*
组装oss配置
*/
func getS3Config(ctx context.Context) (*s3.Client, error) {
	// 创建一个新的会话
	access_key := os.Getenv("AWS_S3_ACCESS_KEY_ID")
	secret_key := os.Getenv("AWS_S3_SECRET_ACCESS_KEY")

	sdkConfig, err := config.LoadDefaultConfig(ctx,
		config.WithRegion(MAXAGENT_S3_REGION),
		config.WithCredentialsProvider(credentials.NewStaticCredentialsProvider(access_key, secret_key, "")))
	if err != nil {
		return nil, err
	}
	s3Client := s3.NewFromConfig(sdkConfig)
	return s3Client, nil
}

func (p *s3Service) GetFile(ctx context.Context, s3Path string) (*s3.GetObjectOutput, error) {
	ctx, clog := tools.GenContext(ctx)
	log := clog.WithField("s3Path", s3Path)
	s3client, err := getS3Config(ctx)
	if err != nil {
		log.Error(err)
		return nil, err
	}

	if strings.HasPrefix(s3Path, MAXAGENT_S3_BUCKET) {
		s3Path = s3Path[len(MAXAGENT_S3_BUCKET)+1:]
	}

	response, err := s3client.GetObject(ctx, &s3.GetObjectInput{
		Bucket: aws.String(MAXAGENT_S3_BUCKET),
		Key:    &s3Path,
	})
	if err != nil {
		log.Error(err)
		return nil, err
	}
	return response, nil
}

/*
上传文件到oss服务
*/
func (p *s3Service) UploadToS3ByIo(ctx context.Context, s3Path string, reader io.Reader) error {
	ctx, log := tools.GenContext(ctx)
	s3client, err := getS3Config(ctx)
	if err != nil {
		log.Error(err)
		return err
	}

	_, err = s3client.PutObject(ctx, &s3.PutObjectInput{
		Bucket: aws.String(MAXAGENT_S3_BUCKET),
		Key:    &s3Path,
		Body:   reader,
	})

	if err != nil {
		log.Error(err)
		return err
	}
	return err
}

func (p *s3Service) DeleteFile(ctx context.Context, s3Path string) error {
	ctx, log := tools.GenContext(ctx)
	s3client, err := getS3Config(ctx)
	if err != nil {
		log.Error(err)
		return err
	}

	if strings.HasPrefix(s3Path, MAXAGENT_S3_BUCKET) {
		s3Path = s3Path[len(MAXAGENT_S3_BUCKET)+1:]
	}

	_, err = s3client.DeleteObject(ctx, &s3.DeleteObjectInput{
		Bucket: aws.String(MAXAGENT_S3_BUCKET),
		Key:    &s3Path,
	})

	if err != nil {
		log.Error(err)
		return err
	}
	return err
}
