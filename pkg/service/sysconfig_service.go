package service

import (
	"context"
	"jarvis_api/pkg/model"
	"jarvis_api/pkg/vo"

	"jarvis_api/tools"

	"jarvis_api/pkg/db"

	"gorm.io/gorm"
)

var SysConfigService = &sysConfigService{}

type sysConfigService struct {
}

// 获取系统配置
func (s *sysConfigService) GetSysconfig(ctx context.Context, request *vo.SysconfigRequest) (string, error) {
	_, log := tools.GenContext(ctx)

	config, err := db.SysconfigManager.FindSysConfig(model.Sysconfig{Key: request.Key})
	if err != nil {
		log.Error(err)
		return "", err
	}

	return config.Value, nil
}

func (s *sysConfigService) SaveSysconfig(ctx context.Context, request *vo.SaveSysconfigReq) error {
	_, log := tools.GenContext(ctx)

	config, err := db.SysconfigManager.FindSysConfig(model.Sysconfig{Key: request.Key})
	if err != nil && err != gorm.ErrRecordNotFound {
		log.Error(err)
		return err
	}

	// 查询如果存在则修改，否则新增
	if config.Id > 0 {
		config.Name = request.Name
		config.Value = request.Value
		config.Desc = request.Desc
		err = db.SysconfigManager.UpdateSysConfig(ctx, config)
		if err != nil {
			log.Error(err)
			return err
		}
		return nil
	}

	sysConfig := &model.Sysconfig{
		Name:  request.Name,
		Key:   request.Key,
		Value: request.Value,
		Desc:  request.Desc,
	}

	err = db.SysconfigManager.AddSysConfig(ctx, sysConfig)
	if err != nil {
		log.Error(err)
		return err
	}
	return nil
}
