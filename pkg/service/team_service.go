package service

import (
	"context"
	"encoding/json"
	"fmt"
	"jarvis_api/pkg/db"
	"jarvis_api/pkg/model"
	"jarvis_api/pkg/vo"
	"jarvis_api/tools"
	"strconv"
	"strings"

	"github.com/aws/aws-sdk-go-v2/aws"
	"github.com/aws/smithy-go/ptr"
	"github.com/jinzhu/copier"
	"github.com/tidwall/gjson"
	"gitlab.mobvista.com/spotmax/cncf_util/common"
)

var TeamService = &teamService{}

type teamService struct {
}

func (s *teamService) Create(ctx context.Context, id int64, createReq *vo.CreateTeamReq) (int64, error) {
	teamModel := &model.TeamModel{}
	err := copier.Copy(teamModel, createReq)
	if err != nil {
		return 0, err
	}

	teamModel.Id = id
	teamModel.CreateUserId = ptr.Int64(tools.GetUserId(ctx))

	teamModel.TaskSort = str2Json(createReq.TaskSort)

	err = db.TeamManager.Create(ctx, teamModel)
	if err != nil {
		return 0, err
	}
	return id, nil
}

func (s *teamService) List(ctx context.Context, appId int64) ([]model.TeamModel, error) {
	userId := tools.GetUserId(ctx)
	return db.TeamManager.List(ctx, userId, appId)
}

func (s *teamService) ListForce(ctx context.Context, appId int64) ([]model.TeamModel, error) {
	return db.TeamManager.ListForce(ctx, appId)
}

func (s *teamService) Get(ctx context.Context, teamId int64) (*model.TeamModel, error) {
	obj, err := db.TeamManager.Get(ctx, teamId)
	if err != nil {
		return nil, err
	}
	return obj, nil
}

func (s *teamService) GetMacroList(ctx context.Context, teamId int64) ([]vo.TaskMacro, error) {
	taskList, err := TaskService.ListForce(ctx, teamId)
	if err != nil {
		return nil, err
	}

	ignoreMap := make(map[string]bool)
	macroList := make([]vo.TaskMacro, 0, 10)
	for _, v := range taskList {
		for _, m := range v.MacroList {
			if ignoreMap[m.Name] {
				continue
			}
			ignoreMap[m.Name] = true
			macroList = append(macroList, m)
		}
	}
	return macroList, nil
}

func (s *teamService) ownerCheck(ctx context.Context, teamId int64) error {
	if teamId <= 0 {
		return nil
	}

	team, err := s.Get(ctx, teamId)
	if err != nil {
		return err
	}
	count := db.AppCollaborationManager.GetAppCollaboration(ctx, ptr.ToInt64(team.AppId), tools.GetUserId(ctx))
	if count > 0 {
		return nil
	}

	otherIds, err := db.TeamManager.PickupOtherTasks(ctx, tools.GetUserId(ctx), []int64{teamId})
	if err != nil {
		return err
	}
	if len(otherIds) > 0 {
		return fmt.Errorf("禁止修改别人的ids: %s", common.PrettifyJson(otherIds, false))
	}
	return nil
}

func (s *teamService) Update(ctx context.Context, updateReq *vo.UpdateTeamReq) error {
	err := s.ownerCheck(ctx, updateReq.Id)
	if err != nil {
		return err
	}

	teamModel := &model.TeamModel{}
	err = copier.Copy(teamModel, updateReq)
	if err != nil {
		return err
	}

	err = db.TeamManager.Update(ctx, teamModel)
	if err != nil {
		return err
	}
	return nil
}

func (s *teamService) Delete(ctx context.Context, deleteReq *vo.DeleteTeamReq) error {
	err := s.ownerCheck(ctx, deleteReq.Id)
	if err != nil {
		return err
	}

	taskList, err := TaskService.List(ctx, deleteReq.Id)
	if err != nil {
		return err
	}
	if len(taskList) > 0 {
		taskIds := make([]int64, 0, len(taskList))
		for _, task := range taskList {
			taskIds = append(taskIds, task.Id)
		}
		err = TaskService.Delete(ctx, &vo.DeleteTaskReq{Ids: taskIds})
		if err != nil {
			return err
		}
	}

	agentList, err := AgentService.List(ctx, deleteReq.Id)
	if err != nil {
		return err
	}
	if len(agentList) > 0 {
		agentIds := make([]int64, 0, len(agentList))
		for _, agent := range agentList {
			agentIds = append(agentIds, agent.Id)
		}
		err = AgentService.Delete(ctx, &vo.DeleteAgentReq{Ids: agentIds})
		if err != nil {
			return err
		}
	}

	return db.TeamManager.Delete(ctx, deleteReq.Id)
}

func (s *teamService) Copy(ctx context.Context, copyReq *vo.CopyTeamReq) (int64, error) {
	teamRes, err := s.Get(ctx, copyReq.Id)
	if err != nil {
		return -1, err
	}

	if tools.GetUserId(ctx) != aws.ToInt64(teamRes.CreateUserId) &&
		!copyReq.ForceCopy && (teamRes.Copyable == nil || !*teamRes.Copyable) {
		return 0, fmt.Errorf("team '%d' copyable is false", copyReq.Id)
	}

	createReq := &vo.CreateTeamReq{}
	err = copier.Copy(&createReq, &teamRes)
	if err != nil {
		return -1, err
	}
	// 新复制的team 默认不开启复制（不允许复制）
	createReq.Copyable = false

	newTeamId := tools.GenId()

	// 1. copy Name
	if copyReq.Name != "" {
		createReq.Name = copyReq.Name
	}

	// 2. copy task
	taskList, err := TaskService.ListForce(ctx, copyReq.Id)
	if err != nil {
		return -1, err
	}
	copiedTaskMap := make(map[int64]int64, len(taskList))
	copiedAgentMap := make(map[int64]int64, len(taskList))
	for _, task := range taskList {
		copyTask := &vo.CopyTaskReq{
			Id:          task.Id,
			Name:        "",
			AppId:       copyReq.AppId,
			AgentTeamId: newTeamId,
			ForceCopy:   true,
		}
		newTaskId, newAgentId, err := TaskService.Copy(ctx, copyTask)
		if err != nil {
			return -1, err
		}
		copiedTaskMap[task.Id] = newTaskId
		copiedAgentMap[task.AgentId] = newAgentId
	}

	// 3. copy other agent
	agentList, err := AgentService.ListForce(ctx, copyReq.Id)
	if err != nil {
		return -1, err
	}
	for _, agent := range agentList {
		if _, exist := copiedAgentMap[agent.Id]; exist {
			continue
		}
		copyAgent := &vo.CopyAgentReq{
			Id:          agent.Id,
			Name:        "",
			AppId:       copyReq.AppId,
			AgentTeamId: newTeamId,
			ForceCopy:   true,
		}
		newAgentId, err := AgentService.Copy(ctx, copyAgent)
		if err != nil {
			return -1, err
		}
		copiedAgentMap[agent.Id] = newAgentId
	}

	// 4. replace task_sort
	createReq.AppId = copyReq.AppId

	newTaskSort, err := replaceSlice(createReq.TaskSort, copiedTaskMap)
	if err != nil {
		return -1, err
	}
	createReq.TaskSort = newTaskSort

	// 5. create obj
	id, err := s.Create(ctx, newTeamId, createReq)
	if err != nil {
		return -1, err
	}
	return id, nil
}

func replaceSlice(jsonStr string, dict map[int64]int64) (string, error) {
	var data []string
	err := json.Unmarshal([]byte(jsonStr), &data)
	if err != nil {
		return "", err
	}

	// 创建一个新的 map 存储替换后的结果
	newData := make([]string, 0, len(data))

	// 遍历原始 map，替换 key 和 value
	for _, key := range data {
		keyInt, err := strconv.ParseInt(key, 10, 64)
		if err != nil {
			fmt.Println("Error converting value to int64:", err)
			continue
		}

		// 替换 key 和 value
		if newKey, ok := dict[keyInt]; ok {
			newData = append(newData, strconv.FormatInt(newKey, 10))
		} else {
			newData = append(newData, key)
		}
	}

	// 将替换后的 map 转换回 JSON 字符串
	newJsonStr, err := json.Marshal(newData)
	if err != nil {
		return "", err
	}
	return string(newJsonStr), nil
}

func replaceSubstrings(src string, dict map[int64]int64) string {
	// 遍历字典中的键值对
	for key, value := range dict {
		// 将 key 和 value 转换为字符串
		keyStr := strconv.FormatInt(key, 10)
		valueStr := strconv.FormatInt(value, 10)
		// 替换 src 中的 keyStr 为 valueStr
		src = strings.ReplaceAll(src, keyStr, valueStr)
	}
	return src
}

func (s *teamService) IsAutoCreateTeamJson(jsonMsg []byte) (string, bool) {
	typeResult := gjson.Get(string(jsonMsg), "type")
	if typeResult.String() != "result" {
		return "", false
	}

	dataResult := gjson.Get(string(jsonMsg), "data")
	if !dataResult.Exists() {
		return "", false
	}

	return dataResult.String(), true
}

type TeamJsonData struct {
	Name   string   `json:"name"`
	Agents []Agents `json:"agents"`
	Tasks  []Tasks  `json:"tasks"`
}
type Agents struct {
	Role            string   `json:"role"`
	Goal            string   `json:"goal"`
	Backstory       string   `json:"backstory"`
	Verbose         bool     `json:"verbose"`
	AllowDelegation bool     `json:"allow_delegation"`
	MaxIter         int      `json:"max_iter"`
	Llm             string   `json:"llm"`
	Tools           []string `json:"tools,omitempty"`
	MemorySpace     string   `json:"memory_space,omitempty"`
}
type Tasks struct {
	Name           string   `json:"name"`
	Description    string   `json:"description"`
	ExpectedOutput string   `json:"expected_output"`
	Agent          string   `json:"agent"`
	Tools          []string `json:"tools"`
	OutputFile     string   `json:"output_file"`
}

// var jsondata = `{"final_result": "{\n  \"agents\": [\n    {\n      \"role\": \"Search Strategy Coordinator\",\n      \"goal\": \"Orchestrate a team of specialized agents to collaboratively perform deep search (深度搜索) to solve complex problems, ensuring comprehensive coverage, accuracy, and user satisfaction.\",\n      \"backstory\": \"You are an expert in multi-agent orchestration, specializing in deep search methodologies. Your responsibility is to break down the problem, assign sub-tasks, monitor progress, and ensure the final solution is robust and meets the user's needs. You always confirm with the user before finalizing the solution.\",\n      \"verbose\": true,\n      \"allow_delegation\": true,\n      \"max_iter\": 20,\n      \"llm\": \"gpt-4.1\"\n    },\n    {\n      \"role\": \"Problem Decomposer\",\n      \"goal\": \"Analyze the given problem and decompose it into well-defined, manageable sub-problems suitable for deep search.\",\n      \"backstory\": \"You are a senior systems analyst with a knack for breaking down complex issues into actionable components. You ensure that each sub-problem is clearly defined and ready for targeted search and solution.\",\n      \"verbose\": true,\n      \"allow_delegation\": false,\n      \"max_iter\": 10,\n      \"llm\": \"gpt-4.1\"\n    },\n    {\n      \"role\": \"Knowledge Search Specialist\",\n      \"goal\": \"Perform in-depth searches using multiple knowledge sources (Google, Wikipedia, arXiv, news, web scraping) to gather comprehensive, relevant information for each sub-problem.\",\n      \"backstory\": \"You are a research specialist with expertise in advanced search techniques and information retrieval. You know how to leverage various tools to extract the most relevant and up-to-date information.\",\n      \"verbose\": true,\n      \"allow_delegation\": false,\n      \"tools\": [\n        \"google_search\",\n        \"wikipedia\",\n        \"arxiv\",\n        \"asknews\",\n        \"scrape_website_tool\",\n        \"serper_dev_tool\"\n      ],\n      \"max_iter\": 15,\n      \"llm\": \"gpt-4.1\"\n    },\n    {\n      \"role\": \"Synthesis & Reasoning Agent\",\n      \"goal\": \"Integrate and synthesize the information collected from all sources, perform deep reasoning, and propose candidate solutions for each sub-problem.\",\n      \"backstory\": \"You are an AI reasoning expert, skilled at connecting disparate pieces of information and drawing logical conclusions. You ensure that the synthesized knowledge is coherent and actionable.\",\n      \"verbose\": true,\n      \"allow_delegation\": false,\n      \"tools\": [\n        \"safe_code_interpreter\"\n      ],\n      \"max_iter\": 10,\n      \"llm\": \"gpt-4.1\"\n    },\n    {\n      \"role\": \"Solution Validator\",\n      \"goal\": \"Critically evaluate the proposed solutions for correctness, completeness, and relevance. Identify gaps or uncertainties and request further search or clarification if needed.\",\n      \"backstory\": \"You are a quality assurance specialist with a background in logic and critical thinking. Your job is to ensure that the final solution is robust, well-supported, and meets the user's requirements.\",\n      \"verbose\": true,\n      \"allow_delegation\": false,\n      \"max_iter\": 10,\n      \"llm\": \"gpt-4.1\"\n    },\n    {\n      \"role\": \"User Feedback Collector\",\n      \"goal\": \"Collect user feedback on the proposed solution, extract actionable insights, and communicate them to the team for iterative improvement. Store user preferences in long-term memory.\",\n      \"backstory\": \"You are a user experience specialist, adept at interpreting feedback and ensuring the user's voice is central to the solution process.\",\n      \"verbose\": true,\n      \"allow_delegation\": false,\n      \"tools\": [\n        \"human_feed_back\"\n      ],\n      \"memory_space\": \"deep_search_solution\",\n      \"max_iter\": 10,\n      \"llm\": \"gpt-4.1\"\n    }\n  ],\n  \"tasks\": [\n    {\n      \"description\": \"Solve the given problem using a deep search approach (深度搜索解决问题). Follow these steps:\\n1. Decompose the problem into sub-problems.\\n2. For each sub-problem, perform comprehensive searches using all available knowledge tools.\\n3. Synthesize and reason over the collected information to propose candidate solutions.\\n4. Validate the solutions for correctness and completeness.\\n5. Present the integrated solution to the user and collect feedback.\\n6. Revise the solution iteratively based on user feedback until the user confirms satisfaction.\\nOnly finalize the solution after explicit user confirmation.\",\n      \"expected_output\": \"A detailed, step-by-step solution to the original problem, including reasoning, sources, and a summary of the deep search process. Output should be in markdown format.\",\n      \"agent\": \"Search Strategy Coordinator\",\n      \"tools\": [],\n      \"output_file\": \"deep_search_solution.md\"\n    }\n  ]\n}"}`

func (s *teamService) AutoCreateTeam(ctx context.Context, autoCreateTeamReq *vo.AutoCreateTeamReq, final_result_json string) (int64, error) {
	jsonDataObj := gjson.Parse(final_result_json)
	teamJsonStr := jsonDataObj.Get("final_result").String()

	// 处理可能的Markdown格式JSON字符串
	teamJsonStr = strings.TrimSpace(teamJsonStr)
	teamJsonStr, _ = strings.CutPrefix(teamJsonStr, "```json")
	teamJsonStr, _ = strings.CutPrefix(teamJsonStr, "```")
	teamJsonStr = strings.TrimSuffix(teamJsonStr, "```")
	teamJsonStr = strings.TrimSpace(teamJsonStr)

	teamObj := &TeamJsonData{}
	err := json.Unmarshal([]byte(teamJsonStr), teamObj)
	if err != nil {
		fmt.Println("json.Unmarshal err:", err)
		return 0, err
	}

	if teamObj.Agents == nil || teamObj.Tasks == nil {
		fmt.Println("teamJsonData.Agents or teamJsonData.Tasks is nil, teamJsonData:", teamObj)
		return 0, err
	}

	// userId := tools.GetUserId(ctx)
	teamId := tools.GenId()

	// 获取Tool key 和id 的映射关系
	toolRes, err := ToolService.List(ctx)
	if err != nil {
		fmt.Println("ToolService.List err:", err)
		return 0, err
	}
	toolMap := make(map[string]int64)
	for _, tool := range toolRes {
		key := strings.TrimPrefix(tool.ToolKey, "mcp:")
		toolMap[key] = tool.Id
	}

	// 1. 创建agents
	agentIdMap := make(map[string]int64)
	for _, agent := range teamObj.Agents {
		toolIds := []string{}
		for _, tool := range agent.Tools {
			if toolId, exist := toolMap[tool]; exist {
				toolIds = append(toolIds, strconv.FormatInt(toolId, 10))
			}
		}

		toolIdsStr := "[]"

		if len(toolIds) > 0 {
			if toolIdsBytes, err := json.Marshal(toolIds); err == nil {
				toolIdsStr = string(toolIdsBytes)
			}
		}

		createAgentReq := vo.CreateAgentReq{
			Name:            agent.Role,
			Remark:          agent.Goal,
			Copyable:        false,
			AppId:           autoCreateTeamReq.AppId,
			AgentTeamId:     teamId,
			Role:            agent.Role,
			Goal:            agent.Goal,
			Backstory:       agent.Backstory,
			Verbose:         agent.Verbose,
			AllowDelegation: agent.AllowDelegation,
			Multimodal:      false,
			Memory:          false,
			MaxIter:         int64(agent.MaxIter),
			ToolIds:         toolIdsStr,
			LlmId:           2, // default gpt4.1
			Knowledges:      []vo.Knowledge{},
			ExtInfo:         "",
			MemorySpace:     "",
		}

		id, err := AgentService.Create(ctx, &createAgentReq)
		if err != nil {
			fmt.Println("AgentService.Create err:", err)
			return 0, err
		}
		agentIdMap[agent.Role] = id
	}

	// 创建tasks
	taskIds := []string{}
	for index, task := range teamObj.Tasks {
		agentId, exist := agentIdMap[task.Agent]
		if !exist {
			fmt.Println("agent not exist, task:", task)
			continue
		}

		toolIds := []string{}
		for _, tool := range task.Tools {
			if toolId, exist := toolMap[tool]; exist {
				toolIds = append(toolIds, strconv.FormatInt(toolId, 10))
			}
		}

		toolIdsStr := "[]"
		if len(toolIds) > 0 {
			if toolIdsBytes, err := json.Marshal(toolIds); err == nil {
				toolIdsStr = string(toolIdsBytes)
			}
		}

		description := task.Description
		macroList := []vo.TaskMacro{}
		if index == 0 && autoCreateTeamReq.OpenChatMode {
			description = description + "\n 1.Here's the conversation context: {context} \n 2.Here's the user's question/requirement: {requirement}"
			// [{"desc": "", "name": "requirement", "macro_type": "text", "default_value": ""}, {"desc": "", "name": "context", "macro_type": "text", "default_value": ""}]
			macroList = []vo.TaskMacro{
				{
					Name:         "context",
					MacroType:    "text",
					DefaultValue: ptr.String(""),
					Desc:         "",
				},
				{
					Name:         "requirement",
					MacroType:    "text",
					DefaultValue: ptr.String(""),
					Desc:         "",
				},
			}
		}

		createTaskReq := vo.CreateTaskReq{
			Name:           task.Name,
			Remark:         "",
			Copyable:       false,
			AppId:          autoCreateTeamReq.AppId,
			AgentTeamId:    teamId,
			AgentId:        agentId,
			AsyncExecution: false,
			DependentTasks: []string{},
			Description:    description,
			ExpectedOutput: task.ExpectedOutput,
			OutputFile:     task.OutputFile,
			ExtInfo:        "",
			MacroList:      macroList,
			MemorySpace:    "",
			MemoryCategory: "",
			ToolIds:        toolIdsStr,
			OutputStruct:   "",
		}

		id, err := TaskService.Create(ctx, &createTaskReq)
		if err != nil {
			fmt.Println("TaskService.Create err:", err)
			return 0, err
		}
		taskIds = append(taskIds, strconv.FormatInt(id, 10))
	}

	// 创建team
	taskIdsBytes, err := json.Marshal(taskIds)
	if err != nil {
		fmt.Println("json.Marshal err:", err)
		return 0, err
	}

	teamName := teamObj.Name
	if teamName == "" {
		teamName = autoCreateTeamReq.Name
	}
	createTeamReq := vo.CreateTeamReq{
		Name:            teamName,
		Remark:          autoCreateTeamReq.Requirement,
		Copyable:        false,
		AppId:           autoCreateTeamReq.AppId,
		TaskSort:        string(taskIdsBytes),
		ResultParamsKey: "",
		IsPlanning:      false,
		PlanningLlmId:   0,
		PythonStruct:    "",
		ExtInfo:         "",
	}

	_, err = s.Create(ctx, teamId, &createTeamReq)
	if err != nil {
		fmt.Println("TeamService.Create err:", err)
		return 0, err
	}

	return teamId, nil
}
