package service

import (
	"context"
	"jarvis_api/pkg/db"
	"jarvis_api/pkg/vo"
	"jarvis_api/tools"
	"testing"

	"github.com/davecgh/go-spew/spew"
	"github.com/stretchr/testify/assert"
)

// https://accounts.mobvista.com/authorize?response_type=code&client_id=1089&redirect_uri=http://localhost:9908/login
func Test_acsService_GetAccessToken(t *testing.T) {
	acsLoginReq := &vo.AcsLoginReq{
		Code: "NjVkZWYyNTUzYWMwMDhzj2I39CDWpKXKwCR4B8Qg7-eHhcfviGpsAnwv11Glbl-wggRkCqlgOpWuTA51jw",
		// RedirectUri: "https%3A%2F%2Fmaxcloud.spotmaxtech.com%2Flogin",
		RedirectUri: "http%3A%2F%2Flocalhost%3A9908%2Flogin",
	}
	acsAccessToken, err := AcsService.GetAccessToken(context.Background(), acsLoginReq)
	assert.Nil(t, err)
	spew.Dump(acsAccessToken)

}

func Test_acsService_GetUserInfo(t *testing.T) {

	acsAccessToken := &AcsAccessToken{
		AccessToken:      "NjU4NTFmODg0MzM5Yofv4yi0S63ipravrckpQPK8SnK3HrLH0bBxm0Z48-y5tXhkeinzRL79baVmJ0HzdA",
		RefreshToken:     "NjU4NTFmODg0MzQzMYKKJRJC9cjwcpjt31WaKvKwvyJXsllYSOaAp80KcJaykc5v62UI6t7ps3rVQEcsXg",
		ExpiresIn:        0,
		TokenType:        "",
		Error:            "",
		ErrorDescription: "",
	}
	acsUserInfo, err := AcsService.GetUserInfo(context.Background(), acsAccessToken)
	assert.Nil(t, err)
	spew.Dump(acsUserInfo)
}

func Test_acsService_SyncUserFromAcs(t *testing.T) {
	assert.Nil(t, db.InitDBManager())
	assert.Nil(t, tools.InitSnowflake())
	err := AcsService.SyncUserFromAcs(context.Background())
	assert.Nil(t, err)
}
