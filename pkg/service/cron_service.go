package service

import (
	"context"
	"jarvis_api/constant"

	"github.com/google/uuid"
	"github.com/robfig/cron/v3"
	"github.com/sirupsen/logrus"
)

func StartCron() error {
	// runjob := os.Getenv("runjob")
	// if runjob == "" || runjob != "true" {
	// 	return nil
	// }

	logger := &DaemonLogger{}
	c := cron.New(cron.WithChain(
		cron.Recover(logger), // or use cron.DefaultLogger
		cron.DelayIfStillRunning(logger),
	))

	// 每5分钟运行一次，清理pod
	_, _ = c.AddFunc("*/5 * * * *", func() {
		ctx := context.Background()
		ctx = context.WithValue(ctx, constant.MiddlewareKeyRequestId, uuid.New().String())

		_ = CleanPodService.CleanPod(ctx)
	})

	c.Start()
	return nil
}

type DaemonLogger struct {
}

func (p *DaemonLogger) Info(msg string, keysAndValues ...interface{}) {
	arg := []interface{}{msg}
	logrus.Info(append(arg, keysAndValues...))
}

func (p *DaemonLogger) Error(err error, msg string, keysAndValues ...interface{}) {
	arg := []interface{}{msg}
	logrus.Error(append(arg, keysAndValues...))
	logrus.Errorf("%+v", err)
}
