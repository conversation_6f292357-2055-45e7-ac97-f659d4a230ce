package service

import (
	"context"
	"encoding/json"
	"fmt"
	"jarvis_api/pkg/db"
	"jarvis_api/pkg/model"
	"jarvis_api/pkg/vo"
	"jarvis_api/tools"

	"github.com/aws/smithy-go/ptr"
	"github.com/jinzhu/copier"
	"gitlab.mobvista.com/spotmax/cncf_util/common"
)

var AgentService = &agentService{}

type agentService struct {
}

func (s *agentService) Create(ctx context.Context, createReq *vo.CreateAgentReq) (int64, error) {
	ctx, log := tools.GenContext(ctx)
	agentModel := &model.AgentModel{}
	err := copier.Copy(agentModel, createReq)
	if err != nil {
		return 0, err
	}
	id := tools.GenId()
	agentModel.Id = id
	agentModel.CreateUserId = tools.GetUserId(ctx)

	agentModel.ToolIds = str2Json(createReq.ToolIds)
	agentModel.ExtInfo = str2Json(createReq.ExtInfo)
	knowledgesJson, err := json.Marshal(createReq.Knowledges)
	if err != nil {
		log.Error(err)
		return 0, err
	}
	agentModel.Knowledges = stringPtr(string(knowledgesJson))

	err = db.AgentManager.Create(ctx, agentModel)
	if err != nil {
		log.Error(err)
		return 0, err
	}
	return id, nil
}

func (s *agentService) Get(ctx context.Context, agentId int64) (*model.AgentModel, error) {
	obj, err := db.AgentManager.Get(ctx, agentId)
	if err != nil {
		return nil, err
	}
	return obj, nil
}

func (s *agentService) List(ctx context.Context, teamIdInt int64) ([]vo.AgentRes, error) {
	ctx, log := tools.GenContext(ctx)
	userId := tools.GetUserId(ctx)
	agentList, err := db.AgentManager.List(ctx, userId, teamIdInt)
	if err != nil {
		log.Error(err)
		return nil, err
	}

	list := []vo.AgentRes{}
	for _, obj := range agentList {
		res := vo.AgentRes{}
		err = copier.Copy(&res, &obj)
		if err != nil {
			log.Error(err)
			return nil, err
		}
		if obj.Knowledges == nil {
			res.Knowledges = []vo.Knowledge{}
		} else {
			err = json.Unmarshal([]byte(*obj.Knowledges), &res.Knowledges)
			if err != nil {
				log.Error(err)
				return nil, err
			}
		}
		list = append(list, res)
	}
	return list, nil
}

func (s *agentService) ListForce(ctx context.Context, teamIdInt int64) ([]vo.AgentRes, error) {
	ctx, log := tools.GenContext(ctx)
	agentList, err := db.AgentManager.ListForce(ctx, teamIdInt)
	if err != nil {
		log.Error(err)
		return nil, err
	}

	list := []vo.AgentRes{}
	for _, obj := range agentList {
		res := vo.AgentRes{}
		err = copier.Copy(&res, &obj)
		if err != nil {
			log.Error(err)
			return nil, err
		}
		if obj.Knowledges == nil {
			res.Knowledges = []vo.Knowledge{}
		} else {
			err = json.Unmarshal([]byte(*obj.Knowledges), &res.Knowledges)
			if err != nil {
				log.Error(err)
				return nil, err
			}
		}
		list = append(list, res)
	}
	return list, nil
}

func (s *agentService) ownerCheck(ctx context.Context, agentId int64) error {
	if agentId <= 0 {
		return nil
	}

	agent, err := s.Get(ctx, agentId)
	if err != nil {
		return err
	}
	count := db.AppCollaborationManager.GetAppCollaboration(ctx, ptr.ToInt64(agent.AppId), tools.GetUserId(ctx))
	if count > 0 {
		return nil
	}

	otherIds, err := db.AgentManager.PickupOtherTasks(ctx, tools.GetUserId(ctx), []int64{agentId})
	if err != nil {
		return err
	}
	if len(otherIds) > 0 {
		return fmt.Errorf("禁止修改别人的ids: %s", common.PrettifyJson(otherIds, false))
	}
	return nil
}

func (s *agentService) Update(ctx context.Context, updateReq *vo.UpdateAgentReq) error {
	ctx, log := tools.GenContext(ctx)
	err := s.ownerCheck(ctx, updateReq.Id)
	if err != nil {
		return err
	}

	agentModel := &model.AgentModel{}
	err = copier.Copy(agentModel, updateReq)
	if err != nil {
		return err
	}

	knowledgesJson, err := json.Marshal(updateReq.Knowledges)
	if err != nil {
		log.Error(err)
		return err
	}
	agentModel.Knowledges = stringPtr(string(knowledgesJson))

	err = db.AgentManager.Update(ctx, agentModel)
	if err != nil {
		return err
	}
	return nil
}

// 只删agent，不删task link
func (s *agentService) Delete(ctx context.Context, deleteReq *vo.DeleteAgentReq) error {
	if len(deleteReq.Ids) == 0 {
		return nil
	}
	for _, agentId := range deleteReq.Ids {
		err := s.ownerCheck(ctx, agentId)
		if err != nil {
			return err
		}
	}

	return db.AgentManager.Delete(ctx, deleteReq.Ids)
}

func (s *agentService) Copy(ctx context.Context, copyReq *vo.CopyAgentReq) (int64, error) {
	ctx, log := tools.GenContext(ctx)
	agentRes, err := s.Get(ctx, copyReq.Id)
	if err != nil {
		log.Error(err)
		return 0, err
	}

	if tools.GetUserId(ctx) != agentRes.CreateUserId &&
		!copyReq.ForceCopy && (agentRes.Copyable == nil || !*agentRes.Copyable) {
		return 0, fmt.Errorf("agent '%d' copyable is false", copyReq.Id)
	}

	createReq := &vo.CreateAgentReq{}
	err = copier.Copy(&createReq, &agentRes)
	if err != nil {
		log.Error(err)
		return 0, err
	}

	// 新复制的team 默认不开启复制（不允许复制）
	createReq.Copyable = false

	createReq.AppId = copyReq.AppId
	createReq.AgentTeamId = copyReq.AgentTeamId
	if copyReq.Name != "" {
		createReq.Name = copyReq.Name
	}

	createReq.Knowledges = []vo.Knowledge{}
	if agentRes.Knowledges != nil {
		err = json.Unmarshal([]byte(*agentRes.Knowledges), &createReq.Knowledges)
		if err != nil {
			log.Error(err)
			return 0, err
		}
	}

	id, err := s.Create(ctx, createReq)
	if err != nil {
		log.Error(err)
		return 0, err
	}
	return id, nil
}
