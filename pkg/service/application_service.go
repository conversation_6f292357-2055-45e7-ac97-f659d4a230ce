package service

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"jarvis_api/pkg/db"
	"jarvis_api/pkg/model"
	"jarvis_api/pkg/vo"
	"jarvis_api/tools"
	"strconv"

	"github.com/jinzhu/copier"
	"gitlab.mobvista.com/spotmax/cncf_util/common"
)

var ApplicationService = &applicationService{}

type applicationService struct {
}

func (s *applicationService) CreateApplication(ctx context.Context, createApplicationReq *vo.CreateApplicationReq) (int64, error) {
	applicationModel := &model.ApplicationModel{}
	err := copier.Copy(applicationModel, createApplicationReq)
	if err != nil {
		return 0, err
	}
	id := tools.GenId()
	applicationModel.Id = id
	applicationModel.CreateUserId = tools.GetUserId(ctx)

	applicationModel.Sort = str2Json(createApplicationReq.Sort)

	err = db.ApplicationManager.CreateApplication(ctx, applicationModel)
	if err != nil {
		return 0, err
	}
	return id, nil
}

func (s *applicationService) GetApplication(ctx context.Context, appId int64) (*model.ApplicationModel, error) {
	application, err := db.ApplicationManager.GetApplication(ctx, appId)
	if err != nil {
		return nil, err
	}
	return application, nil
}

func (s *applicationService) GetMacroList(ctx context.Context, appId int64) ([]vo.TaskMacro, error) {
	teamList, err := TeamService.ListForce(ctx, appId)
	if err != nil {
		return nil, err
	}

	ignoreMap := make(map[string]bool)
	for _, team := range teamList { // team之间互相关联的ResultParamsKey对应的macro不需要返回给用户编辑
		if team.ResultParamsKey != nil {
			ignoreMap[*team.ResultParamsKey] = true
		}
	}

	connectorModels, err := ConnectorService.ListConnector(ctx, appId)
	if err != nil {
		return nil, err
	}
	for _, connector := range connectorModels {
		ignoreMap[connector.ResultParamsKey] = true
	}

	pythonNodeModels, err := PythonNodeService.ListPythonNode(ctx, appId)
	if err != nil {
		return nil, err
	}
	for _, pythonNode := range pythonNodeModels {
		ignoreMap[pythonNode.ResultVar] = true
	}

	macroList := make([]vo.TaskMacro, 0, 10)
	for _, team := range teamList {
		list, err := TeamService.GetMacroList(ctx, team.Id)
		if err != nil {
			return nil, err
		}
		for _, m := range list {
			if ignoreMap[m.Name] {
				continue
			}
			ignoreMap[m.Name] = true
			macroList = append(macroList, m)
		}
	}

	for _, connector := range connectorModels {
		connectorMacroList := connector.MacroList
		for _, m := range connectorMacroList {
			if ignoreMap[m.Name] {
				continue
			}
			ignoreMap[m.Name] = true
			macroList = append(macroList, m)
		}
	}

	for _, pythonNode := range pythonNodeModels {
		pythonNodeMacroList := pythonNode.MacroList
		for _, m := range pythonNodeMacroList {
			if ignoreMap[m.Name] {
				continue
			}
			ignoreMap[m.Name] = true
			macroList = append(macroList, m)
		}
	}
	return macroList, nil
}

// macroList, err := service.TeamService.GetMacroList(ctx, teamIdInt)
// if err != nil {
// log.Error(err)
// ResponseWithError(c, http.StatusInternalServerError, err)
// return
// }
// list.MacroList = macroList
func (s *applicationService) CopyApplication(ctx context.Context, appId int64) (int64, error) {
	appRes, err := s.GetApplication(ctx, appId)
	if err != nil {
		return -1, err
	}

	// 是否允许copy
	if appRes.Copyable == nil || !*appRes.Copyable {
		return -1, errors.New("copyable is false")
	}

	createReq := &vo.CreateApplicationReq{}
	err = copier.Copy(&createReq, &appRes)
	if err != nil {
		return -1, err
	}

	id, err := s.CreateApplication(ctx, createReq)
	if err != nil {
		return -1, err
	}
	return id, nil
}

func (s *applicationService) ListApplication(ctx context.Context) ([]vo.ApplicationRes, error) {
	_, log := tools.GenContext(ctx)

	userId := tools.GetUserId(ctx)
	applicationList, err := db.ApplicationManager.GetApplicationList(ctx, userId)
	if err != nil {
		log.Error(err)
		return nil, err
	}

	applicationCollaborationList, err := db.ApplicationManager.GetMyCollaborationApp(ctx, userId)
	if err != nil {
		log.Error(err)
		return nil, err
	}

	userIds := []int64{}

	for _, app := range applicationList {
		userIds = append(userIds, app.CreateUserId)
	}
	for _, app := range applicationCollaborationList {
		userIds = append(userIds, app.CreateUserId)
	}
	userMaps, _, err := UserService.GetUserByIdList(ctx, userIds)
	if err != nil {
		log.Error(err)
		return nil, err
	}

	list := []vo.ApplicationRes{}

	for _, app := range applicationList {
		appRes := &vo.ApplicationRes{}
		err = copier.Copy(appRes, &app)
		if err != nil {
			log.Error(err)
			return nil, err
		}
		userModel := userMaps[app.CreateUserId]
		UserSimpleInfo := vo.UserSimpleInfo{}
		err = copier.Copy(&UserSimpleInfo, &userModel)
		if err != nil {
			log.Error(err)
			return nil, err
		}
		appRes.CreateUser = UserSimpleInfo
		appRes.IsCollaboration = false
		list = append(list, *appRes)
	}

	for _, app := range applicationCollaborationList {
		appRes := &vo.ApplicationRes{}
		err = copier.Copy(appRes, &app)
		if err != nil {
			log.Error(err)
			return nil, err
		}
		userModel := userMaps[app.CreateUserId]
		UserSimpleInfo := vo.UserSimpleInfo{}
		err = copier.Copy(&UserSimpleInfo, &userModel)
		if err != nil {
			log.Error(err)
			return nil, err
		}
		appRes.CreateUser = UserSimpleInfo
		appRes.IsCollaboration = true
		list = append(list, *appRes)
	}

	return list, nil
}

func (s *applicationService) ownerCheck(ctx context.Context, appId int64) error {
	if appId <= 0 {
		return nil
	}

	userId := tools.GetUserId(ctx)
	count := db.AppCollaborationManager.GetAppCollaboration(ctx, appId, userId)
	if count > 0 {
		return nil
	}

	otherIds, err := db.ApplicationManager.PickupOtherTasks(ctx, tools.GetUserId(ctx), []int64{appId})
	if err != nil {
		return err
	}
	if len(otherIds) > 0 {
		return fmt.Errorf("禁止修改别人的ids: %s", common.PrettifyJson(otherIds, false))
	}
	return nil
}

func (s *applicationService) UpdateApplication(ctx context.Context, updateApplicationReq *vo.UpdateApplicationReq) error {
	err := s.ownerCheck(ctx, updateApplicationReq.Id)
	if err != nil {
		return err
	}

	applicationModel := &model.ApplicationModel{}
	err = copier.Copy(applicationModel, updateApplicationReq)
	if err != nil {
		return err
	}

	err = db.ApplicationManager.UpdateApplication(ctx, applicationModel)
	if err != nil {
		return err
	}
	return nil
}

func (s *applicationService) DeleteApplication(ctx context.Context, deleteApplicationReq *vo.DeleteApplicationReq) error {
	err := s.ownerCheck(ctx, deleteApplicationReq.Id)
	if err != nil {
		return err
	}
	return db.ApplicationManager.DeleteApplication(ctx, deleteApplicationReq.Id)
}

func str2Json(src string) *string {
	isValid := json.Valid([]byte(src))
	if !isValid {
		tmp := "[]"
		return &tmp
	}
	return &src
}

func json2Str(obj interface{}) string {
	result, err := json.Marshal(obj)
	if err != nil {
		return "{}"
	}
	return string(result)
}

func stringPtr(str string) *string {
	return &str
}

func (a *applicationService) ResetCollaboration(ctx context.Context, req *vo.ResetCollaborationReq) error {
	_, log := tools.GenContext(ctx)
	err := db.AppCollaborationManager.CleanCollaboration(ctx, req.AppId)
	if err != nil {
		return err
	}
	for _, userId := range req.CollaborationIds {
		userIdInt, err := strconv.ParseInt(userId, 10, 64)
		if err != nil {
			log.Error(err)
			return err
		}
		err = db.AppCollaborationManager.ResetCollaboration(ctx, req.AppId, userIdInt)
		if err != nil {
			log.Error(err)
			continue
		}
	}
	return nil
}

func (a *applicationService) GetCollaboration(ctx context.Context, appId int64) ([]int64, error) {
	return db.AppCollaborationManager.GetCollaboration(ctx, appId)
}
