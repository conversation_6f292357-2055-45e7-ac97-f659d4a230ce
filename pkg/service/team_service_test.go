package service

import (
	"context"
	"fmt"
	"jarvis_api/pkg/db"
	"jarvis_api/pkg/vo"
	"jarvis_api/tools"
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestTeamCopy(t *testing.T) {
	// 示例字符串
	src := `["1","2","12345"]`
	// 替换规则字典
	dict := map[int64]int64{
		1: 11111,
		2: 22222,
	}

	sreCpy := src

	// 调用替换函数
	sreCpy, err := replaceSlice(src, dict)
	if err != nil {
		t.Fatal(err)
	}
	// 输出结果
	fmt.Println("Original:", src)
	fmt.Println("Modified:", sreCpy)

	sreCpy2 := replaceSubstrings(src, dict)
	// 输出结果
	fmt.Println("Original:", src)
	fmt.Println("Modified:", sreCpy2)
}

const j = "{\"final_result\": \"```json\\n{\"name\":\"Collaborative Tech Report Generation System\",\"agents\":[{\"role\":\"Project Manager\",\"goal\":\"Coordinate the team to generate high-quality, engaging, and effective 小红书运营文案 (Xiaohongshu/RED platform marketing copy) that meets user requirements and satisfaction.\",\"backstory\":\"As a Project Manager specializing in digital marketing campaigns, you ensure that every step of the copywriting process is executed efficiently. You deeply understand the user's needs, break down the task, delegate responsibilities, and confirm user satisfaction before finalizing the output.\",\"verbose\":true,\"allow_delegation\":true,\"max_iter\":15,\"llm\":\"gpt-4.1\"},{\"role\":\"Content Strategist\",\"goal\":\"Research current trends, user preferences, and successful case studies on 小红书 to provide strategic direction and key points for the copywriting process.\",\"backstory\":\"You are a digital marketing strategist with expertise in social media platforms, especially 小红书. You excel at identifying trending topics, analyzing competitor content, and extracting actionable insights to inform content creation.\",\"verbose\":true,\"allow_delegation\":false,\"tools\":[\"google_search\",\"serper_dev_tool\",\"asknews\",\"wikipedia\",\"scrape_website_tool\"],\"max_iter\":10,\"llm\":\"gpt-4.1\"},{\"role\":\"Copywriter\",\"goal\":\"Write engaging, platform-optimized, and brand-aligned 小红书运营文案 based on the research and strategy provided.\",\"backstory\":\"You are a seasoned copywriter with a flair for crafting compelling social media content. You understand the nuances of 小红书's audience and can adapt your writing style to maximize engagement and conversion.\",\"verbose\":true,\"allow_delegation\":false,\"tools\":[\"safe_code_interpreter\"],\"max_iter\":10,\"llm\":\"gpt-4.1\"},{\"role\":\"User Feedback Collector\",\"goal\":\"Collect user feedback on the generated copy, identify actionable improvement suggestions, and relay them to the team for revision until the user is satisfied.\",\"backstory\":\"You are skilled at interpreting user feedback and ensuring that the team understands and implements necessary changes. You keep track of user preferences for future optimization.\",\"verbose\":true,\"allow_delegation\":false,\"tools\":[\"human_feed_back\"],\"memory_space\":\"xiaohongshu_copywriting\",\"max_iter\":10,\"llm\":\"gpt-4.1\"}],\"tasks\":[{\"name\":\"Generate Xiaohongshu Marketing Copy\",\"description\":\"Produce a high-quality, engaging, and effective 小红书运营文案 (Xiaohongshu/RED platform marketing copy) tailored to the user's requirements. The process should include: 1) Researching current trends and best practices, 2) Drafting the initial copy, 3) Collecting user feedback, 4) Revising the copy based on feedback, and 5) Repeating steps 3-4 until the user confirms satisfaction. Only complete the task when the user gives final approval.\",\"expected_output\":\"A finalized, user-approved Xiaohongshu marketing copy in markdown format.\",\"agent\":\"Project Manager\",\"tools\":[\"google_search\",\"serper_dev_tool\",\"asknews\",\"wikipedia\",\"scrape_website_tool\",\"human_feed_back\",\"safe_code_interpreter\"],\"output_file\":\"xiaohongshu_copy.md\"}]}\\n```\"}"

func Test_teamService_AutoCreateTeam(t *testing.T) {
	assert.Nil(t, db.InitDBManager())
	assert.Nil(t, tools.InitSnowflake())

	ctx := context.Background()

	r := &vo.AutoCreateTeamReq{
		AppId:       1,
		Name:        "11",
		Requirement: "3234",
	}
	id, err := TeamService.AutoCreateTeam(ctx, r, j)
	if err != nil {
		t.Fatal(err)
	}
	t.Log(id)
}
