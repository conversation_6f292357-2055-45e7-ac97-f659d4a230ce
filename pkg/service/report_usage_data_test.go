package service

import (
	"context"
	"fmt"
	"jarvis_api/constant"
	"testing"
)

func Test_analyzeMarkdown(t *testing.T) {

	// 请替换为实际的 Markdown 内容

	language, totalLines := analyzeMarkdown(markdown)
	fmt.Printf("Language with most lines: %s\n", language)
	fmt.Printf("Total lines of code: %d\n", totalLines)
}

func Test_report_usage_data(t *testing.T) {
	ctx := context.Background()
	// ctx.Set(constant.MiddlewareKeyUserEmail, user.Email)
	ctx = context.WithValue(ctx, constant.MiddlewareKeyUserEmail, "<EMAIL>")
	report_usage_data(ctx, markdown)
}

const markdown = `
# Sample Markdown Document

Here's a Python code block:

` + "```python" + `
def hello_world():
    print("Hello, World!")
    return True

if __name__ == "__main__":
    hello_world()
` + "```" + `

And here's a Go code block:

` + "```go" + `
package main

import "fmt"

func main() {
    fmt.Println("Hello, World!")
}

func someOtherFunction() {
    // This is just to add more lines
    a := 1
    b := 2
    c := a + b
    fmt.Println(c)
}
` + "```" + `

Finally, here's a JavaScript code block:

` + "```javascript" + `
function helloWorld() {
    console.log("Hello, World!");
}

helloWorld();

// Adding more lines
let x = 10;
let y = 20;
console.log(x + y);
` + "```" + `

That's all for the code examples!
`
