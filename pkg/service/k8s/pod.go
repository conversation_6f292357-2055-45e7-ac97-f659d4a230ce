package k8s

import (
	"context"
	"fmt"

	"io/ioutil"
	"log"

	corev1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/watch"
	"k8s.io/client-go/kubernetes"
	"k8s.io/client-go/kubernetes/scheme"
)

const podYaml string = `apiVersion: v1
kind: Pod
metadata:
  name: maxagent-runner
  namespace: maxagent-test
  labels:
    app: maxagent-runner
spec:
  affinity:
    nodeAffinity:
      requiredDuringSchedulingIgnoredDuringExecution:
        nodeSelectorTerms:
        - matchExpressions:
          - key: Group
            operator: In
            values:
            - maxcloud
  serviceAccountName: maxagent-sa
  containers:
    - name: maxagent-runner
      image: registry.cn-hongkong.aliyuncs.com/spotmax/maxagent-runner:v0.1.17
      imagePullPolicy: IfNotPresent
      env:
        - name: RUNNER_RECORD_ID
          value: "1"
        - name: GOOGLE_APPLICATION_CREDENTIALS
          value: /maxagent/config/google/google-credentials.json
        - name: DOT_PATH
          value: /maxagent/config/env/.env
      ports:
        - containerPort: 8765
          name: httpwithmetrics
          protocol: TCP
      
      securityContext:
        privileged: true
      
      volumeMounts:
      - mountPath: /maxagent/config/google/
        name: google-credentials
        readOnly: true
      - mountPath: /maxagent/config/env/
        name: env
        readOnly: true
  imagePullSecrets:
  - name: ali-ecr
  restartPolicy: Never
  volumes:
  - name: google-credentials
    secret:
      items:
      - key: google-credentials
        path: google-credentials.json
      secretName: maxagent-runner-secret
  - name: env
    secret:
      items:
      - key: .env
        path: .env
      secretName: maxagent-runner-secret
  
  `

// readYamlFile 读取 YAML 文件
func readYamlFile(filename string) (*corev1.Pod, error) {
	yamlFile, err := ioutil.ReadFile(filename)
	if err != nil {
		return nil, fmt.Errorf("failed to read YAML file: %w", err)
	}
	return YamlToPodUniversal(yamlFile)
}

// YamlToPodUniversal 读取 YAML 字符串
func YamlToPodUniversal(yamlContent []byte) (*corev1.Pod, error) {
	decode := scheme.Codecs.UniversalDeserializer().Decode
	obj, _, err := decode(yamlContent, nil, nil)
	if err != nil {
		return nil, err
	}

	pod, ok := obj.(*corev1.Pod)
	if !ok {
		return nil, fmt.Errorf("object is not a Pod")
	}

	return pod, nil
}

// createPod 创建 Pod
func createPod(clientset *kubernetes.Clientset, pod *corev1.Pod) (*corev1.Pod, error) {
	runningPod, err := clientset.CoreV1().Pods(pod.Namespace).Create(context.TODO(), pod, metav1.CreateOptions{})
	if err != nil {
		return nil, fmt.Errorf("failed to create Pod: %w", err)
	}
	return runningPod, nil
}

// 获取 Pod 并检查状态
func checkPodStatus(clientset *kubernetes.Clientset, namespace, podName string) (corev1.PodPhase, error) {
	pod, err := clientset.CoreV1().Pods(namespace).Get(context.TODO(), podName, metav1.GetOptions{})
	if err != nil {
		return corev1.PodUnknown, fmt.Errorf("failed to get pod: %v", err)
	}
	return pod.Status.Phase, nil
}

// watchPodStatus 监视 Pod 状态
func watchPodStatus(clientset *kubernetes.Clientset, pod *corev1.Pod) {
	watcher, err := clientset.CoreV1().Pods(pod.Namespace).Watch(context.TODO(), metav1.ListOptions{
		FieldSelector: fmt.Sprintf("metadata.name=%s", pod.Name),
	})
	if err != nil {
		log.Fatalf("Failed to watch Pod: %v", err)
	}
	defer watcher.Stop()

	ch := watcher.ResultChan()
	for event := range ch {
		pod, ok := event.Object.(*corev1.Pod)
		if !ok {
			log.Fatalf("Unexpected type: %T", event.Object)
		}

		switch event.Type {
		case watch.Added:
			fmt.Printf("Pod %s added\n", pod.Name)
		case watch.Modified:
			fmt.Printf("Pod %s status: %s\n", pod.Name, pod.Status.Phase)
			if pod.Status.Phase == corev1.PodSucceeded || pod.Status.Phase == corev1.PodFailed {
				fmt.Printf("Pod %s finished with status: %s\n", pod.Name, pod.Status.Phase)
				return
			}
		case watch.Deleted:
			fmt.Printf("Pod %s deleted\n", pod.Name)
			return
		case watch.Error:
			fmt.Printf("Error watching Pod %s: %v\n", pod.Name, event.Object)
			return
		}
	}
}
