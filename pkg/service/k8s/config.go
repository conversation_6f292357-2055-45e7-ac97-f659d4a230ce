package k8s

import (
	"fmt"
	"k8s.io/client-go/rest"
	"k8s.io/client-go/tools/clientcmd"
	"os"
	"path/filepath"
)

// loadConfig 加载 Kubernetes 配置
func loadConfig() (*rest.Config, error) {
	// 如果在集群内部运行，使用 InClusterConfig
	config, err := rest.InClusterConfig()
	if err == nil {
		return config, nil
	}

	// 否则，尝试从 kubeconfig 文件加载
	kubeconfig := filepath.Join(os.Getenv("HOME"), ".kube", "config")
	config, err = clientcmd.BuildConfigFromFlags("", kubeconfig)
	if err != nil {
		return nil, fmt.Errorf("failed to load kubeconfig: %w", err)
	}
	return config, nil
}
