package k8s

import (
	"errors"
	"fmt"

	"github.com/gin-gonic/gin"
	corev1 "k8s.io/api/core/v1"
	networkingv1 "k8s.io/api/networking/v1"
	"k8s.io/client-go/kubernetes"
)

var K8sService *k8sToolService

type k8sToolService struct {
	clientSet *kubernetes.Clientset
}

func NewK8sToolService() error {
	// 加载 Kubernetes 配置
	config, err := loadConfig()
	if err != nil {
		return fmt.Errorf("failed to load Kubernetes config: %v", err)
	}

	// 创建 Kubernetes 客户端
	clientset, err := kubernetes.NewForConfig(config)
	if err != nil {
		return fmt.Errorf("failed to create Kubernetes client: %v", err)
	}

	_, err = YamlToPodUniversal([]byte(podYaml))
	if err != nil {
		return fmt.Errorf("failed to build Pod: %v", err)
	}

	K8sService = &k8sToolService{
		clientSet: clientset,
	}
	return nil
}

func (k *k8sToolService) CreatePod(runnerId int64) (*corev1.Pod, error) {
	pod, err := preparePod(runnerId)
	if err != nil {
		return nil, err
	}

	//str, _ := json.MarshalIndent(pod, "", "	")
	//fmt.Printf("%s\n", str)

	return createPod(k.clientSet, pod)
}

func (k *k8sToolService) GetPodStatus(podName string) (corev1.PodPhase, error) {
	namespace := "maxagent-test"
	if gin.Mode() == gin.ReleaseMode {
		namespace = "maxagent-prod"
	}
	return checkPodStatus(k.clientSet, namespace, podName)
}

func (k *k8sToolService) CreateSvc(podName string) (*corev1.Service, error) {
	svc, err := prepareSvc(podName, podName)
	if err != nil {
		return nil, err
	}

	//str, _ := json.MarshalIndent(svc, "", "	")
	//fmt.Printf("svc: %s\n", str)

	return createSvc(k.clientSet, svc)
}

// func (k *k8sToolService) CreateIngress(name, svcName string) (*networkingv1.Ingress, error) {
// 	ingress, err := prepareIngress(name, svcName)
// 	if err != nil {
// 		return nil, err
// 	}

// 	//str, _ := json.MarshalIndent(svc, "", "	")
// 	//fmt.Printf("svc: %s\n", str)

// 	return createIngress(k.clientSet, ingress)
// }

func preparePod(runnerId int64) (*corev1.Pod, error) {
	pod, err := YamlToPodUniversal([]byte(podYaml))
	if err != nil {
		return nil, err
	}

	pod.Name = fmt.Sprintf("maxagent-runner-%d", runnerId)
	pod.Labels["app"] = pod.Name
	if gin.Mode() == gin.ReleaseMode {
		pod.Namespace = "maxagent-prod"
	}

	for j := 0; j < len(pod.Spec.Containers[0].Env); j++ {
		if pod.Spec.Containers[0].Env[j].Name == "RUNNER_RECORD_ID" {
			pod.Spec.Containers[0].Env[j].Value = fmt.Sprintf("%d", runnerId)
			return pod, nil
		}
	}
	return nil, errors.New("failed to find the env 'RUNNER_RECORD_ID'")
}

func prepareSvc(name, podName string) (*corev1.Service, error) {
	svc, err := YamlToSvcUniversal([]byte(svcTpl))
	if err != nil {
		return nil, err
	}
	svc.Name = name
	svc.Spec.Selector["app"] = podName
	if gin.Mode() == gin.ReleaseMode {
		svc.Namespace = "maxagent-prod"
	}
	return svc, nil
}

func prepareIngress(name, svcName string) (*networkingv1.Ingress, error) {
	ingress, err := YamlToIngressUniversal([]byte(ingressTpl))
	if err != nil {
		return nil, err
	}
	ingress.Name = name
	for i := 0; i < len(ingress.Spec.Rules[0].HTTP.Paths); i++ {
		ingress.Spec.Rules[0].HTTP.Paths[i].Backend.Service.Name = svcName
		ingress.Spec.Rules[0].HTTP.Paths[i].Path = fmt.Sprintf("/%s", svcName)
	}
	return ingress, nil
}
