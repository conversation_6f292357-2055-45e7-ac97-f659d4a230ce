package k8s

import (
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
)

func TestX(t *testing.T) {
	err := NewK8sToolService()
	assert.Nil(t, err)

	// 监视 Pod 状态
	pod, err := K8sService.CreatePod(1905518366649225216)
	assert.Nil(t, err)
	t.Logf("pod: %v", pod)

	time.Sleep(time.Second * 3)

	name := pod.Name
	//name := "maxagent-runner-1905518366649225216"

	svc, err := K8sService.CreateSvc(name)
	assert.Nil(t, err)
	t.Logf("svc: %v", svc)

	//ingress, err := K8sService.CreateIngress(name, name)
	//assert.Nil(t, err)
	//t.Logf("ingress: %v", ingress)
}
