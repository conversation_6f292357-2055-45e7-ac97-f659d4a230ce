package k8s

import (
	"context"
	"fmt"
	corev1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/client-go/kubernetes"
	"k8s.io/client-go/kubernetes/scheme"
)

const svcTpl string = `apiVersion: v1
kind: Service
metadata:
  name: maxagent-runner-xxx
  namespace: maxagent-test
spec:
  ports:
    - port: 80
      targetPort: 8765
  selector:
    app: maxagent-runner-xxx`

// YamlToSvcUniversal 读取 YAML 字符串
func YamlToSvcUniversal(yamlContent []byte) (*corev1.Service, error) {
	decode := scheme.Codecs.UniversalDeserializer().Decode
	obj, _, err := decode(yamlContent, nil, nil)
	if err != nil {
		return nil, err
	}

	svc, ok := obj.(*corev1.Service)
	if !ok {
		return nil, fmt.Errorf("object is not a Pod")
	}

	return svc, nil
}

// createSvc 创建 svc
func createSvc(clientset *kubernetes.Clientset, svc *corev1.Service) (*corev1.Service, error) {
	runningSvc, err := clientset.CoreV1().Services(svc.Namespace).Create(context.TODO(), svc, metav1.CreateOptions{})
	if err != nil {
		return nil, err
	}
	return runningSvc, nil
}
