package k8s

import (
	"context"
	"fmt"
	networkingv1 "k8s.io/api/networking/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/client-go/kubernetes"
	"k8s.io/client-go/kubernetes/scheme"
)

const ingressTpl string = `apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: maxagent-runner-xxx
  namespace: maxagent-test
  annotations:
    nginx.ingress.kubernetes.io/force-ssl-redirect: "true"
    nginx.ingress.kubernetes.io/proxy-send-timeout: "3600"
    nginx.ingress.kubernetes.io/proxy-read-timeout: "3600"
spec:
  ingressClassName: nginx
  tls:
    - hosts:
        - maxagent-test-api.spotmaxtech.com
      secretName: spotmaxtech-secret
  rules:
  - host: maxagent-test-api.spotmaxtech.com
    http:
      paths:
      - backend:
          service:
            name: maxagent-runner-xxx
            port:
              number: 80
        path: /maxagent-runner-xxx
        pathType: Prefix`

// YamlToIngressUniversal 读取 YAML 字符串
func YamlToIngressUniversal(yamlContent []byte) (*networkingv1.Ingress, error) {
	decode := scheme.Codecs.UniversalDeserializer().Decode
	obj, _, err := decode(yamlContent, nil, nil)
	if err != nil {
		return nil, err
	}

	ingress, ok := obj.(*networkingv1.Ingress)
	if !ok {
		return nil, fmt.Errorf("object is not a ingress")
	}

	return ingress, nil
}

// createIngress 创建 ingress
func createIngress(clientset *kubernetes.Clientset, ingress *networkingv1.Ingress) (*networkingv1.Ingress, error) {
	runningIngress, err := clientset.NetworkingV1().Ingresses(ingress.Namespace).Create(context.TODO(), ingress, metav1.CreateOptions{})
	if err != nil {
		return nil, err
	}
	return runningIngress, nil
}
