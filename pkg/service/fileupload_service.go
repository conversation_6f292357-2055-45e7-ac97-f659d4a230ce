package service

import (
	"context"
	"fmt"
	"jarvis_api/tools"
	"mime/multipart"
	"path/filepath"
	"strings"
)

var FileUploadService = &fileUploadService{}

type fileUploadService struct {
}

func (s *fileUploadService) UploadFile(ctx context.Context, file *multipart.FileHeader) (string, error) {
	ctx, log := tools.GenContext(ctx)
	originalFilename := file.Filename

	formFile, err := file.Open()
	if err != nil {
		log.Error(err)
		return "", err
	}

	id := tools.GenId()
	// 获取文件后缀
	fileExt := filepath.Ext(originalFilename)

	// 生成文件名前缀的 MD5
	fileNameWithoutExt := strings.TrimSuffix(originalFilename, fileExt)
	md5Prefix := tools.Md5(fmt.Sprintf("%v-%v", id, fileNameWithoutExt))

	// 生成新的文件名
	newFileName := md5Prefix + fileExt
	s3_key := fmt.Sprintf("%s/%s", tools.DataDir(), newFileName)
	err = S3Service.UploadToS3ByIo(ctx, s3_key, formFile)
	if err != nil {
		log.Error(err)
		return "", err
	}
	return fmt.Sprintf("%s/%s", MAXAGENT_S3_BUCKET, s3_key), nil
}
