package service

import (
	"context"
	"strings"

	"github.com/gin-gonic/gin"
	"github.com/sirupsen/logrus"
	"gitlab.mobvista.com/spotmax/cncf_util/common"
	pod_util "gitlab.mobvista.com/spotmax/cncf_util/k8s/pod"
	svc_util "gitlab.mobvista.com/spotmax/cncf_util/k8s/service"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
)

var CleanPodService = &cleanPodService{}

type cleanPodService struct {
}

func (s *cleanPodService) CleanPod(ctx context.Context) error {
	nameSpace := "maxagent-test"
	if gin.Mode() == gin.ReleaseMode {
		nameSpace = "maxagent-prod"
	}
	logrus.Info("start to clean pods")
	// client, err := common.CreateClientsetFromLocal()
	client, err := common.CreateClientsetFromPod()
	if err != nil {
		return err
	}

	podList, _, err := pod_util.ListPod(ctx, client, nameSpace, metav1.ListOptions{})
	if err != nil {
		return err
	}
	logrus.WithField("podCount", len(podList)).Info("start to clean pods")

	for _, pod := range podList {
		pod := pod_util.ConvertPod(ctx, pod)
		if !strings.HasPrefix(pod.Name, "maxagent-runner") {
			logrus.WithField("pod", pod.Name).Info("pod is not maxagent-runner, skip")
			continue
		}
		if pod.Status != "Succeeded" {
			logrus.WithField("pod", pod.Name).Info("pod is not completed, skip")
			continue
		}
		err := pod_util.DeletePod(ctx, client, nameSpace, pod.Name, metav1.DeleteOptions{})
		if err != nil {
			logrus.WithField("pod", pod.Name).Error(err)
		}

		err = svc_util.DeleteService(ctx, client, nameSpace, pod.Name, metav1.DeleteOptions{})
		if err != nil {
			logrus.WithField("svc", pod.Name).Error(err)
		}
	}

	return nil
}
