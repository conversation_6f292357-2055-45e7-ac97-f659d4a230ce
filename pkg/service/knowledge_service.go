package service

import (
	"context"
	"errors"
	"jarvis_api/constant"
	"jarvis_api/pkg/db"
	"jarvis_api/pkg/model"
	"jarvis_api/pkg/vo"
	"jarvis_api/tools"
	"mime/multipart"

	"github.com/jinzhu/copier"
)

var KnowledgeService = &knowledgeService{}

type knowledgeService struct {
}

type KnowledgeBean struct {
	File             *multipart.FileHeader
	OriginalFilename string
	FileType         string
	Remark           string
	Content          string
	Name             string
	IsDir            bool
	ParentId         int64
}

func (s *knowledgeService) DeleteKnowledge(ctx context.Context, id int64) error {
	_, log := tools.GenContext(ctx)

	knowledgeModel, err := db.KnowledgeManager.GetKnowledge(ctx, id)
	if err != nil {
		log.Error(err)
		return err
	}

	if knowledgeModel.IsDir {
		knowledgeModels, err := s.ListKnowledge(ctx, id, knowledgeModel.IsData)
		if err != nil {
			log.Error(err)
			return err
		}
		if len(knowledgeModels) == 0 {
			err = db.KnowledgeManager.DeleteKnowledge(ctx, id)
			if err != nil {
				log.Error(err)
				return err
			}
			return nil
		}
		return errors.New("directory is not empty")
	}

	err = db.KnowledgeManager.DeleteKnowledge(ctx, id)
	if err != nil {
		log.Error(err)
		return err
	}

	if knowledgeModel.FileType != string(constant.KnowledgeTypeText) {
		err = S3Service.DeleteFile(ctx, knowledgeModel.S3Key)
		if err != nil {
			log.Error(err)
			return err
		}
	}
	return nil
}

func (s *knowledgeService) ListKnowledge(ctx context.Context, dirId int64, isData bool) ([]vo.KnowledgeRes, error) {
	_, log := tools.GenContext(ctx)

	userId := tools.GetUserId(ctx)
	knowledgeModels, err := db.KnowledgeManager.ListKnowledge(ctx, userId, dirId, isData)
	if err != nil {
		log.Error(err)
		return nil, err
	}

	knowledgeList := []vo.KnowledgeRes{}
	err = copier.Copy(&knowledgeList, &knowledgeModels)
	if err != nil {
		log.Error(err)
		return nil, err
	}
	return knowledgeList, nil
}

func (s *knowledgeService) CreateKnowledge(ctx context.Context, createKnowledgeReq *vo.CreateKnowledgeReq) (int64, error) {
	ctx, log := tools.GenContext(ctx)

	knowledgeModel := &model.KnowledgeModel{
		Id:           tools.GenId(),
		IsDir:        createKnowledgeReq.IsDir,
		ParentId:     createKnowledgeReq.ParentId,
		Name:         createKnowledgeReq.Name,
		FileName:     createKnowledgeReq.FileName,
		Content:      createKnowledgeReq.Content,
		FileType:     string(createKnowledgeReq.FileType),
		S3Key:        createKnowledgeReq.S3Key,
		Remark:       createKnowledgeReq.Remark,
		CreateUserId: tools.GetUserId(ctx),
		IsData:       createKnowledgeReq.IsData,
	}
	err := db.KnowledgeManager.CreateKnowledge(ctx, knowledgeModel)
	if err != nil {
		log.Error(err)
		return 0, err
	}
	return knowledgeModel.Id, nil
}

func (s *knowledgeService) GetKnowledge(ctx context.Context, id int64) (*vo.KnowledgeRes, error) {
	knowledgeModel, err := db.KnowledgeManager.GetKnowledge(ctx, id)
	if err != nil {
		return nil, err
	}

	knowledgeRes := &vo.KnowledgeRes{}
	err = copier.Copy(knowledgeRes, knowledgeModel)
	if err != nil {
		return nil, err
	}
	return knowledgeRes, nil
}

func (s *knowledgeService) UpdateKnowledge(ctx context.Context, id int64, updateKnowledgeReq *vo.UpdateKnowledgeReq) error {
	_, log := tools.GenContext(ctx)

	knowledgeModel, err := db.KnowledgeManager.GetKnowledge(ctx, id)
	if err != nil {
		log.Error(err)
		return err
	}

	err = copier.Copy(knowledgeModel, updateKnowledgeReq)
	if err != nil {
		log.Error(err)
		return err
	}

	err = db.KnowledgeManager.UpdateKnowledge(ctx, knowledgeModel)
	if err != nil {
		log.Error(err)
		return err
	}
	return nil
}
