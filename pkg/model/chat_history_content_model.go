package model

type ChatHistoryContentModel struct {
	Id            int64  `json:"id"`
	ChatHistoryId int64  `json:"chat_history_id"`                          // 聊天记录ID
	AppId         int64  `json:"app_id"`                                   // 应用ID
	Role          string `json:"role"`                                     // 角色 user、ai、tool、function
	Content       string `json:"content"`                                  // 聊天内容
	ImageUrl      string `json:"image_url"`                                // 图片地址
	UpdatedTime   int64  `json:"updated_time" gorm:"autoUpdateTime:milli"` // 修改时间
	CreatedTime   int64  `json:"created_time" gorm:"autoCreateTime:milli"` // 创建时间
	DeleteTime    int64  `json:"delete_time"`                              // 删除时间
}

func (m ChatHistoryContentModel) TableName() string {
	return "chat_history_content"
}
