package model

type ToolModel struct {
	Id           int64   `json:"id"`
	Name         *string `json:"name"`
	ToolKey      *string `json:"tool_key"`
	CreateUserId *int64  `json:"create_user_id"` // 创建者ID
	Remark       *string `json:"remark"`
	IsPublic     *bool   `json:"is_public"`
	ToolType     *string `json:"tool_type"`
	Url          *string `json:"url"`
	Transport    *string `json:"transport"`
	UpdatedTime  int64   `json:"updated_time" gorm:"autoUpdateTime:milli"` // 修改时间
	CreatedTime  int64   `json:"created_time" gorm:"autoCreateTime:milli"` // 创建时间
	DeleteTime   int64   `json:"delete_time"`                              // 删除时间
}

func (m ToolModel) TableName() string {
	return "tools"
}
