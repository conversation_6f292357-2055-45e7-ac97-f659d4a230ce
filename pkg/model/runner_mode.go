package model

type RunnerModel struct {
	Id int64 `json:"id"`

	Remark *string `json:"remark"`

	RunnerType    *string `json:"runner_type"`
	AppId         *int64  `json:"app_id"`
	AgentTeamId   *int64  `json:"agent_team_id"`
	DataProcessId *int64  `json:"data_process_id"`
	ConnectorId   *int64  `json:"connector_id"`
	PythonNodeId  *int64  `json:"python_node_id"`

	TriggerParams *string `json:"trigger_params"` // json

	// backend
	TriggerTime   int64  `json:"trigger_time"`
	TriggerMode   string `json:"trigger_mode"`
	ExecuteStatus string `json:"execute_status"` // 当状态为init时，pod才会执行
	ExecuteResult string `json:"execute_result"`
	ExecuteLog    string `json:"execute_log"`

	CreateUserId int64  `json:"create_user_id"`
	IsMultiTurn  bool   `json:"is_multi_turn"` // 创建者ID
	PrimaryId    int64  `json:"primary_id,string"`
	AllowTeamId  int64  `json:"allow_team_id,string"`
	AllowTasks   string `json:"allow_tasks"`
	UpdatedTime  int64  `json:"updated_time" gorm:"autoUpdateTime:milli"` // 修改时间
	CreatedTime  int64  `json:"created_time" gorm:"autoCreateTime:milli"` // 创建时间
	DeleteTime   int64  `json:"delete_time"`                              // 删除时间
}

func (m RunnerModel) TableName() string {
	return "runner_record"
}
