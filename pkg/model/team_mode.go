package model

type TeamModel struct {
	Id       int64   `json:"id"`
	Name     *string `json:"name"`
	Remark   *string `json:"remark"`
	Copyable *bool   `json:"copyable"` // 是否允许copy

	AppId *int64 `json:"app_id"` // APPID

	TaskSort        *string `json:"task_sort"`         // 排序关系
	ResultParamsKey *string `json:"result_params_key"` // team 执行结束后的结果保存变量，用于作为下一个team的参数名

	CreateUserId  *int64  `json:"create_user_id"` // 创建者ID
	IsPlanning    *bool   `json:"is_planning"`    // 是否开启 Planning
	PlanningLlmId *int64  `json:"planning_llm_id"`
	PythonStruct  *string `json:"python_struct"`                            // 使用python定义结构task最终返回内容的格式
	ExtInfo       string  `json:"ext_info"`                                 // 扩展信息 使用json存储
	UpdatedTime   int64   `json:"updated_time" gorm:"autoUpdateTime:milli"` // 修改时间
	CreatedTime   int64   `json:"created_time" gorm:"autoCreateTime:milli"` // 创建时间
	DeleteTime    int64   `json:"delete_time"`                              // 删除时间
}

func (m TeamModel) TableName() string {
	return "agent_team"
}
