package model

type ChatHistoryModel struct {
	Id          int64  `json:"id"`
	Name        string `json:"name"`                                     // 会话名称
	UserId      int64  `json:"user_id"`                                  // 用户ID
	AppId       int64  `json:"app_id"`                                   // 应用ID
	LlmId       int64  `json:"llm_id"`                                   // LLM模型ID(只用于多模型测试场景会话)
	BaseApp     bool   `json:"base_app"`                                 // 是否是基础模型应用
	UpdatedTime int64  `json:"updated_time" gorm:"autoUpdateTime:milli"` // 修改时间
	CreatedTime int64  `json:"created_time" gorm:"autoCreateTime:milli"` // 创建时间
	DeleteTime  int64  `json:"delete_time"`                              // 删除时间
}

func (m ChatHistoryModel) TableName() string {
	return "chat_history"
}
