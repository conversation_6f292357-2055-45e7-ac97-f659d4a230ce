package model

type TaskModel struct {
	Id       int64   `json:"id"`
	Name     *string `json:"name"`
	Remark   *string `json:"remark"`
	Copyable *bool   `json:"copyable"`

	AppId       *int64 `json:"app_id"`
	AgentTeamId *int64 `json:"agent_team_id"`
	AgentId     *int64 `json:"agent_id"`

	Description    *string `json:"description"`
	ExpectedOutput *string `json:"expected_output"`                          // 期望输出结果(crewai)
	OutputFile     *string `json:"output_file"`                              // 结果存储到文件，指定文件路径(crewai)
	ExtInfo        *string `json:"ext_info"`                                 // 扩展信息 使用json存储
	MacroList      *string `json:"macro_list"`                               // description内容中引用的宏列表
	AsyncExecution *bool   `json:"async_execution"`                          // 是否异步执行
	CreateUserId   int64   `json:"create_user_id"`                           // 创建者ID
	DependentTasks *string `json:"dependent_tasks"`                          // 依赖的task列表
	MemorySpace    *string `json:"memory_space"`                             // 记忆空间
	MemoryCategory *string `json:"memory_category"`                          // 记忆类型
	ToolIds        *string `json:"tool_ids"`                                 // json
	OutputStruct   *string `json:"output_struct"`                            // task 结果的结构体名称
	UpdatedTime    int64   `json:"updated_time" gorm:"autoUpdateTime:milli"` // 修改时间
	CreatedTime    int64   `json:"created_time" gorm:"autoCreateTime:milli"` // 创建时间
	DeleteTime     int64   `json:"delete_time"`                              // 删除时间
}

func (m TaskModel) TableName() string {
	return "tasks"
}
