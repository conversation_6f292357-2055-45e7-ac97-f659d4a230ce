package model

type LlmProvideAuthModel struct {
	Id              int64  `json:"id" `
	ServiceProvider string `json:"service_provider" `                        // 服务提供云商aws、azure
	Authorization   string `json:"authorization"`                            // 认证方式，使用json加密存储
	Description     string `json:"description" `                             // 描述信息
	UpdatedTime     int64  `json:"updated_time" gorm:"autoUpdateTime:milli"` // 修改时间
	CreatedTime     int64  `json:"created_time" gorm:"autoCreateTime:milli"` // 创建时间
	DeleteTime      int64  `json:"delete_time"`                              // 删除时间
}

func (m LlmProvideAuthModel) TableName() string {
	return "llm_provide_auth"
}
