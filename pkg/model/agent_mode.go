package model

type AgentModel struct {
	Id       int64   `json:"id"`
	Name     *string `json:"name"`
	Remark   *string `json:"remark"`
	Copyable *bool   `json:"copyable"` // 是否允许copy

	AppId       *int64 `json:"app_id"`
	AgentTeamId *int64 `json:"agent_team_id"`

	Role            *string `json:"role"`      // 角色（crewai）
	Goal            *string `json:"goal"`      // 目标（crewai）
	Backstory       *string `json:"backstory"` // 背景信息（crewai）
	Verbose         *bool   `json:"verbose"`
	AllowDelegation *bool   `json:"allow_delegation"`
	Multimodal      *bool   `json:"multimodal"`
	Memory          *bool   `json:"memory"`
	MaxIter         *int64  `json:"max_iter"`
	ToolIds         *string `json:"tool_ids"` // json
	LlmId           *int64  `json:"llm_id"`
	ExtInfo         *string `json:"ext_info"`   // json
	Knowledges      *string `json:"knowledges"` // json

	CreateUserId int64   `json:"create_user_id"`                           // 创建者ID
	MemorySpace  *string `json:"memory_space"`                             // 记忆空间
	UpdatedTime  int64   `json:"updated_time" gorm:"autoUpdateTime:milli"` // 修改时间
	CreatedTime  int64   `json:"created_time" gorm:"autoCreateTime:milli"` // 创建时间
	DeleteTime   int64   `json:"delete_time"`                              // 删除时间
}

func (m AgentModel) TableName() string {
	return "agents"
}
