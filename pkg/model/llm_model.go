package model

type LlmModel struct {
	Id           int64  `json:"id"`
	Name         string `json:"name" `                                    // 模型名称
	Llm<PERSON>ey       string `json:"llm_key" `                                 // 模型唯一key
	CreateUserId int64  `json:"create_user_id"`                           // 创建者ID
	Remark       string `json:"remark"`                                   // 模型描述
	UpdatedTime  int64  `json:"updated_time" gorm:"autoUpdateTime:milli"` // 修改时间
	CreatedTime  int64  `json:"created_time" gorm:"autoCreateTime:milli"` // 创建时间
	DeleteTime   int64  `json:"delete_time"`                              // 删除时间
}

func (m LlmModel) TableName() string {
	return "llms"
}
