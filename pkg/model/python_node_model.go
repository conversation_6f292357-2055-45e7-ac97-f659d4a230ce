package model

type PythonNodeModel struct {
	Id           int64   `json:"id"`
	Name         *string `json:"name"`
	Remark       *string `json:"remark"`
	Copyable     *bool   `json:"copyable"`
	AppId        *int64  `json:"app_id"`
	Code         *string `json:"code"`
	ResultVar    *string `json:"result_var"`
	PathTagVar   *string `json:"path_tag_var"`
	MacroList    *string `json:"macro_list"`
	CreateUserId *int64  `json:"create_user_id"`
	Router       *string `json:"router"`
	UpdatedTime  int64   `json:"updated_time" gorm:"autoUpdateTime:milli"`
	CreatedTime  int64   `json:"created_time" gorm:"autoCreateTime:milli"`
	DeleteTime   int64   `json:"delete_time"`
}

func (m PythonNodeModel) TableName() string {
	return "python_nodes"
}
