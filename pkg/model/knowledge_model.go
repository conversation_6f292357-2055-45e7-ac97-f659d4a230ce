package model

// create table knowledges
// (
//     id bigint primary key ,
//     name varchar(200) default '' not null comment '名称',
//     is_dir bool default false not null comment '是否是目录',
//     parent_id bigint default 0 not null comment '父目录id',
//     file_name varchar(200) default '' not null comment '文件原始名称',
//     content text not null comment '内容',
//     file_type varchar(200) default '' not null comment '文件类型【text、txt、pdf、doc】',
//     s3_key varchar(200) default '' not null comment 'oss key',
//     remark varchar(200) default '' not null comment '备注',
//     create_user_id bigint default 0 not null comment '创建人',
//     updated_time bigint default 0 not null comment '修改时间',
//     created_time bigint default 0 not null comment '创建时间',
//     delete_time bigint default 0 not null comment '删除时间'

// )CHARACTER SET utf8mb4
//
//	COLLATE utf8mb4_0900_ai_ci comment '知识库';
type KnowledgeModel struct {
	Id           int64  `json:"id" gorm:"primaryKey"`
	Name         string `json:"name"`
	IsDir        bool   `json:"is_dir"`
	ParentId     int64  `json:"parent_id"`
	FileName     string `json:"file_name"`
	Content      string `json:"content"`
	FileType     string `json:"file_type"`
	S3Key        string `json:"s3_key"`
	Remark       string `json:"remark"`
	CreateUserId int64  `json:"create_user_id"`
	IsData       bool   `json:"is_data"`
	UpdatedTime  int64  `json:"updated_time" gorm:"autoUpdateTime:milli"`
	CreatedTime  int64  `json:"created_time" gorm:"autoCreateTime:milli"`
	DeleteTime   int64  `json:"delete_time"`
}

func (m KnowledgeModel) TableName() string {
	return "knowledges"
}
