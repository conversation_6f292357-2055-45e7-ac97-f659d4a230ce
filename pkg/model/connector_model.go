package model

type ConnectorModel struct {
	Id              int64   `json:"id"`
	Name            string  `json:"name"` // 应用名称
	RestfulUrl      string  `json:"restful_url"`
	Remark          string  `json:"remark"`
	Copyable        bool    `json:"copyable"` // 是否允许copy
	AppId           int64   `json:"app_id,string"`
	ResultParamsKey string  `json:"result_params_key"`
	MacroList       *string `json:"macro_list"` // description内容中引用的宏列表
	CreateUserId    *int64  `json:"create_user_id"`
	Router          *string `json:"router"`
	UpdatedTime     int64   `json:"updated_time" gorm:"autoUpdateTime:milli"`
	CreatedTime     int64   `json:"created_time" gorm:"autoCreateTime:milli"`
	DeleteTime      int64   `json:"delete_time"`
}

func (c *ConnectorModel) TableName() string {
	return "connectors"
}
