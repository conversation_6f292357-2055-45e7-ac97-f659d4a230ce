package model

type ApplicationModel struct {
	Id int64 `json:"id"`

	Name     *string `json:"name"`
	Remark   *string `json:"remark"`
	Copyable *bool   `json:"copyable"`

	Sort *string `json:"sort"`

	CreateUserId int64 `json:"create_user_id"`
	UpdatedTime  int64 `json:"updated_time" gorm:"autoUpdateTime:milli"`
	CreatedTime  int64 `json:"created_time" gorm:"autoCreateTime:milli"`
	DeleteTime   int64 `json:"delete_time"`
}

func (m ApplicationModel) TableName() string {
	return "app"
}
