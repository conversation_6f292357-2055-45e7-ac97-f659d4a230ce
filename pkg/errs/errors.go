package errs

/*
系统错误码
错误码位 100
*/
var (
	Success      = New(000000, "成功")
	ErrorArgs    = New(100001, "请求参数错误")
	ErrorService = New(100002, "系统错误请稍后再试。")
	// ErrorNoData  = 100003 // 没有查询到数据
)

/*
用户相关错误码
业务错误码位 101
*/
var (
	ErrorAuth                = New(101001, "token校验失败")
	ErrorAccount             = New(101002, "用户名或密码错误")
	ErrorAcsAuth             = New(101003, "用户中心ACS校验失败")
	ErrorInvalidModification = New(101004, "无效修改")
)

type Error struct {
	Code int    // 错误码
	Msg  string // 错误信息
}

func (e Error) Error() string {
	return e.Msg
}

func New(code int, msg string) *Error {
	return &Error{Code: code, Msg: msg}
}

/*
Release相关
*/
var (
	ErrorReleaseNoModifyApproved = New(108001, "不能修改已批准或已发布的发版请求")
)
