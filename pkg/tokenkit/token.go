package tokenkit

import (
	"time"

	"github.com/golang-jwt/jwt"
)

const key = "XoF3TEj@8mR8YUvL"

const tokenDuration = time.Hour * 24 * 7

type Token struct {
	Str       string `json:"str"`
	UserId    int64  `json:"user_id"`
	CompanyId int64  `json:"company"`
}

func NewToken(userId, companyId int64) *Token {
	currentTime := time.Now()
	jwtToken := JWTToken{
		Magic: key,
		Claims: jwt.MapClaims{
			"exp":     currentTime.Add(tokenDuration).Unix(),
			"sub":     userId,
			"iat":     currentTime.Unix(),
			"company": companyId,
		},
	}

	token := Token{
		Str: jwtToken.CreateToken(),
	}
	return &token
}

func NewTokenFromStr(token string) (*Token, error) {
	jwtToken := JWTToken{
		Magic: key,
		Token: token,
	}
	claims, err := jwtToken.ParseToken()
	if err != nil {
		return nil, err
	}

	t := Token{
		Str:       token,
		UserId:    int64(claims["sub"].(float64)),
		CompanyId: int64(claims["company"].(float64)),
	}

	return &t, nil
}
