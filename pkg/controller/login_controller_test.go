package controller

import (
	"context"
	"crypto/md5"
	"encoding/hex"
	"jarvis_api/pkg/service"
	"jarvis_api/pkg/vo"
	"os"
	"sort"
	"strconv"
	"testing"
	"time"

	"github.com/davecgh/go-spew/spew"
	"github.com/go-resty/resty/v2"
	log "github.com/sirupsen/logrus"
)

// https://accounts.mobvista.com/authorize?response_type=code&client_id=1089&redirect_uri=http://localhost:9908/login
// func TestAcsLogin(t *testing.T) {
// 	acsLoginReq := &vo.AcsLoginReq{
// 		Code:        "NjVkZTk4M2M2MzE0Y5DMRF6UtQR2ZAftOGV1a1XFD2JE0RAgDgtKDiUI3BrOojqxiOyJMyQbkwqQktpZCg",
// 		RedirectUri: "http%3A%2F%2Flocalhost%3A9908%2Flogin",
// 	}
// 	response := Post(t, AcsLogin, acsLoginReq)
// 	spew.Dump(response)
// }

func TestSsoLogin(t *testing.T) {
	// code, err := login()
	// if err != nil {
	// 	return
	// }
	ssoLoginReq := &vo.SsoLoginReq{
		Code: "TATL124YH1AveiAJS_2vCRmVapU",
	}
	response := Post(t, SsoLogin, ssoLoginReq)
	spew.Dump(response)
}

type Code struct {
	Code string `json:"code"`
}

func login() (string, error) {
	req := &vo.SsoLoginReq{}
	ssoAccessToken, err := service.SsoService.GetAccessToken(context.Background(), req)
	if err != nil {
		log.Error(err)
		return "", err
	}
	spew.Dump(ssoAccessToken)
	ssoAppSecret := os.Getenv("SSO_APP_SECRET")

	ssoAppKey := os.Getenv("SSO_APP_KEY")
	reqMap := map[string]string{
		"appkey":        ssoAppKey,
		"target_appkey": ssoAppKey,
		"access_token":  ssoAccessToken.AccessToken,
		"timestamp":     strconv.FormatInt(time.Now().Unix(), 10),
	}
	reqMap["sign"] = generateSignature(reqMap, ssoAppSecret)

	code := &Code{}
	response, err := resty.New().R().SetQueryParams(reqMap).SetResult(code).Get(service.SsoUserCenterUrl + "/connect/gen_auth_code")
	if err != nil {
		log.Error(err)
		return "", err
	}
	spew.Dump(response)
	return code.Code, nil
}

func generateSignature(params map[string]string, secret string) string {
	keys := make([]string, 0, len(params))
	for k := range params {
		keys = append(keys, k)
	}
	sort.Strings(keys)

	var kvList string
	for _, key := range keys {
		kvList += key + "=" + params[key]
	}
	originSign := kvList + secret

	hasher := md5.New()
	hasher.Write([]byte(originSign))
	return hex.EncodeToString(hasher.Sum(nil))
}
