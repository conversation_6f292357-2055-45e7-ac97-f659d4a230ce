package controller

import (
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestSanitizeFileName(t *testing.T) {
	tests := []struct {
		name     string
		input    string
		expected string
	}{
		{
			name:     "简单文件名",
			input:    "test.txt",
			expected: "test.txt",
		},
		{
			name:     "带空格的文件名",
			input:    "my document.pdf",
			expected: "my document.pdf",
		},
		{
			name:     "带括号的文件名",
			input:    "report(final).docx",
			expected: "report(final).docx",
		},
		{
			name:     "中文文件名",
			input:    "测试文件.docx",
			expected: "____.docx", // 中文字符会被替换为下划线
		},
		{
			name:     "特殊字符文件名",
			input:    "file@#$%^&*.txt",
			expected: "file_______.txt",
		},
		{
			name:     "复杂文件名",
			input:    "my-file_v2 (copy).txt",
			expected: "my-file_v2 (copy).txt",
		},
		{
			name:     "带引号的文件名",
			input:    "file\"with'quotes.txt",
			expected: "file_with_quotes.txt",
		},
		{
			name:     "带斜杠的文件名",
			input:    "path/to/file.txt",
			expected: "path_to_file.txt",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := sanitizeFileName(tt.input)
			assert.Equal(t, tt.expected, result)
		})
	}
}
