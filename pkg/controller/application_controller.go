package controller

import (
	"fmt"
	"jarvis_api/pkg/service"
	"jarvis_api/pkg/vo"
	"jarvis_api/tools"
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
	"github.com/jinzhu/copier"
)

func ListApplication(c *gin.Context) {
	ctx, log := tools.GenContext(c)

	appRes, err := service.ApplicationService.ListApplication(ctx)
	if err != nil {
		log.Error(err)
		ResponseWithError(c, http.StatusInternalServerError, err)
		return
	}

	DefaultResponse(c, appRes)
}

func GetApplicationResWithMacro(c *gin.Context) {
	ctx, log := tools.GenContext(c)
	appId := c.Param("id")
	appIdInt, err := strconv.ParseInt(appId, 10, 64)
	if err != nil {
		log.Error(err)
		ResponseWithError(c, http.StatusBadRequest, err)
		return
	}
	appRes, err := service.ApplicationService.GetApplication(ctx, appIdInt)
	if err != nil {
		log.Error(err)
		ResponseWithError(c, http.StatusInternalServerError, err)
		return
	}

	listApplicationRes := vo.ApplicationResWithMacro{}
	err = copier.Copy(&listApplicationRes, &appRes)
	if err != nil {
		log.Error(err)
		ResponseWithError(c, http.StatusInternalServerError, err)
		return
	}

	userIds, err := service.ApplicationService.GetCollaboration(ctx, appIdInt)
	if err != nil {
		log.Error(err)
		ResponseWithError(c, http.StatusInternalServerError, err)
		return
	}
	userIds = append(userIds, appRes.CreateUserId)
	userMaps, _, err := service.UserService.GetUserByIdList(ctx, userIds)
	if err != nil {
		log.Error(err)
		ResponseWithError(c, http.StatusInternalServerError, err)
		return
	}
	collaboration := []vo.UserSimpleInfo{}
	for _, userId := range userIds {
		userModel := userMaps[userId]
		UserSimpleInfo := vo.UserSimpleInfo{}
		err = copier.Copy(&UserSimpleInfo, &userModel)
		if err != nil {
			log.Error(err)
			continue
		}
		if userId == appRes.CreateUserId {
			listApplicationRes.CreateUser = UserSimpleInfo
			continue
		}
		collaboration = append(collaboration, UserSimpleInfo)
	}
	listApplicationRes.Collaboration = collaboration

	macroList, err := service.ApplicationService.GetMacroList(ctx, appIdInt)
	if err != nil {
		log.Error(err)
		ResponseWithError(c, http.StatusInternalServerError, err)
		return
	}
	listApplicationRes.MacroList = macroList

	DefaultResponse(c, listApplicationRes)
}

func CreateApplication(c *gin.Context) {
	ctx, log := tools.GenContext(c)
	createApplicationReq := &vo.CreateApplicationReq{}
	err := c.ShouldBind(createApplicationReq)
	if err != nil {
		log.Error(err)
		ResponseWithError(c, http.StatusBadRequest, err)
		return
	}
	err = createApplicationReq.Validation(c)
	if err != nil {
		log.Error(err)
		ResponseWithError(c, http.StatusBadRequest, err)
		return
	}

	id, err := service.ApplicationService.CreateApplication(ctx, createApplicationReq)
	if err != nil {
		log.Error(err)
		ResponseWithError(c, http.StatusInternalServerError, err)
		return
	}

	DefaultResponse(c, gin.H{"id": strconv.FormatInt(id, 10)})
}

func CopyApplication(c *gin.Context) {
	ctx, log := tools.GenContext(c)
	appId := c.Param("id")
	appIdInt, err := strconv.ParseInt(appId, 10, 64)
	if err != nil {
		log.Error(err)
		ResponseWithError(c, http.StatusBadRequest, err)
		return
	}

	id, err := service.ApplicationService.CopyApplication(ctx, appIdInt)
	if err != nil {
		log.Error(err)
		ResponseWithError(c, http.StatusInternalServerError, err)
		return
	}

	DefaultResponse(c, gin.H{"id": strconv.FormatInt(id, 10)})
}

func UpdateApplication(c *gin.Context) {
	ctx, log := tools.GenContext(c)
	appId := c.Param("id")
	appIdInt, err := strconv.ParseInt(appId, 10, 64)
	if err != nil {
		log.Error(err)
		ResponseWithError(c, http.StatusBadRequest, err)
		return
	}

	updateApplicationReq := &vo.UpdateApplicationReq{}
	err = c.ShouldBind(updateApplicationReq)
	if err != nil {
		log.Error(err)
		ResponseWithError(c, http.StatusBadRequest, err)
		return
	}
	updateApplicationReq.Id = appIdInt

	err = updateApplicationReq.Validation(c)
	if err != nil {
		log.Error(err)
		ResponseWithError(c, http.StatusBadRequest, err)
		return
	}

	err = service.ApplicationService.UpdateApplication(ctx, updateApplicationReq)
	if err != nil {
		log.Error(err)
		ResponseWithError(c, http.StatusInternalServerError, err)
		return
	}
	DefaultResponse(c, "success")
}

func DeleteApplication(c *gin.Context) {
	ctx, log := tools.GenContext(c)
	appId := c.Param("id")
	appIdInt, err := strconv.ParseInt(appId, 10, 64)
	if err != nil {
		log.Error(err)
		ResponseWithError(c, http.StatusBadRequest, err)
		return
	}

	req := &vo.DeleteApplicationReq{Id: appIdInt}
	//err := c.ShouldBind(req)
	//if err != nil {
	//	log.Error(err)
	//	ResponseWithError(c, http.StatusBadRequest, err)
	//	return
	//}

	err = req.Validation(c)
	if err != nil {
		log.Error(err)
		ResponseWithError(c, http.StatusBadRequest, err)
		return
	}

	err = service.ApplicationService.DeleteApplication(ctx, req)
	if err != nil {
		log.Error(err)
		ResponseWithError(c, http.StatusInternalServerError, err)
		return
	}
	DefaultResponse(c, "success")
}

func ResetCollaboration(c *gin.Context) {
	ctx, log := tools.GenContext(c)
	resetCollaborationReq := vo.ResetCollaborationReq{}
	err := c.ShouldBind(&resetCollaborationReq)
	if err != nil {
		log.Error(err)
		ResponseWithError(c, http.StatusBadRequest, err)
		return
	}

	current_user_id := tools.GetUserId(ctx)
	if len(resetCollaborationReq.CollaborationIds) > 0 {
		for i, id := range resetCollaborationReq.CollaborationIds {
			if id == fmt.Sprintf("%v", current_user_id) {
				resetCollaborationReq.CollaborationIds = append(resetCollaborationReq.CollaborationIds[:i], resetCollaborationReq.CollaborationIds[i+1:]...)
				break
			}
		}
	}

	if len(resetCollaborationReq.CollaborationIds) == 0 {
		DefaultResponse(c, "success")
		return
	}
	err = resetCollaborationReq.Validation(c)
	if err != nil {
		log.Error(err)
		ResponseWithError(c, http.StatusBadRequest, err)
		return
	}
	err = service.ApplicationService.ResetCollaboration(ctx, &resetCollaborationReq)
	if err != nil {
		log.Error(err)
		ResponseWithError(c, http.StatusInternalServerError, err)
		return
	}
	DefaultResponse(c, "success")
}
