package controller

import (
	"fmt"
	"net/http"
	"strconv"

	"jarvis_api/pkg/service"
	"jarvis_api/pkg/vo"
	"jarvis_api/tools"

	"github.com/gin-gonic/gin"
)

func UpdateConnector(c *gin.Context) {
	ctx, log := tools.GenContext(c)
	id := c.Param("id")
	idInt, err := strconv.ParseInt(id, 10, 64)
	if err != nil {
		log.Error(err)
		ResponseWithError(c, http.StatusBadRequest, err)
		return
	}

	updateReq := &vo.UpdateConnectorReq{}
	err = c.ShouldBind(updateReq)
	if err != nil {
		log.Error(err)
		ResponseWithError(c, http.StatusBadRequest, err)
		return
	}
	err = updateReq.Validation(c)
	if err != nil {
		log.Error(err)
		ResponseWithError(c, http.StatusBadRequest, err)
		return
	}

	err = service.ConnectorService.UpdateConnector(ctx, idInt, updateReq)
	if err != nil {
		log.Error(err)
		ResponseWithError(c, http.StatusInternalServerError, err)
		return
	}

	connector, err := service.ConnectorService.GetConnector(ctx, idInt)
	if err != nil {
		log.Error(err)
		ResponseWithError(c, http.StatusInternalServerError, err)
		return
	}

	DefaultResponse(c, connector)
}

func DeleteConnector(c *gin.Context) {
	ctx, log := tools.GenContext(c)
	id := c.Param("id")
	idInt, err := strconv.ParseInt(id, 10, 64)
	if err != nil {
		log.Error(err)
		ResponseWithError(c, http.StatusBadRequest, err)
		return
	}
	err = service.ConnectorService.DeleteConnector(ctx, idInt)
	if err != nil {
		log.Error(err)
		ResponseWithError(c, http.StatusInternalServerError, err)
		return
	}

	DefaultResponse(c, nil)
}

func GetConnector(c *gin.Context) {
	ctx, log := tools.GenContext(c)
	id := c.Param("id")
	idInt, err := strconv.ParseInt(id, 10, 64)
	if err != nil {
		log.Error(err)
		ResponseWithError(c, http.StatusBadRequest, err)
		return
	}
	connector, err := service.ConnectorService.GetConnector(ctx, idInt)
	if err != nil {
		log.Error(err)
		ResponseWithError(c, http.StatusInternalServerError, err)
		return
	}

	DefaultResponse(c, connector)
}

func CreateConnector(c *gin.Context) {
	ctx, log := tools.GenContext(c)
	createReq := &vo.CreateConnectorReq{}
	err := c.ShouldBind(createReq)
	if err != nil {
		log.Error(err)
		ResponseWithError(c, http.StatusBadRequest, err)
		return
	}
	err = createReq.Validation(c)
	if err != nil {
		log.Error(err)
		ResponseWithError(c, http.StatusBadRequest, err)
		return
	}

	id, err := service.ConnectorService.CreateConnector(ctx, createReq)
	if err != nil {
		log.Error(err)
		ResponseWithError(c, http.StatusInternalServerError, err)
		return
	}

	DefaultResponse(c, gin.H{"id": fmt.Sprintf("%d", id)})
}

func ListConnector(c *gin.Context) {
	ctx, log := tools.GenContext(c)

	appIdInt := int64(0)
	appId := c.Query("app_id")
	if appId != "" {
		num, err := strconv.ParseInt(appId, 10, 64)
		if err != nil {
			log.Error(err)
			ResponseWithError(c, http.StatusBadRequest, err)
			return
		}
		appIdInt = num
	}

	connectorList, err := service.ConnectorService.ListConnector(ctx, appIdInt)
	if err != nil {
		log.Error(err)
		ResponseWithError(c, http.StatusInternalServerError, err)
		return
	}

	DefaultResponse(c, connectorList)
}
