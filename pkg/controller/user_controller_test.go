package controller

import (
	"jarvis_api/pkg/vo"
	"testing"

	"github.com/davecgh/go-spew/spew"
)

func TestSearchUser(t *testing.T) {
	response := GetParams(t, SearchUser, "/user/search/:key", "/user/search/蔡")
	spew.Dump(response)
}

func TestListUser(t *testing.T) {
	response := Get(t, ListUser, nil)
	spew.Dump(response)
}

func TestGetUserListByProjectId(t *testing.T) {
	response := GetParams(t, GetUserListByProjectId, "/user/:projectId", "/user/2")
	spew.Dump(response)
}

func TestApplyForUse(t *testing.T) {
	applyForUse := &vo.ApplyForUse{
		Reason: "test",
	}
	response := Post(t, ApplyForUse, applyForUse)
	spew.Dump(response)
}
