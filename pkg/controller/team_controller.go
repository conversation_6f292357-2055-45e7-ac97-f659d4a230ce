package controller

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"jarvis_api/constant"
	"jarvis_api/pkg/service"
	"jarvis_api/pkg/vo"
	"jarvis_api/tools"
	"net/http"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/jinzhu/copier"
)

func ListTeam(c *gin.Context) {
	ctx, log := tools.GenContext(c)

	appIdInt := int64(0)
	appId := c.Query("app_id")
	if appId != "" {
		num, err := strconv.ParseInt(appId, 10, 64)
		if err != nil {
			log.Error(err)
			ResponseWithError(c, http.StatusBadRequest, err)
			return
		}
		appIdInt = num
	}

	applicationList, err := service.TeamService.List(ctx, appIdInt)
	if err != nil {
		log.Error(err)
		ResponseWithError(c, http.StatusInternalServerError, err)
		return
	}

	var list []vo.TeamRes
	err = copier.Copy(&list, &applicationList)
	if err != nil {
		log.Error(err)
		ResponseWithError(c, http.StatusInternalServerError, err)
		return
	}

	DefaultResponse(c, list)
}

func GetTeamResWithMacro(c *gin.Context) {
	ctx, log := tools.GenContext(c)
	teamId := c.Param("id")
	teamIdInt, err := strconv.ParseInt(teamId, 10, 64)
	if err != nil {
		log.Error(err)
		ResponseWithError(c, http.StatusBadRequest, err)
		return
	}
	obj, err := service.TeamService.Get(ctx, teamIdInt)
	if err != nil {
		log.Error(err)
		ResponseWithError(c, http.StatusInternalServerError, err)
		return
	}

	list := vo.TeamResWithMacro{}
	err = copier.Copy(&list, &obj)
	if err != nil {
		log.Error(err)
		ResponseWithError(c, http.StatusInternalServerError, err)
		return
	}

	macroList, err := service.TeamService.GetMacroList(ctx, teamIdInt)
	if err != nil {
		log.Error(err)
		ResponseWithError(c, http.StatusInternalServerError, err)
		return
	}
	list.MacroList = macroList

	DefaultResponse(c, list)
}

func CreateTeam(c *gin.Context) {
	ctx, log := tools.GenContext(c)
	createReq := &vo.CreateTeamReq{}
	err := c.ShouldBind(createReq)
	if err != nil {
		log.Error(err)
		ResponseWithError(c, http.StatusBadRequest, err)
		return
	}
	err = createReq.Validation(c)
	if err != nil {
		log.Error(err)
		ResponseWithError(c, http.StatusBadRequest, err)
		return
	}

	id := tools.GenId()
	teamId, err := service.TeamService.Create(ctx, id, createReq)
	if err != nil {
		log.Error(err)
		ResponseWithError(c, http.StatusInternalServerError, err)
		return
	}

	DefaultResponse(c, gin.H{"id": strconv.FormatInt(teamId, 10)})
}

func UpdateTeam(c *gin.Context) {
	ctx, log := tools.GenContext(c)
	id := c.Param("id")
	idInt, err := strconv.ParseInt(id, 10, 64)
	if err != nil {
		log.Error(err)
		ResponseWithError(c, http.StatusBadRequest, err)
		return
	}

	updateReq := &vo.UpdateTeamReq{}
	err = c.ShouldBind(updateReq)
	if err != nil {
		log.Error(err)
		ResponseWithError(c, http.StatusBadRequest, err)
		return
	}
	updateReq.Id = idInt

	err = updateReq.Validation(c)
	if err != nil {
		log.Error(err)
		ResponseWithError(c, http.StatusBadRequest, err)
		return
	}

	err = service.TeamService.Update(ctx, updateReq)
	if err != nil {
		log.Error(err)
		ResponseWithError(c, http.StatusInternalServerError, err)
		return
	}
	DefaultResponse(c, "success")
}

func DeleteTeam(c *gin.Context) {
	ctx, log := tools.GenContext(c)
	id := c.Param("id")
	idInt, err := strconv.ParseInt(id, 10, 64)
	if err != nil {
		log.Error(err)
		ResponseWithError(c, http.StatusBadRequest, err)
		return
	}
	req := &vo.DeleteTeamReq{Id: idInt}
	err = req.Validation(c)
	if err != nil {
		log.Error(err)
		ResponseWithError(c, http.StatusBadRequest, err)
		return
	}

	err = service.TeamService.Delete(ctx, req)
	if err != nil {
		log.Error(err)
		ResponseWithError(c, http.StatusInternalServerError, err)
		return
	}
	DefaultResponse(c, "success")
}

func CopyTeam(c *gin.Context) {
	ctx, log := tools.GenContext(c)
	id := c.Param("id")
	teamId, err := strconv.ParseInt(id, 10, 64)
	if err != nil {
		log.Error(err)
		ResponseWithError(c, http.StatusBadRequest, err)
		return
	}

	copyReq := &vo.CopyTeamReq{}
	err = c.ShouldBind(copyReq)
	if err != nil {
		log.Error(err)
		ResponseWithError(c, http.StatusBadRequest, err)
		return
	}
	err = copyReq.Validation(c)
	if err != nil {
		log.Error(err)
		ResponseWithError(c, http.StatusBadRequest, err)
		return
	}
	copyReq.Id = teamId

	newTeamId, err := service.TeamService.Copy(ctx, copyReq)
	if err != nil {
		log.Error(err)
		ResponseWithError(c, http.StatusInternalServerError, err)
		return
	}

	DefaultResponse(c, gin.H{"id": strconv.FormatInt(newTeamId, 10)})
}

// {"type": "start", "date": "任务开始执行"}
type ResultData struct {
	Type string `json:"type"`
	Data string `json:"data"`
}

// AutoCreateTeam
func AutoCreateTeam(c *gin.Context) {
	ctx, log := tools.GenContext(c)

	utoCreateTeamReq := &vo.AutoCreateTeamReq{}
	err := c.ShouldBind(utoCreateTeamReq)
	if err != nil {
		log.Error(err)
		ResponseWithError(c, http.StatusBadRequest, err)
		return
	}

	triggerParams := struct {
		Name  string `json:"name"`
		Value string `json:"value"`
		Type  string `json:"type"`
	}{
		Name:  "requirement",
		Value: utoCreateTeamReq.Requirement,
		Type:  "string",
	}
	triggerParamsJson, err := json.Marshal([]any{triggerParams})
	if err != nil {
		log.Error(err)
		ResponseWithError(c, http.StatusInternalServerError, err)
		return
	}

	appId := int64(1942847098408247296)
	if gin.Mode() != gin.ReleaseMode {
		appId = int64(1945041042455629824)
	}
	createReq := &vo.CreateRunnerReq{
		Remark:        tools.Md5(time.Now().String()),
		RunnerType:    constant.RunnerTypeApp,
		AppId:         appId,
		TriggerParams: string(triggerParamsJson),
	}

	err = createReq.Validation(c)
	if err != nil {
		log.Error(err)
		ResponseWithError(c, http.StatusBadRequest, err)
		return
	}

	id, err := service.RunnerService.Create(ctx, createReq)
	if err != nil {
		log.Error(err)
		ResponseWithError(c, http.StatusInternalServerError, err)
		return
	}

	// maxagentRunnerOnlineUrl := "http://localhost:8080"
	maxagentRunnerOnlineUrl := "http://maxagent-runner-online.maxagent-prod.svc"
	if gin.Mode() != gin.ReleaseMode {
		maxagentRunnerOnlineUrl = "http://maxagent-runner-online.maxagent-test.svc"
	}

	// 设置超时时间
	timeOutSecond := 30 * 60

	// 创建HTTP客户端并设置超时
	httpClient := &http.Client{
		Timeout: time.Duration(timeOutSecond) * time.Second,
	}

	// 创建请求
	req, err := http.NewRequest("POST", maxagentRunnerOnlineUrl+"/runner/exec_event",
		bytes.NewBuffer([]byte(fmt.Sprintf(`{"runner_record_id":%d}`, id))))
	if err != nil {
		log.Error(err)
		ResponseWithError(c, http.StatusInternalServerError, err)
		return
	}

	req.Header.Set("Content-Type", "application/json")

	// 执行请求
	log.Info("开始发送请求到runner-online服务")
	resp, err := httpClient.Do(req)
	if err != nil {
		log.Error("请求runner-online服务失败:", err)
		ResponseWithError(c, http.StatusInternalServerError, err)
		return
	}
	log.Info("成功连接到runner-online服务")

	// 确保响应体被关闭
	defer resp.Body.Close()

	// 设置响应头
	c.Header("Content-Type", "text/event-stream")
	c.Header("Cache-Control", "no-cache")
	c.Header("Connection", "keep-alive")
	c.Header("Transfer-Encoding", "chunked")

	// 使用缓冲区累积数据，确保JSON消息完整性的同时保持流式传输
	var buffer []byte
	readBuffer := make([]byte, 1024)

	c.Stream(func(w io.Writer) bool {
		// 读取数据到缓冲区
		n, err := resp.Body.Read(readBuffer)
		if n > 0 {
			// 将新数据追加到累积缓冲区
			buffer = append(buffer, readBuffer[:n]...)

			// 尝试从缓冲区中提取完整的JSON消息
			for {
				jsonMsg, remaining, found := extractCompleteJSON(buffer)
				if !found {
					// 没有找到完整的JSON，继续读取更多数据
					break
				}
				if len(jsonMsg) == 0 {
					continue
				}

				// 发送完整的JSON消息
				if len(jsonMsg) > 0 {
					log.Infof("接收到完整消息: %s", string(jsonMsg))
					c.SSEvent("", string(jsonMsg))
				}

				// 更新缓冲区，移除已处理的数据
				buffer = remaining

				// 判断如果获取到的json数据是完整json 并且是agent 生成的最终创建team 所需的AutoCreateTeam
				if final_result_json, ok := service.TeamService.IsAutoCreateTeamJson(jsonMsg); ok {
					id, err := service.TeamService.AutoCreateTeam(ctx, utoCreateTeamReq, final_result_json)
					if err != nil {
						log.Error(err)
						// {"type": "start", "date": "任务开始执行"}
						resultData := ResultData{Type: "error", Data: err.Error()}
						resultDataJson, err := json.Marshal(resultData)
						if err != nil {
							log.Error(err)
							c.SSEvent("", `{"type": "error", "date": "任务执行失败1"}`)
							break
						}

						c.SSEvent("", resultDataJson)
						break
					}

					resultData := ResultData{Type: "teamid", Data: strconv.FormatInt(id, 10)}
					resultDataJson, err := json.Marshal(resultData)
					if err != nil {
						log.Error(err)
						c.SSEvent("", `{"type": "error", "date": "任务执行失败2"}`)
						break
					}

					c.SSEvent("", resultDataJson)
					break
				}
			}
			return true
		}

		// 处理读取错误或流结束
		if err != nil {
			if err != io.EOF {
				log.Error("读取响应流出错:", err)
			} else {
				log.Info("响应流已结束")
				// 处理缓冲区中剩余的数据
				if len(buffer) > 0 {
					log.Infof("处理剩余数据: %s", string(buffer))
					c.SSEvent("", string(buffer))
				}
			}
			return false
		}

		return true
	})

	log.Info("流式传输已完成")
}
