package controller

import (
	"encoding/json"
	"jarvis_api/pkg/service"
	"jarvis_api/pkg/vo"
	"jarvis_api/tools"
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
	"github.com/jinzhu/copier"
)

func ListAgent(c *gin.Context) {
	ctx, log := tools.GenContext(c)

	teamIdInt := int64(0)
	teamId := c.Query("team_id")
	if teamId != "" {
		num, err := strconv.ParseInt(teamId, 10, 64)
		if err != nil {
			log.Error(err)
			ResponseWithError(c, http.StatusBadRequest, err)
			return
		}
		teamIdInt = num
	}

	list, err := service.AgentService.List(ctx, teamIdInt)
	if err != nil {
		log.Error(err)
		ResponseWithError(c, http.StatusInternalServerError, err)
		return
	}

	DefaultResponse(c, list)
}

func GetAgent(c *gin.Context) {
	ctx, log := tools.GenContext(c)
	teamId := c.Param("id")
	teamIdInt, err := strconv.ParseInt(teamId, 10, 64)
	if err != nil {
		log.Error(err)
		ResponseWithError(c, http.StatusBadRequest, err)
		return
	}
	obj, err := service.AgentService.Get(ctx, teamIdInt)
	if err != nil {
		log.Error(err)
		ResponseWithError(c, http.StatusInternalServerError, err)
		return
	}

	list := vo.AgentRes{}
	err = copier.Copy(&list, &obj)
	if err != nil {
		log.Error(err)
		ResponseWithError(c, http.StatusInternalServerError, err)
		return
	}
	if obj.Knowledges != nil {
		err = json.Unmarshal([]byte(*obj.Knowledges), &list.Knowledges)
		if err != nil {
			log.Error(err)
			ResponseWithError(c, http.StatusInternalServerError, err)
			return
		}
	}

	DefaultResponse(c, list)
}

func CreateAgent(c *gin.Context) {
	ctx, log := tools.GenContext(c)
	createReq := &vo.CreateAgentReq{}
	err := c.ShouldBind(createReq)
	if err != nil {
		log.Error(err)
		ResponseWithError(c, http.StatusBadRequest, err)
		return
	}
	err = createReq.Validation(c)
	if err != nil {
		log.Error(err)
		ResponseWithError(c, http.StatusBadRequest, err)
		return
	}

	id, err := service.AgentService.Create(ctx, createReq)
	if err != nil {
		log.Error(err)
		ResponseWithError(c, http.StatusInternalServerError, err)
		return
	}

	DefaultResponse(c, gin.H{"id": strconv.FormatInt(id, 10)})
}

func UpdateAgent(c *gin.Context) {
	ctx, log := tools.GenContext(c)
	id := c.Param("id")
	idInt, err := strconv.ParseInt(id, 10, 64)
	if err != nil {
		log.Error(err)
		ResponseWithError(c, http.StatusBadRequest, err)
		return
	}

	updatReq := &vo.UpdateAgentReq{}
	err = c.ShouldBind(updatReq)
	if err != nil {
		log.Error(err)
		ResponseWithError(c, http.StatusBadRequest, err)
		return
	}
	updatReq.Id = idInt

	err = updatReq.Validation(c)
	if err != nil {
		log.Error(err)
		ResponseWithError(c, http.StatusBadRequest, err)
		return
	}

	err = service.AgentService.Update(ctx, updatReq)
	if err != nil {
		log.Error(err)
		ResponseWithError(c, http.StatusInternalServerError, err)
		return
	}
	DefaultResponse(c, "success")
}

func DeleteAgent(c *gin.Context) {
	ctx, log := tools.GenContext(c)
	id := c.Param("id")
	idInt, err := strconv.ParseInt(id, 10, 64)
	if err != nil {
		log.Error(err)
		ResponseWithError(c, http.StatusBadRequest, err)
		return
	}
	req := &vo.DeleteAgentReq{Ids: []int64{idInt}}
	err = req.Validation(c)
	if err != nil {
		log.Error(err)
		ResponseWithError(c, http.StatusBadRequest, err)
		return
	}

	err = service.AgentService.Delete(ctx, req)
	if err != nil {
		log.Error(err)
		ResponseWithError(c, http.StatusInternalServerError, err)
		return
	}
	DefaultResponse(c, "success")
}

func CopyAgent(c *gin.Context) {
	ctx, log := tools.GenContext(c)

	id := c.Param("id")
	agentId, err := strconv.ParseInt(id, 10, 64)
	if err != nil {
		log.Error(err)
		ResponseWithError(c, http.StatusBadRequest, err)
		return
	}

	copyReq := &vo.CopyAgentReq{}
	err = c.ShouldBind(copyReq)
	if err != nil {
		log.Error(err)
		ResponseWithError(c, http.StatusBadRequest, err)
		return
	}
	err = copyReq.Validation(c)
	if err != nil {
		log.Error(err)
		ResponseWithError(c, http.StatusBadRequest, err)
		return
	}
	copyReq.Id = agentId

	teamId, err := service.AgentService.Copy(ctx, copyReq)
	if err != nil {
		log.Error(err)
		ResponseWithError(c, http.StatusInternalServerError, err)
		return
	}

	DefaultResponse(c, gin.H{"id": strconv.FormatInt(teamId, 10)})
}
