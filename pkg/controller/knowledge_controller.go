package controller

import (
	"jarvis_api/pkg/service"
	"jarvis_api/pkg/vo"
	"jarvis_api/tools"
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
)

func ListKnowledge(c *gin.Context) {
	ctx, log := tools.GenContext(c)
	dirIdStr := c.Default<PERSON>uery("dir_id", "0")
	dirId, err := strconv.ParseInt(dirIdStr, 10, 64)
	if err != nil {
		log.Error(err)
		ResponseWithError(c, http.StatusBadRequest, err)
		return
	}
	isDataStr := c.<PERSON>ult<PERSON>("is_data", "false")
	isData, err := strconv.ParseBool(isDataStr)
	if err != nil {
		log.Error(err)
		ResponseWithError(c, http.StatusBadRequest, err)
		return
	}

	list, err := service.KnowledgeService.ListKnowledge(ctx, dirId, isData)
	if err != nil {
		log.Error(err)
		ResponseWithError(c, http.StatusInternalServerError, err)
		return
	}
	DefaultResponse(c, list)
}

func GetKnowledge(c *gin.Context) {
	ctx, log := tools.GenContext(c)
	idStr := c.Param("id")
	idInt, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		log.Error(err)
		ResponseWithError(c, http.StatusBadRequest, err)
		return
	}

	knowledge, err := service.KnowledgeService.GetKnowledge(ctx, idInt)
	if err != nil {
		log.Error(err)
		ResponseWithError(c, http.StatusInternalServerError, err)
		return
	}
	DefaultResponse(c, knowledge)
}

func CreateKnowledge(c *gin.Context) {
	ctx, log := tools.GenContext(c)
	createKnowledgeReq := &vo.CreateKnowledgeReq{}
	if err := c.ShouldBindJSON(createKnowledgeReq); err != nil {
		log.Error(err)
		ResponseWithError(c, http.StatusBadRequest, err)
		return
	}

	id, err := service.KnowledgeService.CreateKnowledge(ctx, createKnowledgeReq)
	if err != nil {
		log.Error(err)
		ResponseWithError(c, http.StatusInternalServerError, err)
		return
	}

	DefaultResponse(c, gin.H{"id": id})
}

func UpdateKnowledge(c *gin.Context) {
	ctx, log := tools.GenContext(c)
	idStr := c.Param("id")
	idInt, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		log.Error(err)
		ResponseWithError(c, http.StatusBadRequest, err)
		return
	}

	updateKnowledgeReq := &vo.UpdateKnowledgeReq{}
	if err := c.ShouldBindJSON(updateKnowledgeReq); err != nil {
		log.Error(err)
		ResponseWithError(c, http.StatusBadRequest, err)
		return
	}

	err = service.KnowledgeService.UpdateKnowledge(ctx, idInt, updateKnowledgeReq)
	if err != nil {
		log.Error(err)
		ResponseWithError(c, http.StatusInternalServerError, err)
		return
	}
	DefaultResponse(c, nil)
}

func DeleteKnowledge(c *gin.Context) {
	ctx, log := tools.GenContext(c)
	idStr := c.Param("id")
	idInt, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		log.Error(err)
		ResponseWithError(c, http.StatusBadRequest, err)
		return
	}

	err = service.KnowledgeService.DeleteKnowledge(ctx, idInt)
	if err != nil {
		log.Error(err)
		ResponseWithError(c, http.StatusInternalServerError, err)
		return
	}
	DefaultResponse(c, nil)
}
