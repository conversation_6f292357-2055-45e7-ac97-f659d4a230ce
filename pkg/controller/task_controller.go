package controller

import (
	"jarvis_api/pkg/service"
	"jarvis_api/pkg/vo"
	"jarvis_api/tools"
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
)

func ListTask(c *gin.Context) {
	ctx, log := tools.GenContext(c)

	teamIdInt := int64(0)
	teamId := c.Query("team_id")
	if teamId != "" {
		num, err := strconv.ParseInt(teamId, 10, 64)
		if err != nil {
			log.Error(err)
			ResponseWithError(c, http.StatusBadRequest, err)
			return
		}
		teamIdInt = num
	}

	list, err := service.TaskService.List(ctx, teamIdInt)
	if err != nil {
		log.Error(err)
		ResponseWithError(c, http.StatusInternalServerError, err)
		return
	}

	DefaultResponse(c, list)
}

func GetTask(c *gin.Context) {
	ctx, log := tools.GenContext(c)
	teamId := c.Param("id")
	teamIdInt, err := strconv.ParseInt(teamId, 10, 64)
	if err != nil {
		log.Error(err)
		ResponseWithError(c, http.StatusBadRequest, err)
		return
	}
	obj, err := service.TaskService.Get(ctx, teamIdInt)
	if err != nil {
		log.Error(err)
		ResponseWithError(c, http.StatusInternalServerError, err)
		return
	}

	DefaultResponse(c, obj)
}

func CreateTask(c *gin.Context) {
	ctx, log := tools.GenContext(c)
	createReq := &vo.CreateTaskReq{}
	err := c.ShouldBind(createReq)
	if err != nil {
		log.Error(err)
		ResponseWithError(c, http.StatusBadRequest, err)
		return
	}
	err = createReq.Validation(c)
	if err != nil {
		log.Error(err)
		ResponseWithError(c, http.StatusBadRequest, err)
		return
	}

	id, err := service.TaskService.Create(ctx, createReq)
	if err != nil {
		log.Error(err)
		ResponseWithError(c, http.StatusInternalServerError, err)
		return
	}

	DefaultResponse(c, gin.H{"id": strconv.FormatInt(id, 10)})
}

func UpdateTask(c *gin.Context) {
	ctx, log := tools.GenContext(c)
	id := c.Param("id")
	idInt, err := strconv.ParseInt(id, 10, 64)
	if err != nil {
		log.Error(err)
		ResponseWithError(c, http.StatusBadRequest, err)
		return
	}

	updatReq := &vo.UpdateTaskReq{}
	err = c.ShouldBind(updatReq)
	if err != nil {
		log.Error(err)
		ResponseWithError(c, http.StatusBadRequest, err)
		return
	}
	updatReq.Id = idInt

	err = updatReq.Validation(c)
	if err != nil {
		log.Error(err)
		ResponseWithError(c, http.StatusBadRequest, err)
		return
	}

	err = service.TaskService.Update(ctx, updatReq)
	if err != nil {
		log.Error(err)
		ResponseWithError(c, http.StatusInternalServerError, err)
		return
	}
	DefaultResponse(c, "success")
}

func DeleteTask(c *gin.Context) {
	ctx, log := tools.GenContext(c)
	id := c.Param("id")
	idInt, err := strconv.ParseInt(id, 10, 64)
	if err != nil {
		log.Error(err)
		ResponseWithError(c, http.StatusBadRequest, err)
		return
	}
	req := &vo.DeleteTaskReq{Ids: []int64{idInt}}
	err = req.Validation(c)
	if err != nil {
		log.Error(err)
		ResponseWithError(c, http.StatusBadRequest, err)
		return
	}

	err = service.TaskService.Delete(ctx, req)
	if err != nil {
		log.Error(err)
		ResponseWithError(c, http.StatusInternalServerError, err)
		return
	}
	DefaultResponse(c, "success")
}

func CopyTask(c *gin.Context) {
	ctx, log := tools.GenContext(c)
	id := c.Param("id")
	taskId, err := strconv.ParseInt(id, 10, 64)
	if err != nil {
		log.Error(err)
		ResponseWithError(c, http.StatusBadRequest, err)
		return
	}

	copyReq := &vo.CopyTaskReq{}
	err = c.ShouldBind(copyReq)
	if err != nil {
		log.Error(err)
		ResponseWithError(c, http.StatusBadRequest, err)
		return
	}
	err = copyReq.Validation(c)
	if err != nil {
		log.Error(err)
		ResponseWithError(c, http.StatusBadRequest, err)
		return
	}
	copyReq.Id = taskId

	newTaskId, _, err := service.TaskService.Copy(ctx, copyReq)
	if err != nil {
		log.Error(err)
		ResponseWithError(c, http.StatusInternalServerError, err)
		return
	}

	DefaultResponse(c, gin.H{"id": strconv.FormatInt(newTaskId, 10)})
}
