package controller

import (
	"encoding/base64"
	"encoding/json"
	"fmt"
	"testing"

	log "github.com/sirupsen/logrus"
)

const remoteTriggerReq_TriggerParams = `W3siY3VzdG9tZXJfZW1haWxfY29udGVudCI6IiMg6YKu5Lu25Li76aKY77yaUGF5bWVudCBhZHZpY2XvvIhZWVkyNzQxMVlZWe+8iVxuXG4jIyDpgq7ku7YgMe+8mkZyaSwgMjcgSnVuIDIwMjUgMTQ6MDM6MzUgKzA5MDAg5p2l6IeqID0/dXRmLTg/Qj81NUMwNWJDKzQ0R2Y0NEdMNDRHdD89IDx0X2tvdG9vQHNpci1iYXJyaWUuY28uanA+XG5cbmBgYFxuRGVhciBNaW50ZWdyYWwgVGVhbVxuXG5UaGlzIGlzIEtPVE9PIGZyb20gQkFSUklFIENPLiwgTFRELlxuSSBtYWRlIHRoZSBkZXBvc2l0IHRvZGF5IGFzIHBlciB0aGUgYXR0YWNobWVudC5cblxuSW4gdGhlIGRvY3VtZW50LCB0aGUgaW52b2ljZSBudW1iZXIgd2FzIGRpZmZlcmVudCBpbiB0aGUgdG9wIHJpZ2h0IGFuZCBpbiB0aGUgdGV4dC4gXG5UaGVyZWZvcmUsIEkgIGhhdmUgbWFkZSB0aGUgZGVwb3NpdCB1c2luZyB0aGUgaW52b2ljZSBudW1iZXIg4oCYWVlZWTI3NDExWVlZWeKAmSBpbiB0aGUgdGV4dC5cblxuSSBleHBlY3QgdGhlIG1vbmV5IHdpbGwgYXJyaXZlIHdpdGhpbiBhIGZldyBkYXlzLCBcbnNvIHBsZWFzZSB1cGRhdGUgeW91ciBhY2NvdW50IGFzIHNvb24gYXMgeW91IGhhdmUgY29uZmlybWVkIGl0LlxuXG5TaW5jZXJlbHksXG7ilIHilIHilIHilIHilIHilIHilIHilIHilIHilIHilIHilIHilIHilIHilIHilIHilIHilIFcbuagquW8j+S8muekvuODkOODquODvFxuaHR0cHM6Ly9zaXItYmFycmllLmNvLmpwL1xu4pSB4pSB4pSB4pSB4pSB4pSB4pSB4pSB4pSB4pSB4pSB4pSB4pSB4pSB4pSB4pSB4pSB4pSBXG7nkLTlsL7jgIDjgZ/jgYvjga0oVGFrYW5lIEtvdG9vKVxuTWFpbDp0X2tvdG9vQHNpci1iYXJyaWUuY28uanBcbk1PQklMRTogMDkwLTY1NzItNDgzNVxu4pSB4pSB4pSB4pSB4pSB4pSB4pSB4pSB4pSB4pSB4pSB4pSB4pSB4pSB4pSB4pSB4pSB4pSBXG7iiarnpZ7nlLDjgqrjg5XjgqPjgrnvvIjmnKznpL7jg7vllrbmpa3mnKzpg6jvvIniiatcbuOAkjEwMS0wMDQxXG7mnbHkuqzpg73ljYPku6PnlLDljLrnpZ7nlLDpoIjnlLDnlLoxLTMyLTdcbuOCr+ODrOOCueS4jeWLleeUo+elnueUsOODk+ODq0IxXG5URUw6IDAzLTUyMDktMDAxN1xuRkFYOiAwMy01MjA5LTAwMThcbuKJquadseS4remHjuOCquODleOCo+OCue+8iOeuoeeQhuacrOmDqO+8ieKJq1xu44CSMTY0LTAwMDNcbuadseS6rOmDveS4remHjuWMuuadseS4remHjjEtNTYtNiBSQVZJTkUgMTFcblRFTDogMDMtNTMzMi03MDM3XG5GQVg6IDAzLTUzMzItNzAzOFxu4pSB4pSB4pSB4pSB4pSB4pSB4pSB4pSB4pSB4pSB4pSB4pSB4pSB4pSB4pSB4pSB4pSB4pSBXG7vv7xcbmBgYFxuXG4jIyDpgq7ku7YgMu+8mkZyaSwgMjcgSnVuIDIwMjUgMTQ6MDM6MzUgKzA5MDAg5p2l6IeqID0/dXRmLTg/Qj81NUMwNWJDKzQ0R2Y0NEdMNDRHdD89IDx0X2tvdG9vQHNpci1iYXJyaWUuY28uanA+XG5cbmBgYFxuRGVhciBNaW50ZWdyYWwgVGVhbVxuXG5UaGlzIGlzIEtPVE9PIGZyb20gQkFSUklFIENPLiwgTFRELlxuSSBtYWRlIHRoZSBkZXBvc2l0IHRvZGF5IGFzIHBlciB0aGUgYXR0YWNobWVudC5cblxuSW4gdGhlIGRvY3VtZW50LCB0aGUgaW52b2ljZSBudW1iZXIgd2FzIGRpZmZlcmVudCBpbiB0aGUgdG9wIHJpZ2h0IGFuZCBpbiB0aGUgdGV4dC4gXG5UaGVyZWZvcmUsIEkgIGhhdmUgbWFkZSB0aGUgZGVwb3NpdCB1c2luZyB0aGUgaW52b2ljZSBudW1iZXIg4oCYWVlZWTI3NDExWVlZWeKAmSBpbiB0aGUgdGV4dC5cblxuSSBleHBlY3QgdGhlIG1vbmV5IHdpbGwgYXJyaXZlIHdpdGhpbiBhIGZldyBkYXlzLCBcbnNvIHBsZWFzZSB1cGRhdGUgeW91ciBhY2NvdW50IGFzIHNvb24gYXMgeW91IGhhdmUgY29uZmlybWVkIGl0LlxuXG5TaW5jZXJlbHksXG7ilIHilIHilIHilIHilIHilIHilIHilIHilIHilIHilIHilIHilIHilIHilIHilIHilIHilIFcbuagquW8j+S8muekvuODkOODquODvFxuaHR0cHM6Ly9zaXItYmFycmllLmNvLmpwL1xu4pSB4pSB4pSB4pSB4pSB4pSB4pSB4pSB4pSB4pSB4pSB4pSB4pSB4pSB4pSB4pSB4pSB4pSBXG7nkLTlsL7jgIDjgZ/jgYvjga0oVGFrYW5lIEtvdG9vKVxuTWFpbDp0X2tvdG9vQHNpci1iYXJyaWUuY28uanBcbk1PQklMRTogMDkwLTY1NzItNDgzNVxu4pSB4pSB4pSB4pSB4pSB4pSB4pSB4pSB4pSB4pSB4pSB4pSB4pSB4pSB4pSB4pSB4pSB4pSBXG7iiarnpZ7nlLDjgqrjg5XjgqPjgrnvvIjmnKznpL7jg7vllrbmpa3mnKzpg6jvvIniiatcbuOAkjEwMS0wMDQxXG7mnbHkuqzpg73ljYPku6PnlLDljLrnpZ7nlLDpoIjnlLDnlLoxLTMyLTdcbuOCr+ODrOOCueS4jeWLleeUo+elnueUsOODk+ODq0IxXG5URUw6IDAzLTUyMDktMDAxN1xuRkFYOiAwMy01MjA5LTAwMThcbuKJquadseS4remHjuOCquODleOCo+OCue+8iOeuoeeQhuacrOmDqO+8ieKJq1xu44CSMTY0LTAwMDNcbuadseS6rOmDveS4remHjuWMuuadseS4remHjjEtNTYtNiBSQVZJTkUgMTFcblRFTDogMDMtNTMzMi03MDM3XG5GQVg6IDAzLTUzMzItNzAzOFxu4pSB4pSB4pSB4pSB4pSB4pSB4pSB4pSB4pSB4pSB4pSB4pSB4pSB4pSB4pSB4pSB4pSB4pSBXG7vv7xcbmBgYCJ9XQ==`

func TestRemoteTriggerSync(t *testing.T) {
	triggerParamsByte, err := base64.StdEncoding.DecodeString(remoteTriggerReq_TriggerParams)
	if err != nil {
		log.Error(err)
		fmt.Println(err)
		return
	}

	isValid := json.Valid(triggerParamsByte)
	fmt.Println(isValid)
	triggerParamsStr := string(triggerParamsByte)
	fmt.Println(triggerParamsStr)
}
