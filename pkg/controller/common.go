package controller

import (
	"jarvis_api/constant"
	"net/http"

	"jarvis_api/pkg/errs"

	"time"

	"github.com/gin-gonic/gin"
)

type Response struct {
	Status    int         `json:"status"`
	ErrCode   int         `json:"err_code"`
	RequestId string      `json:"request_id"`
	Message   string      `json:"message"`
	Data      interface{} `json:"data,omitempty"`
	Time      int64       `json:"time"`
}

func DefaultResponse(c *gin.Context, result interface{}) {
	response := Response{
		Status:  http.StatusOK,
		Message: "success",
		Time:    time.Now().Unix(),
	}
	if result == nil {
		response.Data = struct{}{}
	} else {
		response.Data = result
	}

	requestId := c.GetString(constant.MiddlewareKeyRequestId)
	if requestId != "" {
		response.RequestId = requestId
	}
	c.JSON(response.Status, response)
}

/*
公共返回错误处理
*/
func ResponseWithError(c *gin.Context, code int, err error) {
	var response Response
	if v, ok := err.(*errs.Error); ok {
		response = Response{
			Status:  code,
			ErrCode: v.Code,
			Message: v.Msg,
			Time:    time.Now().Unix(),
		}

	} else {
		response = Response{
			Status:  code,
			ErrCode: errs.ErrorService.Code,
			Message: err.Error(),
			Time:    time.Now().Unix(),
		}
	}
	requestId := c.GetString(constant.MiddlewareKeyRequestId)
	if requestId != "" {
		response.RequestId = requestId
	}
	c.JSON(response.Status, response)
}
