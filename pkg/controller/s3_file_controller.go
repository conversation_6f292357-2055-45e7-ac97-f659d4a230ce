package controller

import (
	"errors"
	"io"
	"jarvis_api/pkg/service"
	"jarvis_api/tools"
	"net/http"
	"path/filepath"

	"github.com/gin-gonic/gin"
)

// sanitizeFileName 清理文件名中的特殊字符，确保在HTTP头中安全使用
func sanitizeFileName(fileName string) string {
	// 移除或替换可能导致问题的字符
	// 保留基本的文件名字符：字母、数字、点、下划线、连字符、空格、括号
	result := ""
	for _, r := range fileName {
		if (r >= 'a' && r <= 'z') || (r >= 'A' && r <= 'Z') ||
			(r >= '0' && r <= '9') || r == '.' || r == '_' ||
			r == '-' || r == ' ' || r == '(' || r == ')' {
			result += string(r)
		} else {
			// 将其他字符替换为下划线
			result += "_"
		}
	}
	return result
}

func ShowS3File(c *gin.Context) {
	ctx, log := tools.GenContext(c)
	s3Key := c.Query("s3_key")
	if s3Key == "" {
		log.Error("s3_key is empty")
		ResponseWithError(c, http.StatusBadRequest, errors.New("s3_key is empty"))
		return
	}

	getObjectOutput, err := service.S3Service.GetFile(ctx, s3Key)
	if err != nil {
		log.Error(err)
		ResponseWithError(c, http.StatusInternalServerError, err)
		return
	}

	defer getObjectOutput.Body.Close()

	var contentTypeMap = map[string]string{
		".png":  "image/png",
		".jpg":  "image/jpeg",
		".jpeg": "image/jpeg",
		".gif":  "image/gif",
		".webp": "image/webp",
		".svg":  "image/svg+xml",
	}

	// 获取文件扩展名
	ext := filepath.Ext(s3Key)

	// 尝试从映射中获取内容类型
	if contentType, ok := contentTypeMap[ext]; ok {
		c.Header("Content-Type", contentType)
	} else {
		// 如果无法识别，使用原始的 ContentType 或设置默认值
		if getObjectOutput.ContentType != nil {
			c.Header("Content-Type", *getObjectOutput.ContentType)
		} else {
			c.Header("Content-Type", "application/octet-stream")
		}
	}

	// 从S3 key中提取文件名
	fileName := filepath.Base(s3Key)

	// 清理文件名，确保在HTTP头中安全使用
	safeFileName := sanitizeFileName(fileName)

	// 设置Content-Disposition头，指定下载时的文件名
	c.Header("Content-Disposition", "attachment; filename=\""+safeFileName+"\"")
	c.Header("Transfer-Encoding", "chunked")

	// 将 S3 对象内容复制到响应
	_, err = io.Copy(c.Writer, getObjectOutput.Body)
	if err != nil {
		c.String(http.StatusInternalServerError, "Failed to send file")
		return
	}
	DefaultResponse(c, nil)
}
