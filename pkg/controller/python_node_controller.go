package controller

import (
	"jarvis_api/pkg/errs"
	"jarvis_api/pkg/service"
	"jarvis_api/pkg/vo"
	"jarvis_api/tools"
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
)

func CreatePythonNode(c *gin.Context) {
	_, log := tools.GenContext(c)
	log.Info("CreatePythonNode")

	createPythonNodeReq := &vo.CreatePythonNodeReq{}
	err := c.ShouldBind(createPythonNodeReq)
	if err != nil {
		log.Error(err)
		ResponseWithError(c, http.StatusBadRequest, err)
		return
	}
	err = createPythonNodeReq.Validation(c)
	if err != nil {
		log.Error(err)
		ResponseWithError(c, http.StatusBadRequest, err)
		return
	}

	id := tools.GenId()
	err = service.PythonNodeService.CreatePythonNode(c, id, createPythonNodeReq)
	if err != nil {
		log.Error(err)
		ResponseWithError(c, http.StatusInternalServerError, err)
		return
	}
	DefaultResponse(c, gin.H{"id": strconv.FormatInt(id, 10)})

}

func ListPythonNode(c *gin.Context) {
	_, log := tools.GenContext(c)
	log.Info("ListPythonNode")
	appIdInt := int64(0)
	appId := c.Query("app_id")
	if appId != "" {
		num, err := strconv.ParseInt(appId, 10, 64)
		if err != nil {
			log.Error(err)
			ResponseWithError(c, http.StatusBadRequest, err)
			return
		}
		appIdInt = num
	}

	list, err := service.PythonNodeService.ListPythonNode(c, appIdInt)
	if err != nil {
		log.Error(err)
		ResponseWithError(c, http.StatusInternalServerError, err)
		return
	}
	DefaultResponse(c, list)
}

func GetPythonNode(c *gin.Context) {
	_, log := tools.GenContext(c)
	log.Info("GetPythonNode")
	python_node_id := c.Param("id")
	if python_node_id == "" {
		log.Error("python_node_id is empty")
		ResponseWithError(c, http.StatusBadRequest, errs.ErrorArgs)
		return
	}
	python_node_idInt, err := strconv.ParseInt(python_node_id, 10, 64)
	if err != nil {
		log.Error(err)
		ResponseWithError(c, http.StatusBadRequest, err)
		return
	}
	python_node_res, err := service.PythonNodeService.GetPythonNode(c, python_node_idInt)
	if err != nil {
		log.Error(err)
		ResponseWithError(c, http.StatusInternalServerError, err)
		return
	}
	DefaultResponse(c, python_node_res)
}

func UpdatePythonNode(c *gin.Context) {
	_, log := tools.GenContext(c)
	log.Info("UpdatePythonNode")
	python_node_id := c.Param("id")
	if python_node_id == "" {
		log.Error("python_node_id is empty")
		ResponseWithError(c, http.StatusBadRequest, errs.ErrorArgs)
		return
	}
	python_node_idInt, err := strconv.ParseInt(python_node_id, 10, 64)
	if err != nil {
		log.Error(err)
		ResponseWithError(c, http.StatusBadRequest, err)
		return
	}

	updateReq := &vo.UpdatePythonNodeReq{}
	err = c.ShouldBind(updateReq)
	if err != nil {
		log.Error(err)
		ResponseWithError(c, http.StatusBadRequest, err)
		return
	}
	updateReq.Id = python_node_idInt

	err = updateReq.Validation(c)
	if err != nil {
		log.Error(err)
		ResponseWithError(c, http.StatusBadRequest, err)
		return
	}

	err = service.PythonNodeService.UpdatePythonNode(c, updateReq)
	if err != nil {
		log.Error(err)
		ResponseWithError(c, http.StatusInternalServerError, err)
		return
	}
	DefaultResponse(c, "success")
}

func DeletePythonNode(c *gin.Context) {
	_, log := tools.GenContext(c)
	log.Info("DeletePythonNode")

	python_node_id := c.Param("id")
	if python_node_id == "" {
		log.Error("python_node_id is empty")
		ResponseWithError(c, http.StatusBadRequest, errs.ErrorArgs)
		return
	}
	python_node_idInt, err := strconv.ParseInt(python_node_id, 10, 64)
	if err != nil {
		log.Error(err)
		ResponseWithError(c, http.StatusBadRequest, err)
		return
	}
	err = service.PythonNodeService.DeletePythonNode(c, python_node_idInt)
	if err != nil {
		log.Error(err)
		ResponseWithError(c, http.StatusInternalServerError, err)
		return
	}
	DefaultResponse(c, "success")
}
