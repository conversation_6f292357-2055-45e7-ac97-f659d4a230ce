package controller

import (
	"jarvis_api/pkg/vo"
	"testing"

	"github.com/aws/smithy-go/ptr"
	"github.com/davecgh/go-spew/spew"
)

func TestCreatePythonNode(t *testing.T) {
	createPythonNodeReq := &vo.CreatePythonNodeReq{
		Name:       "test",
		Remark:     "test",
		Copyable:   false,
		AppId:      1,
		Code:       "test",
		ResultVar:  "test",
		PathTagVar: "test",
		MacroList:  []vo.TaskMacro{},
	}
	Post(t, CreatePythonNode, createPythonNodeReq)
}

func TestListPythonNode(t *testing.T) {
	result := Get(t, ListPythonNode, nil)
	spew.Dump(result)
}

func TestListPythonNode2(t *testing.T) {
	result := Get(t, ListPythonNode, map[string]string{"team_id": "22"})
	spew.Dump(result)
}

func TestGetPythonNode(t *testing.T) {
	result := GetParams(t, GetPythonNode, "/python_node/:id", "/python_node/1933137894836957184")
	spew.Dump(result)
}
func TestGetPythonNode2(t *testing.T) {
	result := GetParams(t, GetPythonNode, "/python_node/:id", "/python_node/1933137894836957184")
	spew.Dump(result)
}

func TestUpdatePythonNode(t *testing.T) {
	updatePythonNodeReq := &vo.UpdatePythonNodeReq{
		Id:         1933137894836957184,
		Name:       ptr.String("test"),
		Remark:     ptr.String("test"),
		Copyable:   ptr.Bool(false),
		AppId:      ptr.Int64(1),
		Code:       ptr.String("test"),
		ResultVar:  ptr.String("test"),
		PathTagVar: ptr.String("test"),
		MacroList:  &[]vo.TaskMacro{},
	}
	PutParams(t, UpdatePythonNode, "/python_node/:id", "/python_node/1933137894836957184", updatePythonNodeReq)
}

func TestDeletePythonNode(t *testing.T) {
	DeleteParams(t, DeletePythonNode, "/python_node/:id", "/python_node/1933137894836957184")
}
