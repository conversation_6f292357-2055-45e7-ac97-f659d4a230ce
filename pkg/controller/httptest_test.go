package controller

import (
	"bytes"
	"encoding/json"
	"fmt"
	"net/http"
	"net/http/httptest"

	"jarvis_api/pkg/db"
	"jarvis_api/pkg/tokenkit"
	"jarvis_api/tools"
	"strings"
	"testing"

	"github.com/gin-gonic/gin"
	log "github.com/sirupsen/logrus"
	"github.com/stretchr/testify/assert"
)

// const token = "ZXlKaGJHY2lPaUpJVXpJMU5pSXNJblI1Y0NJNklrcFhWQ0o5LmV5SmpiMjF3WVc1NUlqb3hMQ0psZUhBaU9qRTNNVFkwTlRFd01UWXNJbWxoZENJNk1UY3hOVGcwTmpJeE5pd2ljM1ZpSWpveE5ETTVObjAuSHUwbkRvamNBVjRXTW5iY1B6WVhBOGZqdWZTMnRLc2JuVENBM2pjXzhqOA"
var token = tokenkit.NewToken(14592, 1).Str // kui.chen

func testInitGin(t *testing.T) *gin.Engine {
	assert.Nil(t, db.InitDBManager())
	assert.Nil(t, tools.InitSnowflake())
	engin := gin.Default()
	engin.Use(SetRequestId)
	engin.Use(MiddlewareToken)

	return engin
}

func Put(t *testing.T, ginFunc func(c *gin.Context), query map[string]string, body interface{}) *httptest.ResponseRecorder {
	engin := testInitGin(t)

	bodyByte, _ := json.Marshal(body)

	t.Log("createAuthorizationReqByte", string(bodyByte))

	uri := "/api/put"
	sb := []string{}
	for key, value := range query {
		sb = append(sb, fmt.Sprintf("%v=%v", key, value))
	}

	urlWithArgs := uri
	if len(sb) > 0 {
		urlWithArgs = fmt.Sprintf("%v?%v", urlWithArgs, strings.Join(sb, "&"))
	}
	request := httptest.NewRequest(http.MethodPut, urlWithArgs, bytes.NewBuffer(bodyByte))
	request.Header.Set("Content-Type", "application/json; charset=UTF-8")

	response := httptest.NewRecorder()

	engin.PUT(uri, ginFunc)
	// todo del
	request.Header.Add("Authorization", token)
	engin.ServeHTTP(response, request)

	return response
}

func Post(t *testing.T, ginFunc func(c *gin.Context), req interface{}) *httptest.ResponseRecorder {
	engin := testInitGin(t)

	reqByte, _ := json.Marshal(req)

	t.Log("createAuthorizationReqByte", string(reqByte))

	uri := "/api/post"

	request := httptest.NewRequest(http.MethodPost, uri, bytes.NewBuffer(reqByte))
	request.Header.Set("Content-Type", "application/json; charset=UTF-8")

	response := httptest.NewRecorder()

	engin.POST(uri, ginFunc)
	// todo del
	request.Header.Add("Authorization", token)
	engin.ServeHTTP(response, request)

	return response
}

func Get(t *testing.T, ginFunc func(c *gin.Context), req map[string]string) *httptest.ResponseRecorder {
	engin := testInitGin(t)

	reqByte, err := json.Marshal(req)
	if err != nil {
		log.Error(err)
		return nil
	}

	t.Log("createAuthorizationReqByte", string(reqByte))

	uri := "/api/get"

	sb := []string{}
	for key, value := range req {
		sb = append(sb, fmt.Sprintf("%v=%v", key, value))
	}

	urlWithArgs := uri
	if len(sb) > 0 {
		urlWithArgs = fmt.Sprintf("%v?%v", urlWithArgs, strings.Join(sb, "&"))
	}

	request := httptest.NewRequest(http.MethodGet, urlWithArgs, bytes.NewBuffer(reqByte))
	// todo del
	request.Header.Add("Authorization", token)
	response := httptest.NewRecorder()

	engin.GET(uri, ginFunc)
	engin.ServeHTTP(response, request)

	return response
}

// response := GetParams(t, DetailBundleGroup, "/api/:bundle_group_id", "/api/1")
func GetParams(t *testing.T, ginFunc func(c *gin.Context), url, fulUrl string) *httptest.ResponseRecorder {
	engin := testInitGin(t)

	request := httptest.NewRequest(http.MethodGet, fulUrl, nil)
	// todo del
	request.Header.Add("Authorization", token)
	response := httptest.NewRecorder()

	engin.GET(url, ginFunc)
	engin.ServeHTTP(response, request)

	return response
}

func PutParams(t *testing.T, ginFunc func(c *gin.Context), url, fulUrl string, body interface{}) *httptest.ResponseRecorder {
	engin := testInitGin(t)

	bodyByte, _ := json.Marshal(body)

	t.Log("createAuthorizationReqByte", string(bodyByte))

	request := httptest.NewRequest(http.MethodPut, fulUrl, bytes.NewBuffer(bodyByte))
	request.Header.Set("Content-Type", "application/json; charset=UTF-8")

	response := httptest.NewRecorder()

	engin.PUT(url, ginFunc)
	// todo del
	request.Header.Add("Authorization", token)
	engin.ServeHTTP(response, request)

	return response
}

func Patch(t *testing.T, ginFunc func(c *gin.Context), req interface{}) *httptest.ResponseRecorder {
	engin := testInitGin(t)

	reqByte, _ := json.Marshal(req)

	t.Log("createAuthorizationReqByte", string(reqByte))

	uri := "/api/post"

	request := httptest.NewRequest(http.MethodPost, uri, bytes.NewBuffer(reqByte))
	request.Header.Set("Content-Type", "application/json; charset=UTF-8")

	response := httptest.NewRecorder()

	engin.PATCH(uri, ginFunc)
	// todo del
	request.Header.Add("Authorization", token)
	engin.ServeHTTP(response, request)

	return response
}

func PatchParams(t *testing.T, ginFunc func(c *gin.Context), url, fulUrl string, body interface{}) *httptest.ResponseRecorder {
	engin := testInitGin(t)

	bodyByte, _ := json.Marshal(body)

	t.Log("createAuthorizationReqByte", string(bodyByte))

	request := httptest.NewRequest(http.MethodPatch, fulUrl, bytes.NewBuffer(bodyByte))
	request.Header.Set("Content-Type", "application/json; charset=UTF-8")

	response := httptest.NewRecorder()

	engin.PATCH(url, ginFunc)
	// todo del
	request.Header.Add("Authorization", token)
	engin.ServeHTTP(response, request)

	return response
}

func Delete(t *testing.T, ginFunc func(c *gin.Context)) *httptest.ResponseRecorder {
	engin := testInitGin(t)
	uri := "/api/delete"

	request := httptest.NewRequest(http.MethodPost, uri, nil)
	request.Header.Set("Content-Type", "application/json; charset=UTF-8")

	response := httptest.NewRecorder()

	engin.DELETE(uri, ginFunc)
	// todo del
	request.Header.Add("Authorization", token)
	engin.ServeHTTP(response, request)

	return response
}

func DeleteParams(t *testing.T, ginFunc func(c *gin.Context), url, fulUrl string) *httptest.ResponseRecorder {
	engin := testInitGin(t)

	request := httptest.NewRequest(http.MethodDelete, fulUrl, nil)
	request.Header.Set("Content-Type", "application/json; charset=UTF-8")

	response := httptest.NewRecorder()

	engin.DELETE(url, ginFunc)
	// todo del
	request.Header.Add("Authorization", token)
	engin.ServeHTTP(response, request)

	return response
}
