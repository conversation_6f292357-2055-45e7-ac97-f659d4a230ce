package controller

import (
	"bytes"
	"crypto/sha256"
	"encoding/hex"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"jarvis_api/constant"
	"jarvis_api/pkg/db"
	"jarvis_api/pkg/service"
	"jarvis_api/pkg/tokenkit"
	"jarvis_api/tools"
	"net/http"
	"regexp"
	"sort"
	"strconv"
	"strings"

	"github.com/gin-contrib/cors"
	"github.com/gin-gonic/gin"
	mysqldriver "github.com/go-sql-driver/mysql"
	"github.com/google/uuid"
	"github.com/sirupsen/logrus"
)

func SetRequestId(context *gin.Context) {
	context.Set(constant.MiddlewareKeyRequestId, strings.ReplaceAll(uuid.NewString(), "-", ""))
}

func CorsConfig() gin.HandlerFunc {
	// cors
	corsConfig := cors.DefaultConfig()
	corsConfig.AllowHeaders = append(corsConfig.AllowHeaders, "Authorization", "company_id")
	corsConfig.AllowOriginFunc = func(origin string) bool {
		_, err := regexp.MatchString(`mobvista.com|spotmaxtech.com|localhost|zon`, origin)
		if err != nil {
			logrus.Errorln(err)
		}
		return true // TODO
	}
	corsConfig.AllowCredentials = true
	return cors.New(corsConfig)
}

func MiddlewareToken(c *gin.Context) {
	ctx, log := tools.GenContext(c)

	accessToken := c.GetHeader("Authorization")
	if accessToken == "" {
		log.Warn("Authorization empty")
		ResponseWithError(c, http.StatusUnauthorized, errors.New("Authorization empty"))
		c.Abort()
		return
	}

	token, err := tokenkit.NewTokenFromStr(accessToken)
	if err != nil {
		ResponseWithError(c, http.StatusUnauthorized, err)
		c.Abort()
		return
	}
	userId := token.UserId

	user, err := service.UserService.GetUserById(ctx, userId)
	if err != nil {
		// 由于数据库密码是由aws秘钥管理，秘钥会定期刷新，为防止出现秘钥刷新后数据库连接失败的情况，
		// 这里检测如果出现数据库密码错误的的报错，就重新连接数据库
		mysqlError := &mysqldriver.MySQLError{}
		if errors.As(err, &mysqlError) && mysqlError.Number == 1045 {
			// 重连数据库
			err := db.InitDBManager()
			if err != nil {
				ResponseWithError(c, http.StatusInternalServerError, err)
				c.Abort()
				return
			}
			ResponseWithError(c, http.StatusInternalServerError, errors.New("网络错误请稍后再试"))
			c.Abort()
			return
		}

		ResponseWithError(c, http.StatusUnauthorized, err)
		c.Abort()
		return
	}
	fmt.Println(c.FullPath())
	if "/api/v1/user/apply_for_use" != c.FullPath() {
		if user.WhiteList != 1 {
			ResponseWithError(c, http.StatusForbidden, errors.New("您的账号已经加入wating list,请等待审核通过后再使用"))
			c.Abort()
			return
		}
	}

	c.Set(constant.MiddlewareKeyCompanyId, user.CompanyId)
	c.Set(constant.MiddlewareKeyUserId, user.Id)
	c.Set(constant.MiddlewareKeyUserEmail, user.Email)

	c.Next()
}

func MiddlewareSign(c *gin.Context) {
	ctx, log := tools.GenContext(c)

	// 校验 Content-Type 必须为 application/json
	contentType := c.GetHeader("Content-Type")
	if !strings.Contains(contentType, "application/json") && !strings.Contains(contentType, "multipart/form-data") {
		log.Warn("Content-Type must be application/json")
		ResponseWithError(c, http.StatusUnsupportedMediaType, errors.New("Content-Type must be application/json"))
		c.Abort()
		return
	}

	vcode := c.GetHeader("vcode")
	if vcode == "" {
		log.Warn("vcode empty")
		ResponseWithError(c, http.StatusUnauthorized, errors.New("vcode empty"))
		c.Abort()
		return
	}

	userId := c.GetHeader("userid")
	if userId == "" {
		log.Warn("userid empty")
		ResponseWithError(c, http.StatusUnauthorized, errors.New("userid empty"))
		c.Abort()
		return
	}

	userIdInt, err := strconv.ParseInt(userId, 10, 64)
	if err != nil {
		ResponseWithError(c, http.StatusUnauthorized, err)
		c.Abort()
		return
	}

	user, err := service.UserService.GetUserById(ctx, userIdInt)
	if err != nil {
		ResponseWithError(c, http.StatusUnauthorized, err)
		c.Abort()
		return
	}

	if strings.Contains(contentType, "multipart/form-data") {
		// TODO 校验vcode
		c.Set(constant.MiddlewareKeyCompanyId, user.CompanyId)
		c.Set(constant.MiddlewareKeyUserId, user.Id)
		c.Set(constant.MiddlewareKeyUserEmail, user.Email)

		c.Next()
		return
	}

	// 如果用户是志琨 则用固定值校验， 志琨程序参数中有很多特殊字符导致计算签名有问题
	if userIdInt == 14913 && vcode == "9e6aa4cea41df11bae402c0f9627715f" {
		c.Set(constant.MiddlewareKeyCompanyId, user.CompanyId)
		c.Set(constant.MiddlewareKeyUserId, user.Id)
		c.Set(constant.MiddlewareKeyUserEmail, user.Email)

		c.Next()
		return
	}

	// 涂苗光
	if userIdInt == 11155 && vcode == "052129f2c8401b85639d9021b2453d0e" {
		c.Set(constant.MiddlewareKeyCompanyId, user.CompanyId)
		c.Set(constant.MiddlewareKeyUserId, user.Id)
		c.Set(constant.MiddlewareKeyUserEmail, user.Email)

		c.Next()
		return
	}

	body, err := io.ReadAll(c.Request.Body)
	if err != nil {
		ResponseWithError(c, http.StatusBadRequest, err)
		c.Abort()
		return
	}
	// 重新设置请求体，使其可以被后续处理程序读取
	c.Request.Body = io.NopCloser(bytes.NewBuffer(body))
	validate := validateVcode(user.SecretKey, body, vcode)
	if !validate {
		ResponseWithError(c, http.StatusUnauthorized, errors.New("vcode error"))
		c.Abort()
		return
	}

	c.Set(constant.MiddlewareKeyCompanyId, user.CompanyId)
	c.Set(constant.MiddlewareKeyUserId, user.Id)
	c.Set(constant.MiddlewareKeyUserEmail, user.Email)

	c.Next()
}

func validateVcode(magic string, body []byte, vcode string) bool {
	// 确保JSON字符串的稳定性
	sortedPayload := string(sortJSON(body))
	fmt.Println(sortedPayload)
	// 计算base string
	baseString := magic + sortedPayload

	// 计算SHA256哈希
	h := sha256.New()
	h.Write([]byte(baseString))
	computedHash := hex.EncodeToString(h.Sum(nil))

	return computedHash == vcode
}

// sortJSON 对JSON进行排序以确保稳定的输出
func sortJSON(data []byte) []byte {
	var m map[string]interface{}
	if err := json.Unmarshal(data, &m); err != nil {
		return data
	}

	keys := make([]string, 0, len(m))
	for k := range m {
		keys = append(keys, k)
	}
	sort.Strings(keys)

	sorted := make(map[string]interface{})
	for _, k := range keys {
		sorted[k] = m[k]
	}

	result, _ := json.Marshal(sorted)
	return result
}
