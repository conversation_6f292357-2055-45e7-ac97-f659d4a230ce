package controller

import (
	"jarvis_api/pkg/errs"
	"jarvis_api/pkg/tokenkit"
	"jarvis_api/pkg/vo"
	"jarvis_api/tools"
	"net/http"

	"jarvis_api/pkg/service"

	"github.com/gin-gonic/gin"
	"github.com/jinzhu/copier"
)

// func AcsLogin(c *gin.Context) {
// 	ctx, log := tools.GenContext(c)
// 	input := &vo.AcsLoginReq{}
// 	err := c.ShouldBind(input)
// 	if err != nil {
// 		log.Error(err)
// 		ResponseWithError(c, http.StatusBadRequest, err)
// 		return
// 	}

// 	acsAccessToken, err := service.AcsService.GetAccessToken(ctx, input)
// 	if err != nil {
// 		log.Error(err)
// 		ResponseWithError(c, http.StatusBadRequest, err)
// 		return
// 	}

// 	acsUserInfo, err := service.AcsService.GetUserInfo(ctx, acsAccessToken)
// 	if err != nil {
// 		log.Error(err)
// 		ResponseWithError(c, http.StatusBadRequest, err)
// 		return
// 	}
// 	if acsUserInfo.Profiles.Status != 10 {
// 		log.WithField("status", acsUserInfo.Profiles.Status).Warn("用户状态异常请联系IT管理员")
// 		ResponseWithError(c, http.StatusUnauthorized, errors.New("用户状态异常请联系IT管理员"))
// 		return
// 	}

// 	// 用户中心的用户信息换取speed系统中用户信息
// 	user, err := service.UserService.AcsUserLogin(ctx, acsUserInfo)
// 	if err != nil {
// 		log.Error(err)
// 		ResponseWithError(c, http.StatusBadRequest, err)
// 		return
// 	}

// 	output := vo.AcsLoginRes{
// 		Id:    user.Id,
// 		Token: tokenkit.NewToken(user.Id, user.CompanyId).Str,
// 		Email: acsUserInfo.Profiles.Email,
// 		Name:  acsUserInfo.Profiles.RealName,
// 	}

// 	DefaultResponse(c, output)
// }

func SsoLogin(c *gin.Context) {
	ctx, log := tools.GenContext(c)
	input := &vo.SsoLoginReq{}
	err := c.ShouldBind(input)
	if err != nil {
		log.Error(err)
		ResponseWithError(c, http.StatusBadRequest, errs.ErrorArgs)
		return
	}

	err = input.Validate()
	if err != nil {
		log.Error(err)
		ResponseWithError(c, http.StatusBadRequest, errs.ErrorArgs)
		return
	}

	ssoAccessToken, err := service.SsoService.GetAccessToken(ctx, input)
	if err != nil {
		log.Error(err)
		ResponseWithError(c, http.StatusBadRequest, err)
		return
	}

	ssoUserInfo, err := service.SsoService.GetUserInfo(ctx, ssoAccessToken)

	if err != nil {
		log.Error(err)
		ResponseWithError(c, http.StatusBadRequest, err)
		return
	}

	// 赋值给acsUserInfo
	acsUserInfo := service.AcsUserInfo{}
	err = copier.Copy(&acsUserInfo, &ssoUserInfo.ThirdPartyInfo)
	if err != nil {
		log.Error(err)
		ResponseWithError(c, http.StatusBadRequest, err)
		return
	}

	// max connect的用户信息换取mrmax系统中用户信息
	user, err := service.UserService.AcsUserLogin(ctx, &acsUserInfo)
	if err != nil {
		log.Error(err)
		ResponseWithError(c, http.StatusBadRequest, err)
		return
	}

	output := vo.AcsLoginRes{
		Id:        user.Id,
		Token:     tokenkit.NewToken(user.Id, user.CompanyId).Str,
		Email:     acsUserInfo.Profiles.Email,
		Name:      acsUserInfo.Profiles.RealName,
		Username:  acsUserInfo.Profiles.Username,
		SecretKey: user.SecretKey,
	}

	DefaultResponse(c, output)
}
