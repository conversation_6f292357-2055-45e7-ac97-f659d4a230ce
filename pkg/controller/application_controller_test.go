package controller

import (
	"jarvis_api/pkg/vo"
	"testing"

	"github.com/davecgh/go-spew/spew"
)

func TestListApplication(t *testing.T) {
	listApplicationResponse := Get(t, ListApplication, nil)
	spew.Dump(listApplicationResponse)
}

func TestResetCollaboration(t *testing.T) {
	resetCollaborationReq := &vo.ResetCollaborationReq{
		AppId:            1,
		CollaborationIds: []string{"11", "2"},
	}
	response := Post(t, ResetCollaboration, resetCollaborationReq)
	spew.Dump(response)
}
