package controller

import (
	"bufio"
	"bytes"
	"encoding/base64"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"jarvis_api/constant"
	"jarvis_api/pkg/service"
	"jarvis_api/pkg/service/k8s"
	"jarvis_api/pkg/vo"
	"jarvis_api/tools"
	"net/http"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/go-resty/resty/v2"
	"github.com/jinzhu/copier"
	log "github.com/sirupsen/logrus"
	corev1 "k8s.io/api/core/v1"
	apierrors "k8s.io/apimachinery/pkg/api/errors"
)

func ListRunnerV2(c *gin.Context) {
	ctx, log := tools.GenContext(c)
	listReq := &vo.ListRunnerReq{}
	err := c.ShouldBind(listReq)
	if err != nil {
		log.Error(err)
		ResponseWithError(c, http.StatusBadRequest, err)
		return
	}
	err = listReq.Validation(c)
	if err != nil {
		log.Error(err)
		ResponseWithError(c, http.StatusBadRequest, err)
		return
	}

	list, err := service.RunnerService.ListRunnerV2(ctx, listReq)
	if err != nil {
		log.Error(err)
		ResponseWithError(c, http.StatusInternalServerError, err)
		return
	}

	DefaultResponse(c, list)

}

func ListRunner(c *gin.Context) {
	ctx, log := tools.GenContext(c)

	runnerType := constant.RunnerType(c.Query("runner_type"))
	if runnerType == "" {
		log.Error("runner_type is empty")
		ResponseWithError(c, http.StatusBadRequest, errors.New("runner_type is empty"))
		return
	}
	switch runnerType {
	case constant.RunnerTypeApp, constant.RunnerTypeAgentTeam, constant.RunnerTypeDataProcess, constant.RunnerTypeConnector, constant.RunnerTypePython:
		log.Debug("runner_type is supported")
	default:
		log.Error("runner_type is not supported")
		ResponseWithError(c, http.StatusBadRequest, errors.New("runner_type is not supported"))
		return
	}
	runnerTargetId, err := strconv.ParseInt(c.Query("runner_target_id"), 10, 64)
	if err != nil {
		log.Error(err)
		ResponseWithError(c, http.StatusBadRequest, err)
		return
	}
	if runnerTargetId == 0 {
		log.Error("runner_target_id is empty")
		ResponseWithError(c, http.StatusBadRequest, errors.New("runner_target_id is empty"))
		return
	}

	appRes, err := service.RunnerService.List(ctx, runnerType, runnerTargetId)
	if err != nil {
		log.Error(err)
		ResponseWithError(c, http.StatusInternalServerError, err)
		return
	}

	DefaultResponse(c, appRes)
}

func GetRunnerStatus(c *gin.Context) {
	//ctx, log := tools.GenContext(c)
	runnerId := c.Param("id")
	runnerIdInt, err := strconv.ParseInt(runnerId, 10, 64)
	if err != nil {
		log.Error(err)
		ResponseWithError(c, http.StatusBadRequest, err)
		return
	}

	var (
		phase        corev1.PodPhase
		webServerUrl string
	)
	defer func() {
		obj := vo.RunnerStatus{
			Id:           runnerIdInt,
			Phase:        string(phase),
			WebServerUrl: webServerUrl,
		}
		if err != nil {
			obj.Error = err.Error()
		}

		DefaultResponse(c, obj)
	}()

	podName := "maxagent-runner-" + runnerId

	phase, err = k8s.K8sService.GetPodStatus(podName)
	if err != nil {
		log.Error(err)
		return
	}
	if phase == corev1.PodRunning {
		_, err = k8s.K8sService.CreateSvc(podName)
		if err != nil && !apierrors.IsAlreadyExists(err) {
			log.Error(err)
			return
		}
		//_, err = k8s.K8sService.CreateIngress(podName, podName)
		//if err != nil && !apierrors.IsAlreadyExists(err) {
		//	log.Error(err)
		//	return
		//}
		domain := "maxagent-test-api.spotmaxtech.com"
		if gin.Mode() == gin.ReleaseMode {
			domain = "maxagent-api.spotmaxtech.com"
		}
		webServerUrl = fmt.Sprintf("wss://%s/ws?runner_record_id=%s", domain, runnerId)
	}
	return
}

func GetRunner(c *gin.Context) {
	ctx, log := tools.GenContext(c)
	runnerId := c.Param("id")
	runnerIdInt, err := strconv.ParseInt(runnerId, 10, 64)
	if err != nil {
		log.Error(err)
		ResponseWithError(c, http.StatusBadRequest, err)
		return
	}
	obj, err := service.RunnerService.Get(ctx, runnerIdInt)
	if err != nil {
		log.Error(err)
		ResponseWithError(c, http.StatusInternalServerError, err)
		return
	}

	res := vo.RunnerRes{}
	err = copier.Copy(&res, &obj)
	if err != nil {
		log.Error(err)
		ResponseWithError(c, http.StatusInternalServerError, err)
		return
	}

	if obj.ExecuteLog == "" {
		DefaultResponse(c, gin.H{"log": []string{}, "result": res})
		return
	}

	// 如果有执行日志，直接返回日志内容

	executeLogOutput, err := service.S3Service.GetFile(ctx, obj.ExecuteLog)
	if err != nil {
		log.Error(err)
		ResponseWithError(c, http.StatusInternalServerError, err)
		return
	}

	defer executeLogOutput.Body.Close()

	logresult := []string{}
	// 使用bufio.Scanner一行一行读取
	scanner := bufio.NewScanner(executeLogOutput.Body)
	for scanner.Scan() {
		line := scanner.Text()
		logresult = append(logresult, line)
	}

	if err := scanner.Err(); err != nil {
		log.Error(err)
		ResponseWithError(c, http.StatusInternalServerError, err)
		return
	}

	DefaultResponse(c, gin.H{"log": logresult, "result": res})
}

func CreateRunner(c *gin.Context) {
	ctx, log := tools.GenContext(c)
	createReq := &vo.CreateRunnerReq{}
	err := c.ShouldBind(createReq)
	if err != nil {
		log.Error(err)
		ResponseWithError(c, http.StatusBadRequest, err)
		return
	}
	err = createReq.Validation(c)
	if err != nil {
		log.Error(err)
		ResponseWithError(c, http.StatusBadRequest, err)
		return
	}

	// AllowTeamId 和  AllowTasks 必须同步存在
	if len(createReq.AllowTasks) >= 0 {
		if createReq.AllowTeamId < 1 {
			ResponseWithError(c, http.StatusBadRequest, errors.New("AllowTeamId is empty"))
			return
		}
	}

	// AllowTeamId 和  AllowTasks 必须同步存在
	if createReq.AllowTeamId > 0 {
		if len(createReq.AllowTasks) == 0 {
			ResponseWithError(c, http.StatusBadRequest, errors.New("allow_tasks is empty"))
			return
		}
		// 如果是运行AgentTeam AgentTeamId和AllowTeamId 必须一直
		if createReq.RunnerType == constant.RunnerTypeAgentTeam {
			if createReq.AgentTeamId != createReq.AllowTeamId {
				ResponseWithError(c, http.StatusBadRequest, errors.New("run Agent team AgentTeamId must equal allow_team_id"))
				return
			}
		}

		// AllowTasks 必须都属于 AgentTeam
		taskRess, err := service.TaskService.List(ctx, createReq.AllowTeamId)
		if err != nil {
			log.Error(err)
			ResponseWithError(c, http.StatusInternalServerError, err)
			return
		}

		taskResMap := make(map[int64]struct{}, len(taskRess))
		for _, taskRes := range taskRess {
			taskResMap[taskRes.Id] = struct{}{}
		}

		for _, allowTaskId := range createReq.AllowTasks {
			allowTaskIdInt, err := strconv.ParseInt(allowTaskId, 10, 64)
			if err != nil {
				log.Error(err)
				ResponseWithError(c, http.StatusBadRequest, err)
				return
			}
			if _, exist := taskResMap[allowTaskIdInt]; !exist {
				ResponseWithError(c, http.StatusBadRequest, errors.New("allow_tasks is not belong to allow_team_id"))
				return
			}
		}

	}

	id, err := service.RunnerService.Create(ctx, createReq)
	if err != nil {
		log.Error(err)
		ResponseWithError(c, http.StatusInternalServerError, err)
		return
	}

	if createReq.PrimaryId != 0 {
		// 如果是多轮对话的第二轮，直接返回，不在创建pod
		DefaultResponse(c, gin.H{"id": strconv.FormatInt(id, 10)})
		return
	}

	pod, err := k8s.K8sService.CreatePod(id)
	if err != nil {
		log.Error(err)
		ResponseWithError(c, http.StatusInternalServerError, err)
		return
	}

	_, err = k8s.K8sService.CreateSvc(pod.Name)
	if err != nil {
		log.Error(err)
		return
	}

	DefaultResponse(c, gin.H{"id": strconv.FormatInt(id, 10)})
}

func RemoteTriggerAsync(c *gin.Context) {
	ctx, log := tools.GenContext(c)
	remoteTriggerReq := &vo.RemoteTriggerReq{}
	err := c.ShouldBind(remoteTriggerReq)
	if err != nil {
		log.Error(err)
		ResponseWithError(c, http.StatusBadRequest, err)
		return
	}

	createReq := &vo.CreateRunnerReq{
		Remark:        remoteTriggerReq.Remark,
		RunnerType:    constant.RunnerTypeApp,
		AppId:         remoteTriggerReq.AppId,
		TriggerParams: remoteTriggerReq.TriggerParams,
	}

	err = createReq.Validation(c)
	if err != nil {
		log.Error(err)
		ResponseWithError(c, http.StatusBadRequest, err)
		return
	}

	id, err := service.RunnerService.Create(ctx, createReq)
	if err != nil {
		log.Error(err)
		ResponseWithError(c, http.StatusInternalServerError, err)
		return
	}

	pod, err := k8s.K8sService.CreatePod(id)
	if err != nil {
		log.Error(err)
		ResponseWithError(c, http.StatusInternalServerError, err)
		return
	}
	_, err = k8s.K8sService.CreateSvc(pod.Name)
	if err != nil {
		log.Error(err)
		return
	}
	DefaultResponse(c, gin.H{"id": strconv.FormatInt(id, 10)})
}

func RemoteTriggerSync(c *gin.Context) {
	ctx, log := tools.GenContext(c)
	remoteTriggerReq := &vo.RemoteTriggerSyncReq{}
	err := c.ShouldBind(remoteTriggerReq)
	if err != nil {
		log.Error(err)
		ResponseWithError(c, http.StatusBadRequest, err)
		return
	}
	triggerParams := remoteTriggerReq.TriggerParams
	if remoteTriggerReq.Coding == "base64" {
		triggerParamsByte, err := base64.StdEncoding.DecodeString(remoteTriggerReq.TriggerParams)
		if err != nil {
			log.Error(err)
			ResponseWithError(c, http.StatusBadRequest, err)
			return
		}
		triggerParams = string(triggerParamsByte)
	}

	createReq := &vo.CreateRunnerReq{
		Remark:        remoteTriggerReq.Remark,
		RunnerType:    constant.RunnerTypeApp,
		AppId:         remoteTriggerReq.AppId,
		TriggerParams: triggerParams,
	}

	err = createReq.Validation(c)
	if err != nil {
		log.Error(err)
		ResponseWithError(c, http.StatusBadRequest, err)
		return
	}

	id, err := service.RunnerService.Create(ctx, createReq)
	if err != nil {
		log.Error(err)
		ResponseWithError(c, http.StatusInternalServerError, err)
		return
	}

	// maxagentRunnerOnlineUrl := "http://localhost:8080"
	maxagentRunnerOnlineUrl := "http://maxagent-runner-online.maxagent-prod.svc"
	if gin.Mode() == gin.TestMode {
		maxagentRunnerOnlineUrl = "http://maxagent-runner-online.maxagent-test.svc"
	}

	// todo: 通过http请求maxagent-runner-online服务，触发执行
	timeOutSecond := remoteTriggerReq.TimeOutSecond
	if timeOutSecond == 0 {
		timeOutSecond = 30 * 60
	}
	res, err := resty.New().
		SetTimeout(time.Duration(timeOutSecond)*time.Second).
		R().
		SetHeader("Content-Type", "application/json").
		SetBody(gin.H{"runner_record_id": id}).
		Post(maxagentRunnerOnlineUrl + "/runner/exec")
	if err != nil {
		log.Error(err)
		ResponseWithError(c, http.StatusInternalServerError, err)
		return
	}

	fmt.Println("========res.Body()========")
	fmt.Println(string(res.Body()))
	fmt.Println("========res.Body()========")
	result := struct {
		Message string `json:"message"`
		Data    any    `json:"data"`
	}{}
	err = json.Unmarshal(res.Body(), &result)

	if err != nil {
		log.Error(err)
		ResponseWithError(c, http.StatusInternalServerError, err)
		return
	}

	DefaultResponse(c, result)
}

func RemoteTriggerSyncStep(c *gin.Context) {
	ctx, log := tools.GenContext(c)
	remoteTriggerReq := &vo.RemoteTriggerSyncReq{}
	err := c.ShouldBind(remoteTriggerReq)
	if err != nil {
		log.Error(err)
		ResponseWithError(c, http.StatusBadRequest, err)
		return
	}

	createReq := &vo.CreateRunnerReq{
		Remark:        remoteTriggerReq.Remark,
		RunnerType:    constant.RunnerTypeApp,
		AppId:         remoteTriggerReq.AppId,
		TriggerParams: remoteTriggerReq.TriggerParams,
	}

	err = createReq.Validation(c)
	if err != nil {
		log.Error(err)
		ResponseWithError(c, http.StatusBadRequest, err)
		return
	}

	id, err := service.RunnerService.Create(ctx, createReq)
	if err != nil {
		log.Error(err)
		ResponseWithError(c, http.StatusInternalServerError, err)
		return
	}

	// maxagentRunnerOnlineUrl := "http://localhost:8080"
	maxagentRunnerOnlineUrl := "http://maxagent-runner-online.maxagent-prod.svc"
	if gin.Mode() == gin.TestMode {
		maxagentRunnerOnlineUrl = "http://maxagent-runner-online.maxagent-test.svc"
	}

	// todo: 通过http请求maxagent-runner-online服务，触发执行
	timeOutSecond := remoteTriggerReq.TimeOutSecond
	if timeOutSecond == 0 {
		timeOutSecond = 30 * 60
	}
	res, err := resty.New().
		SetTimeout(time.Duration(timeOutSecond)*time.Second).
		R().
		SetHeader("Content-Type", "application/json").
		SetBody(gin.H{"runner_record_id": id}).
		Post(maxagentRunnerOnlineUrl + "/runner/exec_step")
	if err != nil {
		log.Error(err)
		ResponseWithError(c, http.StatusInternalServerError, err)
		return
	}

	result := struct {
		Message string `json:"message"`
		Data    any    `json:"data"`
	}{}
	err = json.Unmarshal(res.Body(), &result)

	if err != nil {
		log.Error(err)
		ResponseWithError(c, http.StatusInternalServerError, err)
		return
	}

	DefaultResponse(c, result)
}

func RemoteTriggerSyncStream(c *gin.Context) {
	ctx, log := tools.GenContext(c)
	remoteTriggerReq := &vo.RemoteTriggerSyncReq{}
	err := c.ShouldBind(remoteTriggerReq)
	if err != nil {
		log.Error(err)
		ResponseWithError(c, http.StatusBadRequest, err)
		return
	}

	createReq := &vo.CreateRunnerReq{
		Remark:        remoteTriggerReq.Remark,
		RunnerType:    constant.RunnerTypeApp,
		AppId:         remoteTriggerReq.AppId,
		TriggerParams: remoteTriggerReq.TriggerParams,
	}

	err = createReq.Validation(c)
	if err != nil {
		log.Error(err)
		ResponseWithError(c, http.StatusBadRequest, err)
		return
	}

	id, err := service.RunnerService.Create(ctx, createReq)
	if err != nil {
		log.Error(err)
		ResponseWithError(c, http.StatusInternalServerError, err)
		return
	}

	// maxagentRunnerOnlineUrl := "http://localhost:8080"
	maxagentRunnerOnlineUrl := "http://maxagent-runner-online.maxagent-prod.svc"
	if gin.Mode() == gin.TestMode {
		maxagentRunnerOnlineUrl = "http://maxagent-runner-online.maxagent-test.svc"
	}

	// 设置超时时间
	timeOutSecond := remoteTriggerReq.TimeOutSecond
	if timeOutSecond == 0 {
		timeOutSecond = 30 * 60
	}

	// 创建HTTP客户端并设置超时
	httpClient := &http.Client{
		Timeout: time.Duration(timeOutSecond) * time.Second,
	}

	// 创建请求
	req, err := http.NewRequest("POST", maxagentRunnerOnlineUrl+"/runner/exec_event",
		bytes.NewBuffer([]byte(fmt.Sprintf(`{"runner_record_id":%d}`, id))))
	if err != nil {
		log.Error(err)
		ResponseWithError(c, http.StatusInternalServerError, err)
		return
	}

	req.Header.Set("Content-Type", "application/json")

	// 执行请求
	log.Info("开始发送请求到runner-online服务")
	resp, err := httpClient.Do(req)
	if err != nil {
		log.Error("请求runner-online服务失败:", err)
		ResponseWithError(c, http.StatusInternalServerError, err)
		return
	}
	log.Info("成功连接到runner-online服务")

	// 确保响应体被关闭
	defer resp.Body.Close()

	// 设置响应头
	c.Header("Content-Type", "text/event-stream")
	c.Header("Cache-Control", "no-cache")
	c.Header("Connection", "keep-alive")
	c.Header("Transfer-Encoding", "chunked")

	// 使用缓冲区累积数据，确保JSON消息完整性的同时保持流式传输
	var buffer []byte
	readBuffer := make([]byte, 1024)

	c.Stream(func(w io.Writer) bool {
		// 读取数据到缓冲区
		n, err := resp.Body.Read(readBuffer)
		if n > 0 {
			// 将新数据追加到累积缓冲区
			buffer = append(buffer, readBuffer[:n]...)

			// 尝试从缓冲区中提取完整的JSON消息
			for {
				jsonMsg, remaining, found := extractCompleteJSON(buffer)
				if !found {
					// 没有找到完整的JSON，继续读取更多数据
					break
				}

				// 发送完整的JSON消息
				if len(jsonMsg) > 0 {
					log.Infof("接收到完整消息: %s", string(jsonMsg))
					c.SSEvent("", string(jsonMsg))
				}

				// 更新缓冲区，移除已处理的数据
				buffer = remaining
			}
			return true
		}

		// 处理读取错误或流结束
		if err != nil {
			if err != io.EOF {
				log.Error("读取响应流出错:", err)
			} else {
				log.Info("响应流已结束")
				// 处理缓冲区中剩余的数据
				if len(buffer) > 0 {
					log.Infof("处理剩余数据: %s", string(buffer))
					c.SSEvent("", string(buffer))
				}
			}
			return false
		}

		return true
	})

	log.Info("流式传输已完成")
}

// extractCompleteJSON 从缓冲区中提取完整的JSON消息
// 返回值：jsonMsg(完整的JSON消息), remaining(剩余数据), found(是否找到完整JSON)
func extractCompleteJSON(buffer []byte) ([]byte, []byte, bool) {
	if len(buffer) == 0 {
		return nil, buffer, false
	}

	// 跳过开头的空白字符，找到JSON开始位置
	start := 0
	for start < len(buffer) && (buffer[start] == ' ' || buffer[start] == '\t' || buffer[start] == '\n' || buffer[start] == '\r') {
		start++
	}

	if start >= len(buffer) {
		return nil, buffer, false
	}

	// 寻找可能的JSON结束位置（查找'}' 字符）
	// 然后使用json.Valid验证是否为完整的JSON
	for i := start; i < len(buffer); i++ {
		if buffer[i] == '}' {
			// 找到可能的JSON结束位置，验证从start到i+1的数据
			candidate := buffer[start : i+1]
			if json.Valid(candidate) {
				// 找到完整的JSON消息
				remaining := buffer[i+1:]
				return candidate, remaining, true
			}
		}
	}

	// 没有找到完整的JSON对象
	return nil, buffer, false
}
