package controller

import (
	"errors"
	"jarvis_api/constant"
	"jarvis_api/pkg/service"
	"jarvis_api/tools"
	"net/http"

	"github.com/gin-gonic/gin"
)

func UploadFile(c *gin.Context) {
	ctx, log := tools.GenContext(c)
	file, err := c.FormFile("file")
	if err != nil {
		log.Error(err)
		ResponseWithError(c, http.StatusBadRequest, err)
		return
	}

	// 获取文件大小
	fileSize := file.Size
	if fileSize > 256*1024*1024 { // 256MB
		log.Error("file size too large")
		ResponseWithError(c, http.StatusBadRequest, errors.New("file size too large"))
		return
	}

	s3_key, err := service.FileUploadService.UploadFile(ctx, file)
	if err != nil {
		log.Error(err)
		ResponseWithError(c, http.StatusInternalServerError, err)
		return
	}
	DefaultResponse(c, gin.H{
		"s3_key": s3_key,
	})
}

func UploadKnowledgeFile(c *gin.Context) {
	ctx, log := tools.GenContext(c)
	// 上传文件
	file, err := c.FormFile("file")
	if err != nil {
		log.Error(err)
		ResponseWithError(c, http.StatusBadRequest, err)
		return
	}

	fileSizeLimit := int64(256 * 1024 * 1024)

	// 获取文件大小
	fileSize := file.Size
	if fileSize > fileSizeLimit { // 256MB
		log.Error("file size too large")
		ResponseWithError(c, http.StatusBadRequest, errors.New("file size too large"))
		return
	}

	// 获取文件类型
	fileType := file.Header.Get("Content-Type")

	// 如果不是doc、pdf、txt 等直接返回错误
	allowedTypes := map[string]string{
		"application/vnd.ms-word":                                                              string(constant.KnowledgeTypeDoc),
		"application/vnd.ms-word.document.12":                                                  string(constant.KnowledgeTypeDoc),
		"application/vnd.ms-word.document.macroEnabled.12":                                     string(constant.KnowledgeTypeDoc),
		"application/vnd.openxmlformats-officedocument.wordprocessingml.document.macroEnabled": string(constant.KnowledgeTypeDoc),
		"application/vnd.ms-word.template.12":                                                  string(constant.KnowledgeTypeDoc),
		"application/vnd.openxmlformats-officedocument.wordprocessingml.template":              string(constant.KnowledgeTypeDoc),
		"application/vnd.ms-word.template.macroEnabled.12":                                     string(constant.KnowledgeTypeDoc),
		"application/vnd.openxmlformats-officedocument.wordprocessingml.template.macroEnabled": string(constant.KnowledgeTypeDoc),
		"application/vnd.openxmlformats-officedocument.wordprocessingml.document":              string(constant.KnowledgeTypeDoc),
		"application/msword":  string(constant.KnowledgeTypeDoc),
		"application/pdf":     string(constant.KnowledgeTypePdf),
		"application/x-pdf":   string(constant.KnowledgeTypePdf),
		"application/acrobat": string(constant.KnowledgeTypePdf),
		"application/vnd.pdf": string(constant.KnowledgeTypePdf),
		"text/pdf":            string(constant.KnowledgeTypePdf),
		"text/x-pdf":          string(constant.KnowledgeTypePdf),
		"text/plain":          string(constant.KnowledgeTypeTxt),
	}

	// 检查文件类型是否被允许
	knowledgeType, isAllowed := allowedTypes[fileType]
	if !isAllowed {
		log.WithField("fileType", fileType).Error("file type not allowed")
		ResponseWithError(c, http.StatusBadRequest, errors.New("file type not allowed"))
		return
	}

	s3_key, err := service.FileUploadService.UploadFile(ctx, file)
	if err != nil {
		log.Error(err)
		ResponseWithError(c, http.StatusInternalServerError, err)
		return
	}
	DefaultResponse(c, gin.H{
		"s3_key":    s3_key,
		"file_name": file.Filename,
		"file_type": knowledgeType,
	})
}

func UploadDataFile(c *gin.Context) {
	ctx, log := tools.GenContext(c)
	// 上传文件
	file, err := c.FormFile("file")
	if err != nil {
		log.Error(err)
		ResponseWithError(c, http.StatusBadRequest, err)
		return
	}

	fileSizeLimit := int64(256 * 1024 * 1024)
	userId := tools.GetUserId(ctx)
	if userId == 18141 || userId == 12156 || userId == 14592 {
		fileSizeLimit = 1024 * 1024 * 1024
	}

	// 获取文件大小
	fileSize := file.Size
	if fileSize > fileSizeLimit { // 256MB
		log.Error("file size too large")
		ResponseWithError(c, http.StatusBadRequest, errors.New("file size too large"))
		return
	}

	// 获取文件类型
	fileType := file.Header.Get("Content-Type")

	// 如果不是doc、pdf、txt 等直接返回错误
	allowedTypes := map[string]string{
		"application/vnd.ms-word":                                                              string(constant.KnowledgeTypeDoc),
		"application/vnd.ms-word.document.12":                                                  string(constant.KnowledgeTypeDoc),
		"application/vnd.ms-word.document.macroEnabled.12":                                     string(constant.KnowledgeTypeDoc),
		"application/vnd.openxmlformats-officedocument.wordprocessingml.document.macroEnabled": string(constant.KnowledgeTypeDoc),
		"application/vnd.ms-word.template.12":                                                  string(constant.KnowledgeTypeDoc),
		"application/vnd.openxmlformats-officedocument.wordprocessingml.template":              string(constant.KnowledgeTypeDoc),
		"application/vnd.ms-word.template.macroEnabled.12":                                     string(constant.KnowledgeTypeDoc),
		"application/vnd.openxmlformats-officedocument.wordprocessingml.template.macroEnabled": string(constant.KnowledgeTypeDoc),
		"application/vnd.openxmlformats-officedocument.wordprocessingml.document":              string(constant.KnowledgeTypeDoc),
		"application/msword":  string(constant.KnowledgeTypeDoc),
		"application/pdf":     string(constant.KnowledgeTypePdf),
		"application/x-pdf":   string(constant.KnowledgeTypePdf),
		"application/acrobat": string(constant.KnowledgeTypePdf),
		"application/vnd.pdf": string(constant.KnowledgeTypePdf),
		"text/pdf":            string(constant.KnowledgeTypePdf),
		"text/x-pdf":          string(constant.KnowledgeTypePdf),
		"text/plain":          string(constant.KnowledgeTypeTxt),
	}

	// 检查文件类型是否被允许
	// knowledgeType, isAllowed := allowedTypes[fileType]
	// if !isAllowed {
	// 	log.WithField("fileType", fileType).Error("file type not allowed")
	// 	ResponseWithError(c, http.StatusBadRequest, errors.New("file type not allowed"))
	// 	return
	// }

	s3_key, err := service.FileUploadService.UploadFile(ctx, file)
	if err != nil {
		log.Error(err)
		ResponseWithError(c, http.StatusInternalServerError, err)
		return
	}
	if t, ok := allowedTypes[fileType]; ok {
		DefaultResponse(c, gin.H{
			"s3_key":    s3_key,
			"file_name": file.Filename,
			"file_type": t,
		})
	} else {
		DefaultResponse(c, gin.H{
			"s3_key":    s3_key,
			"file_name": file.Filename,
			"file_type": "Unknown",
		})
	}

}
