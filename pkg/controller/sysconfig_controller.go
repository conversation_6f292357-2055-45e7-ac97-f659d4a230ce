package controller

import (
	"jarvis_api/pkg/service"
	"jarvis_api/pkg/vo"
	"jarvis_api/tools"
	"net/http"

	"github.com/gin-gonic/gin"
)

func GetSysconfig(c *gin.Context) {
	ctx, log := tools.GenContext(c)

	input := &vo.SysconfigRequest{}
	err := c.ShouldBind(&input)
	if err != nil {
		log.Error(err)
		ResponseWithError(c, http.StatusBadRequest, err)
		return
	}

	sysconfig, err := service.SysConfigService.GetSysconfig(ctx, input)
	if err != nil {
		log.Error(err)
		ResponseWithError(c, http.StatusInternalServerError, err)
		return
	}

	DefaultResponse(c, sysconfig)
}

func SaveSysconfig(c *gin.Context) {
	ctx, log := tools.GenContext(c)
	input := &vo.SaveSysconfigReq{}
	err := c.ShouldBind(&input)
	if err != nil {
		log.Error(err)
		ResponseWithError(c, http.StatusBadRequest, err)
		return
	}

	err = service.SysConfigService.SaveSysconfig(ctx, input)
	if err != nil {
		log.Error(err)
		ResponseWithError(c, http.StatusInternalServerError, err)
		return
	}

	DefaultResponse(c, "success")
}
