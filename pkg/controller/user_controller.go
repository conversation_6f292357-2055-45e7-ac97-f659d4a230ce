package controller

import (
	"jarvis_api/pkg/errs"
	"jarvis_api/pkg/service"
	"jarvis_api/pkg/vo"
	"jarvis_api/tools"
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
	"github.com/go-resty/resty/v2"
	"gorm.io/gorm"
)

func ApplyForUse(c *gin.Context) {
	ctx, log := tools.GenContext(c)
	userId := tools.GetUserId(ctx)
	// userId := int64(14592)

	applyForUse := &vo.ApplyForUse{}
	err := c.ShouldBind(applyForUse)
	if err != nil {
		log.Error(err)
		ResponseWithError(c, http.StatusBadRequest, err)
		return
	}

	user, err := service.UserService.GetUserById(ctx, userId)
	if err != nil {
		log.Error(err)
		ResponseWithError(c, http.StatusInternalServerError, err)
		return
	}

	deliverReq := struct {
		UserName string
		Reason   string
	}{
		UserName: user.RealName,
		Reason:   applyForUse.Reason,
	}
	result, err := tools.ReplaceTemplate(ctx, DeliverContext, deliverReq)
	if err != nil {
		log.Error(err)
		ResponseWithError(c, http.StatusBadRequest, err)
		return
	}

	webhook := "https://oapi.dingtalk.com/robot/send?access_token=94bbc6c72c62dd044fec0ac814f94065dd5e477c97f7e94d91fa90ef98956781"
	client := resty.New()
	resp, err := client.R().
		SetHeader("Content-Type", "application/json").
		SetBody(map[string]interface{}{
			"msgtype": "markdown",
			"markdown": map[string]interface{}{
				"title": "MaxAgent试用申请",
				"text":  result,
			},
			"at": map[string]interface{}{
				"isAtAll": true,
			},
		}).
		Post(webhook)

	if err != nil {
		log.Error(err)
		ResponseWithError(c, http.StatusInternalServerError, err)
		return
	}

	if resp.StatusCode() != http.StatusOK {
		log.Error(err)
		ResponseWithError(c, http.StatusInternalServerError, err)
		return
	}

	DefaultResponse(c, "success")
}

const DeliverContext = `### 有新用户提交了MaxAgent试用申请，请及时处理
**申请人：**：{{.UserName}}

**申请理由：**：{{.Reason}}
`

func ResetSecretKey(c *gin.Context) {
	ctx, log := tools.GenContext(c)
	userId := tools.GetUserId(ctx)

	secretKey, err := service.UserService.ResetSecretKey(ctx, userId)
	if err != nil {
		log.Error(err)
		ResponseWithError(c, http.StatusInternalServerError, err)
		return
	}
	DefaultResponse(c, gin.H{"secret_key": secretKey})
}

func GetUserInfo(c *gin.Context) {
	ctx, log := tools.GenContext(c)

	userId := tools.GetUserId(ctx)
	user, err := service.UserService.GetUserById(ctx, userId)
	if err != nil && err != gorm.ErrRecordNotFound {
		log.Error(err)
		ResponseWithError(c, http.StatusInternalServerError, err)
		return
	}

	output := vo.AcsLoginRes{
		Id:        user.Id,
		Email:     user.Email,
		Name:      user.RealName,
		Username:  user.Username,
		SecretKey: user.SecretKey,
	}

	DefaultResponse(c, output)
}

func SearchUser(c *gin.Context) {
	ctx, log := tools.GenContext(c)

	key := c.Query("key")
	if key == "" {
		log.Error(errs.ErrorArgs)
		ResponseWithError(c, http.StatusBadRequest, errs.ErrorArgs)
		return
	}

	userRes, err := service.UserService.SearchUser(ctx, key)
	if err != nil {
		log.Error(err)
		ResponseWithError(c, http.StatusInternalServerError, err)
		return
	}
	DefaultResponse(c, userRes)
}

func ListUser(c *gin.Context) {
	ctx, log := tools.GenContext(c)

	userRes, err := service.UserService.ListUser(ctx)
	if err != nil {
		log.Error(err)
		ResponseWithError(c, http.StatusInternalServerError, err)
		return
	}
	DefaultResponse(c, userRes)

}
func GetUserListByProjectId(c *gin.Context) {
	ctx, log := tools.GenContext(c)
	projectIdStr := c.Param("projectId")
	if projectIdStr == "" {
		log.Error(errs.ErrorArgs)
		ResponseWithError(c, http.StatusBadRequest, errs.ErrorArgs)
		return
	}

	projectId, err := strconv.ParseInt(projectIdStr, 10, 64)
	if err != nil {
		log.Error(err)
		ResponseWithError(c, http.StatusBadRequest, errs.ErrorArgs)
		return
	}

	userRes, err := service.UserService.GetUserListByProjectId(ctx, projectId)
	if err != nil {
		log.Error(err)
		ResponseWithError(c, http.StatusInternalServerError, err)
		return
	}
	DefaultResponse(c, userRes)
}
