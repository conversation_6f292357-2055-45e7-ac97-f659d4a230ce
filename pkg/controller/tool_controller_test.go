package controller

import (
	"jarvis_api/pkg/vo"
	"testing"

	"github.com/aws/smithy-go/ptr"
	"github.com/davecgh/go-spew/spew"
)

func TestListTool(t *testing.T) {
	response := Get(t, ListTool, nil)
	spew.Dump(response)
}

func TestCreateTool(t *testing.T) {
	createToolReq := &vo.CreateToolReq{
		Name:      "test",
		Remark:    "test",
		IsPublic:  false,
		ToolType:  "mcp",
		Url:       "http://aa.com",
		Transport: "streamable-http",
	}
	res := Post(t, CreateTool, createToolReq)
	spew.Dump(res)
}

// UpdateTool
func TestUpdateTool(t *testing.T) {
	updateToolReq := &vo.UpdateToolReq{
		Name:      ptr.String("test_update"),
		Remark:    ptr.String("test"),
		IsPublic:  ptr.Bool(false),
		ToolType:  ptr.String("mcp"),
		Url:       ptr.String("http://aa1.com"),
		Transport: ptr.String("streamable-http"),
	}
	PutParams(t, UpdateTool, "/tool/:id", "/tool/1943517800618258432", updateToolReq)
}
