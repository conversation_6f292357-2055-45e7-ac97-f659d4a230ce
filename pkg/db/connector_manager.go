package db

import (
	"context"
	"jarvis_api/pkg/model"
	"time"

	"gorm.io/gorm"
)

type connectorManager struct {
	db *gorm.DB
}

func (c *connectorManager) Delete(ctx context.Context, id int64) error {
	return c.db.WithContext(ctx).Model(&model.ConnectorModel{}).Where("id = ?", id).Update("delete_time", time.Now().UnixMilli()).Error
}

func (c *connectorManager) Update(ctx context.Context, obj *model.ConnectorModel) error {
	return c.db.WithContext(ctx).Save(obj).Error
}

func (c *connectorManager) Create(ctx context.Context, obj *model.ConnectorModel) error {
	return c.db.WithContext(ctx).Create(obj).Error
}

func (c *connectorManager) List(ctx context.Context, appId int64) ([]model.ConnectorModel, error) {
	var connectorList []model.ConnectorModel
	err := c.db.WithContext(ctx).Where("app_id = ? and delete_time = 0", appId).Find(&connectorList).Error
	if err != nil {
		return nil, err
	}
	return connectorList, nil
}

func (c *connectorManager) Get(ctx context.Context, id int64) (*model.ConnectorModel, error) {
	connector := &model.ConnectorModel{}
	err := c.db.WithContext(ctx).Where("id = ? and delete_time = 0", id).First(connector).Error
	if err != nil {
		return nil, err
	}
	return connector, nil
}
