package db

import (
	"context"
	"jarvis_api/pkg/model"
	"time"

	"gorm.io/gorm"
)

type taskManager struct {
	db *gorm.DB
}

func (t *taskManager) Create(ctx context.Context, obj *model.TaskModel) error {
	return t.db.WithContext(ctx).Create(obj).Error
}

func (t *taskManager) Get(ctx context.Context, taskId int64) (*model.TaskModel, error) {
	var obj model.TaskModel
	tx := t.db.WithContext(ctx).Where("id = ? and delete_time = 0", taskId).First(&obj)
	if tx.Error != nil {
		return nil, tx.Error
	}
	return &obj, nil
}

func (t *taskManager) List(ctx context.Context, userId, teamId int64) ([]model.TaskModel, error) {
	var list []model.TaskModel
	tx := t.db.WithContext(ctx).Model(&model.TaskModel{}).
		Where("delete_time = 0")
	if teamId > 0 {
		tx.Where("agent_team_id = ?", teamId)
	} else {
		tx.Where("create_user_id = ? or copyable = 1", userId)
	}
	err := tx.Order("created_time desc").Find(&list).Error
	if err != nil {
		return nil, err
	}
	return list, nil
}

func (t *taskManager) ListForce(ctx context.Context, teamId int64) ([]model.TaskModel, error) {
	var list []model.TaskModel
	tx := t.db.WithContext(ctx).Model(&model.TaskModel{}).
		Where("delete_time = 0")
	//Where("create_user_id = ? or copyable = 1", userId)
	if teamId > 0 {
		tx.Where("agent_team_id = ?", teamId)
	}
	err := tx.Order("created_time desc").Find(&list).Error
	if err != nil {
		return nil, err
	}
	return list, nil
}

func (t *taskManager) PickupOtherTasks(ctx context.Context, userId int64, ids []int64) ([]int64, error) {
	var otherIds []int64
	tx := t.db.WithContext(ctx).Model(&model.TaskModel{}).Select("id").
		Where("delete_time = 0").
		Where("create_user_id != ?", userId).
		Where("id IN ?", ids)
	err := tx.Find(&otherIds).Error
	if err != nil {
		return nil, err
	}
	return otherIds, nil
}

func (t *taskManager) Update(ctx context.Context, obj *model.TaskModel) error {
	return t.db.WithContext(ctx).Updates(obj).Error
}

func (t *taskManager) Delete(ctx context.Context, ids []int64) error {
	return t.db.WithContext(ctx).Model(&model.TaskModel{}).
		Where("id IN ?", ids).
		Update("delete_time", time.Now().UnixMilli()).Error
}
