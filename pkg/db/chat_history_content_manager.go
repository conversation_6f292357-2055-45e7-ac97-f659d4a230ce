package db

import (
	"context"
	"jarvis_api/pkg/model"

	"gorm.io/gorm"
)

type chatHistoryContentManager struct {
	db *gorm.DB
}

func (c *chatHistoryContentManager) CreateChatHistoryContent(ctx context.Context, chatHistoryContent *model.ChatHistoryContentModel) error {
	return c.db.WithContext(ctx).Create(chatHistoryContent).Error
}

func (c *chatHistoryContentManager) ListChatHistoryContent(ctx context.Context, chatHistoryContentId int64) ([]model.ChatHistoryContentModel, error) {
	chatHistoryContents := []model.ChatHistoryContentModel{}
	err := c.db.WithContext(ctx).Where("chat_history_id = ?", chatHistoryContentId).Order("id asc").Find(&chatHistoryContents).Error
	if err != nil {
		return nil, err
	}
	return chatHistoryContents, nil
}
