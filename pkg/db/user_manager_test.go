package db

import (
	"context"
	"jarvis_api/pkg/model"
	"testing"

	"github.com/stretchr/testify/assert"
)

func Test_userManager_CreateUserBatch(t *testing.T) {
	assert.Nil(t, InitDBManager())
	err := UserManager.CreateUserBatch(context.Background(), []model.UserModel{{
		Id:          14592,
		CompanyId:   1,
		AcsId:       14592,
		Nid:         "12609",
		Username:    "dddd",
		RealName:    "xxx",
		Email:       "",
		DdUserId:    "",
		UpdatedTime: 0,
		CreatedTime: 0,
		DeleteTime:  0,
	}})
	assert.Nil(t, err)
}
