package db

import (
	"context"
	"jarvis_api/pkg/model"
	"time"

	"gorm.io/gorm"
)

type knowledgeManager struct {
	db *gorm.DB
}

func (k *knowledgeManager) GetKnowledge(ctx context.Context, id int64) (*model.KnowledgeModel, error) {
	knowledgeModel := &model.KnowledgeModel{}
	err := k.db.WithContext(ctx).Where("id = ? and delete_time = 0", id).First(&knowledgeModel).Error
	return knowledgeModel, err
}

func (k *knowledgeManager) DeleteKnowledge(ctx context.Context, id int64) error {
	return k.db.WithContext(ctx).Updates(&model.KnowledgeModel{Id: id, DeleteTime: time.Now().UnixMilli()}).Error
}

func (k *knowledgeManager) ListKnowledge(ctx context.Context, userId, dirId int64, isData bool) ([]model.KnowledgeModel, error) {
	knowledgeList := []model.KnowledgeModel{}
	err := k.db.WithContext(ctx).Where("create_user_id = ? and parent_id = ? and is_data = ? and delete_time = 0", userId, dirId, isData).Find(&knowledgeList).Error
	return knowledgeList, err
}

func (k *knowledgeManager) CreateKnowledge(ctx context.Context, obj *model.KnowledgeModel) error {
	return k.db.WithContext(ctx).Create(obj).Error
}

// UpdateKnowledge
func (k *knowledgeManager) UpdateKnowledge(ctx context.Context, obj *model.KnowledgeModel) error {
	return k.db.WithContext(ctx).Updates(obj).Error
}
