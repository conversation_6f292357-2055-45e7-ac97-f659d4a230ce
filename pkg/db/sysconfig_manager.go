package db

import (
	"context"
	"jarvis_api/pkg/model"

	"gorm.io/gorm"
)

type sysconfigManager struct {
	db *gorm.DB
}

// AddSysConfig sys config
func (m *sysconfigManager) AddSysConfig(ctx context.Context, sysConfig *model.Sysconfig) error {
	return m.db.Table("sys_config").Create(&sysConfig).Error
}

func (m *sysconfigManager) UpdateSysConfig(ctx context.Context, sysConfig model.Sysconfig) error {
	return m.db.Table("sys_config").
		Where("`key` = ? and company_id = ? ", sysConfig.Key, sysConfig.CompanyId).
		Update("value", sysConfig.Value).
		Error
}

func (m *sysconfigManager) FindSysConfig(sysConfig model.Sysconfig) (model.Sysconfig, error) {
	var sysConfigRes model.Sysconfig
	result := m.db.Table("sys_config").Where(sysConfig).First(&sysConfigRes)
	if result.Error == gorm.ErrRecordNotFound {
		return sysConfigRes, nil
	}
	return sysConfigRes, result.Error
}
