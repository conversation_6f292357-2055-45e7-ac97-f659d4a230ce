package db

import (
	"context"
	"jarvis_api/pkg/model"

	"gorm.io/gorm"
)

type toolManager struct {
	db *gorm.DB
}

func (t *toolManager) List(ctx context.Context, userId int64) ([]model.ToolModel, error) {
	var list []model.ToolModel
	tx := t.db.WithContext(ctx).Where("delete_time = 0").Where("create_user_id = ? or is_public = 1", userId).Find(&list)
	if tx.Error != nil {
		return nil, tx.Error
	}
	return list, nil
}

// Create
func (t *toolManager) Create(ctx context.Context, toolModel *model.ToolModel) error {
	tx := t.db.WithContext(ctx).Create(toolModel)
	if tx.Error != nil {
		return tx.Error
	}
	return nil
}

// Update
func (t *toolManager) Update(ctx context.Context, toolModel *model.ToolModel) error {
	tx := t.db.WithContext(ctx).Updates(toolModel)
	if tx.Error != nil {
		return tx.Error
	}
	return nil
}
