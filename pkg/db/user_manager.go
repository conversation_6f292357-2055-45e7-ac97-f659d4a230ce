package db

import (
	"context"
	"jarvis_api/pkg/model"
	"jarvis_api/tools"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

type userManager struct {
	db *gorm.DB
}

func (u *userManager) UpdateSecretKey(ctx context.Context, id int64, secretKey string) error {
	return u.db.WithContext(ctx).Model(&model.UserModel{}).Where("id = ?", id).Update("secret_key", secretKey).Error
}

func (u *userManager) GetUserByIdList(ctx context.Context, ids []int64) ([]model.UserModel, error) {
	var users []model.UserModel
	err := u.db.WithContext(ctx).Where("id in ?", ids).Find(&users).Error
	if err != nil {
		return nil, err
	}
	return users, nil
}

func (u *userManager) GetUserById(ctx context.Context, id int64) (*model.UserModel, error) {
	var user model.UserModel
	err := u.db.WithContext(ctx).Where("id = ?", id).First(&user).Error
	if err != nil {
		return nil, err
	}
	return &user, nil
}

func (u *userManager) GetUserByAcsId(ctx context.Context, acsId int64) (*model.UserModel, error) {
	var user model.UserModel
	err := u.db.WithContext(ctx).Where("acs_id = ?", acsId).First(&user).Error
	if err != nil {
		return nil, err
	}
	return &user, nil
}

func (u *userManager) Createuser(ctx context.Context, userModel *model.UserModel) error {
	return u.db.WithContext(ctx).Create(userModel).Error
}

func (u *userManager) SearchUser(ctx context.Context, key string) ([]model.UserModel, error) {
	users := []model.UserModel{}
	err := u.db.WithContext(ctx).Where("delete_time = 0").Where("real_name like ? or email like ?", "%"+key+"%", "%"+key+"%").Find(&users).Error
	if err != nil {
		return nil, err
	}
	return users, nil
}

func (u *userManager) ListUser(ctx context.Context) ([]model.UserModel, error) {
	var users []model.UserModel
	err := u.db.WithContext(ctx).Where("company_id = ? and delete_time = 0", tools.GetCompanyId(ctx)).Order("id desc").Find(&users).Error
	if err != nil {
		return nil, err
	}
	return users, nil
}

func (u *userManager) GetUserListByProjectId(ctx context.Context, projectId int64) ([]model.UserModel, error) {
	var users []model.UserModel

	sql := `SELECT distinct u.*
	FROM user u
			 JOIN (select f.create_user_id, f.dev_user_id, f.project_user_id
				   from feature f
				   where f.company_id = ?
					 and f.project_id = ?) fa
			 join (select t.create_user_id, t.assignee from task t where t.company_id = ? and t.project_id = ?) tb
				  on u.id in (tb.assignee) or u.id in (tb.create_user_id) or u.id IN (fa.create_user_id) or
					 u.id in (fa.dev_user_id) or u.id in (fa.project_user_id)
	where u.company_id = ?`
	companyId := tools.GetCompanyId(ctx)
	err := u.db.WithContext(ctx).Raw(sql, companyId, projectId, companyId, projectId, companyId).Find(&users).Error
	if err != nil {
		return nil, err
	}
	return users, nil
}

func (u *userManager) CreateUserBatch(ctx context.Context, userModels []model.UserModel) error {
	return u.db.Clauses(clause.OnConflict{
		Columns: []clause.Column{{Name: "id"}},
		DoUpdates: clause.AssignmentColumns([]string{
			"username",
			"real_name",
			"email",
			"dd_user_id",
			"updated_time",
		}),
	}).Create(&userModels).Error
}

func (p *userManager) GetUsersByProjectMember(ctx context.Context, projectId int64) ([]model.UserModel, error) {
	var usersModel []model.UserModel
	sql := `SELECT u.* from user u left join project_member pm on u.id = pm.member_user_id where pm.project_id = ?`
	err := p.db.WithContext(ctx).Raw(sql, projectId).Find(&usersModel).Error
	return usersModel, err
}

func (p *userManager) GetUserByDDUserId(ctx context.Context, ddUserID string) (model.UserModel, error) {
	var usersModel model.UserModel
	sql := `SELECT * from user where dd_user_id = ?`
	err := p.db.WithContext(ctx).Raw(sql, ddUserID).Find(&usersModel).Error
	return usersModel, err
}
