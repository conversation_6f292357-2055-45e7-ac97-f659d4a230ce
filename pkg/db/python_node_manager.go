package db

import (
	"context"
	"jarvis_api/pkg/model"
	"time"

	"gorm.io/gorm"
)

type pythonNodeManager struct {
	db *gorm.DB
}

func (p *pythonNodeManager) Get(ctx context.Context, id int64) (*model.PythonNodeModel, error) {
	var pythonNodeModel model.PythonNodeModel
	err := p.db.WithContext(ctx).Where("id = ? and delete_time = 0", id).First(&pythonNodeModel).Error
	if err != nil {
		return nil, err
	}
	return &pythonNodeModel, nil
}

func (p *pythonNodeManager) Create(ctx context.Context, pythonNodeModel *model.PythonNodeModel) error {
	return p.db.WithContext(ctx).Create(pythonNodeModel).Error
}

func (p *pythonNodeManager) ListPythonNode(ctx context.Context, userId, appId int64) ([]model.PythonNodeModel, error) {
	pythonNodeList := []model.PythonNodeModel{}

	tx := p.db.WithContext(ctx).Where("delete_time = 0")
	if appId > 0 {
		tx.Where("app_id = ?", appId)
	} else {
		tx.Where("create_user_id = ? or copyable = 1", userId)
	}
	err := tx.Order("created_time desc").Find(&pythonNodeList).Error
	if err != nil {
		return nil, err
	}
	return pythonNodeList, nil
}

func (p *pythonNodeManager) Update(ctx context.Context, pythonNodeModel *model.PythonNodeModel) error {
	return p.db.WithContext(ctx).Updates(pythonNodeModel).Error
}

func (p *pythonNodeManager) Delete(ctx context.Context, id int64) error {
	return p.db.WithContext(ctx).Model(&model.PythonNodeModel{}).Where("id = ?", id).Update("delete_time", time.Now().UnixMilli()).Error
}
