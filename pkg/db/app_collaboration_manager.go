package db

import (
	"context"
	"jarvis_api/pkg/model"
	"jarvis_api/tools"

	"gorm.io/gorm"
)

type appCollaborationManager struct {
	db *gorm.DB
}

// CleanCollaboration
func (a *appCollaborationManager) CleanCollaboration(ctx context.Context, appId int64) error {
	return a.db.WithContext(ctx).Where("app_id = ?", appId).Delete(&model.AppCollaborationModel{}).Error
}

// ResetCollaboration
func (a *appCollaborationManager) ResetCollaboration(ctx context.Context, appId int64, userId int64) error {
	return a.db.WithContext(ctx).Create(&model.AppCollaborationModel{Id: tools.GenId(), AppId: appId, UserId: userId}).Error
}

// GetCollaboration
func (a *appCollaborationManager) GetCollaboration(ctx context.Context, appId int64) ([]int64, error) {
	appCollaborationModels := []model.AppCollaborationModel{}
	err := a.db.WithContext(ctx).Table("app_collaboration").Where("app_id = ?", appId).Find(&appCollaborationModels).Error
	if err != nil {
		return nil, err
	}
	userIds := []int64{}
	for _, appCollaborationModel := range appCollaborationModels {
		userIds = append(userIds, appCollaborationModel.UserId)
	}
	return userIds, nil
}

func (a *appCollaborationManager) GetAppCollaboration(ctx context.Context, appId, userId int64) int {
	var count int64
	a.db.WithContext(ctx).Table("app_collaboration").Where("delete_time = 0").Where("app_id = ? and user_id = ?", appId, userId).Count(&count)
	return int(count)
}
