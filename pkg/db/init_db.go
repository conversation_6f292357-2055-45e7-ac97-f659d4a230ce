package db

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"jarvis_api/constant"
	"net/url"
	"os"
	"time"

	log "github.com/sirupsen/logrus"

	"github.com/aws/aws-sdk-go-v2/aws"
	"github.com/aws/aws-sdk-go-v2/config"
	"github.com/aws/aws-sdk-go-v2/credentials"
	"github.com/aws/aws-sdk-go-v2/service/secretsmanager"

	"github.com/gin-gonic/gin"

	"github.com/sirupsen/logrus"

	"gorm.io/driver/mysql"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"
	"gorm.io/gorm/utils"
)

var (
	ApplicationManager      *applicationManager
	AppCollaborationManager *appCollaborationManager
	TeamManager             *teamManager
	TaskManager             *taskManager
	AgentManager            *agentManager
	RunnerManager           *runnerManager
	ToolManager             *toolManager

	LlmManager                *llmManager
	UserManager               *userManager
	ChatHistoryManager        *chatHistoryManager
	LlmProvideAuthManager     *llmProvideAuthManager
	ChatHistoryContentManager *chatHistoryContentManager
	SysconfigManager          *sysconfigManager
	ConnectorManager          *connectorManager
	KnowledgeManager          *knowledgeManager
	PythonNodeManager         *pythonNodeManager
)

func InitDBManager() error {
	dbDSN, err := getMysqlDSN()
	if err != nil {
		return err
	}

	db, err := gorm.Open(mysql.Open(dbDSN), &gorm.Config{
		Logger: newJsonLogger(),
	})

	db = db.Debug()

	if err != nil {
		logrus.Error("connection db error", err)
		return err
	}

	ApplicationManager = &applicationManager{db: db}
	AppCollaborationManager = &appCollaborationManager{db: db}
	TeamManager = &teamManager{db: db}
	TaskManager = &taskManager{db: db}
	AgentManager = &agentManager{db: db}
	RunnerManager = &runnerManager{db: db}
	ToolManager = &toolManager{db: db}

	LlmManager = &llmManager{db: db}
	UserManager = &userManager{db: db}
	ChatHistoryManager = &chatHistoryManager{db: db}
	LlmProvideAuthManager = &llmProvideAuthManager{db: db}
	ChatHistoryContentManager = &chatHistoryContentManager{db: db}
	SysconfigManager = &sysconfigManager{db: db}
	ConnectorManager = &connectorManager{db: db}
	KnowledgeManager = &knowledgeManager{db: db}
	PythonNodeManager = &pythonNodeManager{db: db}
	return nil
}

func getMysqlDSN() (string, error) {
	// 线上库和测试库分开
	var dbDSN string

	if gin.Mode() == gin.ReleaseMode {
		var err error
		dbDSN, err = getMysqlConnection(true)
		if err != nil {
			return "", err
		}
	} else {
		var err error
		dbDSN, err = getMysqlConnection(false)
		if err != nil {
			return "", err
		}
	}
	return dbDSN, nil
}

// 获取mysql数据库的连接信息
func getMysqlConnection(production bool) (string, error) {
	type Mysql struct {
		UserName string `json:"username"`
		Password string `json:"password"`
		Host     string `json:"host"`
		Port     int    `json:"port"`
		DbName   string `json:"dbname"`
	}

	secretName := "spotmax-maxagent-rds-vg-admin"
	secretNameEnv := os.Getenv("SECRET_NAME")
	if secretNameEnv != "" {
		secretName = secretNameEnv
	}

	region := "us-east-1"
	regionEnv := os.Getenv("SECRET_REGION")
	if regionEnv != "" {
		region = regionEnv
	}

	access_key := os.Getenv("AWS_ACCESS_KEY_ID")
	secret_key := os.Getenv("AWS_SECRET_ACCESS_KEY")
	ctx := context.Background()

	sdkConfig, err := config.LoadDefaultConfig(ctx,
		config.WithRegion(region),
		config.WithCredentialsProvider(credentials.NewStaticCredentialsProvider(access_key, secret_key, "")),
	)
	if err != nil {
		log.Error(err)
		return "", err
	}
	svc := secretsmanager.NewFromConfig(sdkConfig)

	input := &secretsmanager.GetSecretValueInput{
		SecretId:     aws.String(secretName),
		VersionStage: aws.String("AWSCURRENT"),
	}

	result, err := svc.GetSecretValue(ctx, input)
	if err != nil {
		log.Error(err)
		return "", err
	}

	mysql := Mysql{}
	err = json.Unmarshal([]byte(*result.SecretString), &mysql)
	if err != nil {
		log.Error(err)
		return "", err
	}

	if production {
		mysql.DbName = "jarvis_prod"
	} else {
		mysql.DbName = "jarvis_test"
	}

	timezone := "'Asia/Shanghai'"
	dbConnection := fmt.Sprintf("%s:%s@tcp(%s:%d)/%s?charset=utf8mb4&collation=utf8mb4_unicode_ci&parseTime=true&loc=Local&time_zone=%s",
		mysql.UserName,
		mysql.Password,
		mysql.Host,
		mysql.Port,
		mysql.DbName,
		url.QueryEscape(timezone),
	)

	return dbConnection, nil
}

func newJsonLogger() logger.Interface {
	return &jsonLogger{
		Config: logger.Config{
			SlowThreshold: 200 * time.Millisecond,
			LogLevel:      logger.Warn,
			Colorful:      true,
		},
	}
}

// ----------------------------------
type jsonLogger struct {
	logger.Config
}

type Config struct {
	SlowThreshold             time.Duration
	Colorful                  bool
	IgnoreRecordNotFoundError bool
	LogLevel                  LogLevel
}

func (l *jsonLogger) getEntry() *logrus.Entry {
	return logrus.WithFields(logrus.Fields{
		"log_type":     "db_log",
		"release_mode": gin.Mode(),
	})
}

// LogMode log mode
func (l *jsonLogger) LogMode(level logger.LogLevel) logger.Interface {
	newlogger := *l
	newlogger.LogLevel = level
	return &newlogger
}

// Info print info
func (l jsonLogger) Info(ctx context.Context, msg string, data ...interface{}) {
	l.getEntry().Info(append([]interface{}{msg}, data...)...)
}

// Warn print warn messages
func (l jsonLogger) Warn(ctx context.Context, msg string, data ...interface{}) {
	l.getEntry().Warn(append([]interface{}{msg}, data...)...)
}

// Error print error messages
func (l jsonLogger) Error(ctx context.Context, msg string, data ...interface{}) {
	l.getEntry().Error(append([]interface{}{msg}, data...)...)
}

// Trace print sql message
func (l jsonLogger) Trace(ctx context.Context, begin time.Time, fc func() (string, int64), err error) {
	reqID := ctx.Value(constant.MiddlewareKeyRequestId)
	if reqID == interface{}(nil) {
		// not all the sql req is with req ID
		reqID = ""
	}

	if l.LogLevel <= logger.Silent {
		return
	}

	elapsed := time.Since(begin)
	sql, rows := fc()
	entry := l.getEntry().WithFields(logrus.Fields{
		"sql":        sql,
		"file":       utils.FileWithLineNum(),
		"elapsed":    float64(elapsed.Nanoseconds()) / 1e6,
		"request_id": reqID,
	})
	if rows != -1 {
		entry.WithField("rowcount", rows)
	}

	switch {
	case err != nil && l.LogLevel >= logger.Error && (!errors.Is(err, logger.ErrRecordNotFound) || !l.IgnoreRecordNotFoundError):
		entry.Error(err)
	case elapsed > l.SlowThreshold && l.SlowThreshold != 0 && l.LogLevel >= logger.Warn:
		slowLog := fmt.Sprintf("SLOW SQL >= %v", l.SlowThreshold)
		entry.Warn(slowLog)
	case l.LogLevel == logger.Info:
		entry.Trace("")
	}
}

// LogLevel
type LogLevel int

const (
	Silent LogLevel = iota + 1
	Error
	Warn
	Info
)
