package db

import (
	"context"
	"fmt"
	"jarvis_api/constant"
	"jarvis_api/pkg/model"
	"jarvis_api/pkg/vo"

	"gorm.io/gorm"
)

type runnerManager struct {
	db *gorm.DB
}

func (t *runnerManager) Create(ctx context.Context, obj *model.RunnerModel) error {
	return t.db.WithContext(ctx).Create(obj).Error
}

func (t *runnerManager) Get(ctx context.Context, runnerId int64) (*model.RunnerModel, error) {
	var obj model.RunnerModel
	tx := t.db.WithContext(ctx).
		Where("delete_time = 0").
		Where("id = ?", runnerId).First(&obj)
	if tx.Error != nil {
		return nil, tx.Error
	}
	return &obj, nil
}

func (t *runnerManager) List(ctx context.Context, runnerType constant.RunnerType, userId, runnerTargetId int64) ([]model.RunnerModel, error) {
	var list []model.RunnerModel
	tx := t.db.WithContext(ctx).Select("id,runner_type,app_id,agent_team_id,data_process_id,connector_id,create_user_id,trigger_time,trigger_mode,trigger_params,execute_status,execute_result,execute_log,remark,updated_time,created_time,delete_time").
		Where("delete_time = 0").
		Where("create_user_id = ?", userId).
		Where("runner_type = ?", runnerType)
	switch runnerType {
	case constant.RunnerTypeApp:
		tx.Where("app_id = ?", runnerTargetId)
	case constant.RunnerTypeAgentTeam:
		tx.Where("agent_team_id = ?", runnerTargetId)
	case constant.RunnerTypeDataProcess:
		tx.Where("data_process_id = ?", runnerTargetId)
	case constant.RunnerTypeConnector:
		tx.Where("connector_id = ?", runnerTargetId)
	case constant.RunnerTypePython:
		tx.Where("python_node_id = ?", runnerTargetId)
	default:
		return nil, fmt.Errorf("runner_type is not supported: %s", runnerType)
	}

	err := tx.Order("created_time desc").Find(&list).Error
	if err != nil {
		return nil, err
	}
	return list, nil
}
func (t *runnerManager) ListRunnerV2(ctx context.Context, userId int64, listRunnerReq *vo.ListRunnerReq) ([]model.RunnerModel, error) {
	var list []model.RunnerModel
	tx := t.db.WithContext(ctx).Select("id,runner_type,app_id,agent_team_id,data_process_id,connector_id,create_user_id,trigger_time,trigger_mode,trigger_params,execute_status,execute_result,execute_log,remark,updated_time,created_time,delete_time").
		Where("delete_time = 0 and primary_id = 0").
		// Where("create_user_id = ?", userId).
		Where("runner_type = ?", listRunnerReq.RunnerType)
	switch listRunnerReq.RunnerType {
	case constant.RunnerTypeApp:
		tx.Where("app_id = ?", listRunnerReq.RunnerTargetId)
	case constant.RunnerTypeAgentTeam:
		tx.Where("agent_team_id = ?", listRunnerReq.RunnerTargetId)
	case constant.RunnerTypeDataProcess:
		tx.Where("data_process_id = ?", listRunnerReq.RunnerTargetId)
	case constant.RunnerTypeConnector:
		tx.Where("connector_id = ?", listRunnerReq.RunnerTargetId)
	case constant.RunnerTypePython:
		tx.Where("python_node_id = ?", listRunnerReq.RunnerTargetId)
	default:
		return nil, fmt.Errorf("runner_type is not supported: %s", listRunnerReq.RunnerType)
	}
	if listRunnerReq.RunnerRecordId > 0 {
		tx.Where("id = ?", listRunnerReq.RunnerRecordId)
	}
	if listRunnerReq.ExecuteStatus != "" {
		tx.Where("execute_status = ?", listRunnerReq.ExecuteStatus)
	}
	if listRunnerReq.StartTime > 0 {
		tx.Where("trigger_time >= ?", listRunnerReq.StartTime)
	}
	if listRunnerReq.EndTime > 0 {
		tx.Where("trigger_time <= ?", listRunnerReq.EndTime)
	}
	if listRunnerReq.LastId > 0 {
		tx.Where("id < ?", listRunnerReq.LastId)
	}
	tx.Limit(listRunnerReq.PageSize)

	err := tx.Order("id desc").Find(&list).Error
	if err != nil {
		return nil, err
	}
	return list, nil
}
