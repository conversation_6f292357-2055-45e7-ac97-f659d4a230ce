package db

import (
	"context"
	"jarvis_api/pkg/model"
	"time"

	"gorm.io/gorm"
)

type agent<PERSON>anager struct {
	db *gorm.DB
}

func (t *agentManager) Create(ctx context.Context, obj *model.AgentModel) error {
	return t.db.WithContext(ctx).Create(obj).Error
}

func (t *agentManager) Get(ctx context.Context, agentId int64) (*model.AgentModel, error) {
	var obj model.AgentModel
	tx := t.db.WithContext(ctx).Where("id = ? and delete_time = 0", agentId).First(&obj)
	if tx.Error != nil {
		return nil, tx.Error
	}
	return &obj, nil
}

func (t *agentManager) List(ctx context.Context, userId, teamId int64) ([]model.AgentModel, error) {
	var list []model.AgentModel
	tx := t.db.WithContext(ctx).
		Where("delete_time = 0")
	if teamId > 0 {
		tx.Where("agent_team_id = ?", teamId)
	} else {
		tx.Where("create_user_id = ? or copyable = 1", userId)
	}
	err := tx.Order("created_time desc").Find(&list).Error
	if err != nil {
		return nil, err
	}
	return list, nil
}

func (t *agentManager) ListForce(ctx context.Context, teamId int64) ([]model.AgentModel, error) {
	var list []model.AgentModel
	tx := t.db.WithContext(ctx).
		Where("delete_time = 0")
	//Where("create_user_id = ? or copyable = 1", userId)
	if teamId > 0 {
		tx.Where("agent_team_id = ?", teamId)
	}
	err := tx.Order("created_time desc").Find(&list).Error
	if err != nil {
		return nil, err
	}
	return list, nil
}

func (t *agentManager) PickupOtherTasks(ctx context.Context, userId int64, ids []int64) ([]int64, error) {
	var otherIds []int64
	tx := t.db.WithContext(ctx).Model(&model.AgentModel{}).Select("id").
		Where("delete_time = 0").
		Where("create_user_id != ?", userId).
		Where("id IN ?", ids)
	err := tx.Find(&otherIds).Error
	if err != nil {
		return nil, err
	}
	return otherIds, nil
}

func (t *agentManager) Update(ctx context.Context, obj *model.AgentModel) error {
	return t.db.WithContext(ctx).Updates(obj).Error
}

func (t *agentManager) Delete(ctx context.Context, ids []int64) error {
	return t.db.WithContext(ctx).Model(&model.AgentModel{}).
		Where("id IN ?", ids).Update("delete_time", time.Now().UnixMilli()).Error
	//return app.db.WithContext(ctx).Where("id = ?", appId).Delete(&model.ApplicationModel{}).Error
}
