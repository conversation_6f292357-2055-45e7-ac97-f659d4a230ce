package db

import (
	"context"
	"jarvis_api/pkg/model"

	"gorm.io/gorm"
)

type llmProvideAuthManager struct {
	db *gorm.DB
}

func (m *llmProvideAuthManager) CreateLlmProvideAuth(ctx context.Context, llmProvideAuth *model.LlmProvideAuthModel) error {
	return m.db.WithContext(ctx).Create(llmProvideAuth).Error
}

func (m *llmProvideAuthManager) GetLlmProvideAuth(ctx context.Context, llmProvideAuthId int64) (*model.LlmProvideAuthModel, error) {
	var llmProvideAuth model.LlmProvideAuthModel
	err := m.db.WithContext(ctx).Where("id = ?", llmProvideAuthId).First(&llmProvideAuth).Error
	if err != nil {
		return nil, err
	}
	return &llmProvideAuth, nil
}
