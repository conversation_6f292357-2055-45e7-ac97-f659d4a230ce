package db

import (
	"context"
	"jarvis_api/pkg/model"

	"gorm.io/gorm"
)

type llmManager struct {
	db *gorm.DB
}

func (l *llmManager) ListLlms(ctx context.Context) ([]model.LlmModel, error) {
	var llms []model.LlmModel
	err := l.db.WithContext(ctx).Where("delete_time = 0").Find(&llms).Error
	if err != nil {
		return nil, err
	}
	return llms, nil
}

func (l *llmManager) CreateLlm(ctx context.Context, llm *model.LlmModel) error {
	return l.db.WithContext(ctx).Create(llm).Error
}

func (l *llmManager) GetLlm(ctx context.Context, llmId int64) (*model.LlmModel, error) {
	var llmModel model.LlmModel
	err := l.db.WithContext(ctx).Where("id = ? and delete_time = 0", llmId).First(&llmModel).Error
	if err != nil {
		return nil, err
	}
	return &llmModel, nil
}
