package db

import (
	"context"
	"jarvis_api/pkg/model"
	"time"

	"gorm.io/gorm"
)

type applicationManager struct {
	db *gorm.DB
}

func (app *applicationManager) CreateApplication(ctx context.Context, application *model.ApplicationModel) error {
	return app.db.WithContext(ctx).Create(application).Error
}

func (app *applicationManager) GetApplication(ctx context.Context, appId int64) (*model.ApplicationModel, error) {
	var application model.ApplicationModel
	tx := app.db.WithContext(ctx).Where("id = ? and delete_time = 0", appId).First(&application)
	if tx.Error != nil {
		return nil, tx.Error
	}
	return &application, nil
}

func (app *applicationManager) GetApplicationList(ctx context.Context, userId int64) ([]model.ApplicationModel, error) {
	var applicationList []model.ApplicationModel
	err := app.db.WithContext(ctx).Select("id,name,remark,create_user_id,copyable,sort,updated_time,created_time,delete_time").
		Where("(create_user_id = ? or copyable = 1) and delete_time = 0", userId).Order("created_time desc").Find(&applicationList).Error
	if err != nil {
		return nil, err
	}
	return applicationList, nil
}

func (t *applicationManager) PickupOtherTasks(ctx context.Context, userId int64, ids []int64) ([]int64, error) {
	var otherIds []int64
	tx := t.db.WithContext(ctx).Model(&model.ApplicationModel{}).Select("id").
		Where("delete_time = 0").
		Where("create_user_id != ?", userId).
		Where("id IN ?", ids)
	err := tx.Find(&otherIds).Error
	if err != nil {
		return nil, err
	}
	return otherIds, nil
}

func (app *applicationManager) UpdateApplication(ctx context.Context, applicationModel *model.ApplicationModel) error {
	return app.db.WithContext(ctx).Updates(applicationModel).Error
}

func (app *applicationManager) DeleteApplication(ctx context.Context, appId int64) error {
	return app.db.WithContext(ctx).Model(&model.ApplicationModel{}).Where("id = ?", appId).Update("delete_time", time.Now().UnixMilli()).Error
	//return app.db.WithContext(ctx).Where("id = ?", appId).Delete(&model.ApplicationModel{}).Error
}

// GetMyCollaborationApp
func (app *applicationManager) GetMyCollaborationApp(ctx context.Context, userId int64) ([]model.ApplicationModel, error) {
	var applicationList []model.ApplicationModel
	err := app.db.WithContext(ctx).Model(&model.ApplicationModel{}).Select("distinct app.id,app.name,app.remark,app.create_user_id,app.copyable,app.sort,app.updated_time,app.created_time,app.delete_time").
		Joins("join app_collaboration ac on app.id = ac.app_id").
		Where("ac.user_id = ? and app.delete_time = 0", userId).Order("app.created_time desc").Find(&applicationList).Error
	if err != nil {
		return nil, err
	}
	return applicationList, nil
}
