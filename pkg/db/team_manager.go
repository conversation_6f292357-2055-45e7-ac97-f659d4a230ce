package db

import (
	"context"
	"gorm.io/gorm"
	"jarvis_api/pkg/model"
	"time"
)

type teamManager struct {
	db *gorm.DB
}

func (team *teamManager) Create(ctx context.Context, obj *model.TeamModel) error {
	return team.db.WithContext(ctx).Create(obj).Error
}

func (team *teamManager) Get(ctx context.Context, teamId int64) (*model.TeamModel, error) {
	var obj model.TeamModel
	tx := team.db.WithContext(ctx).Where("id = ? and delete_time = 0", teamId).First(&obj)
	if tx.Error != nil {
		return nil, tx.Error
	}
	return &obj, nil
}

func (team *teamManager) List(ctx context.Context, userId, appId int64) ([]model.TeamModel, error) {
	var list []model.TeamModel
	tx := team.db.WithContext(ctx).Model(&model.TeamModel{}).
		Where("delete_time = 0").
		Where("create_user_id = ? or copyable = 1", userId)
	if appId > 0 {
		tx.Where("app_id = ?", appId)
	}
	err := tx.Order("created_time desc").Find(&list).Error
	if err != nil {
		return nil, err
	}
	return list, nil
}

func (team *teamManager) ListForce(ctx context.Context, appId int64) ([]model.TeamModel, error) {
	var list []model.TeamModel
	tx := team.db.WithContext(ctx).Model(&model.TeamModel{}).
		Where("delete_time = 0")
	//Where("create_user_id = ? or copyable = 1", userId)
	if appId > 0 {
		tx.Where("app_id = ?", appId)
	}
	err := tx.Order("created_time desc").Find(&list).Error
	if err != nil {
		return nil, err
	}
	return list, nil
}

func (t *teamManager) PickupOtherTasks(ctx context.Context, userId int64, ids []int64) ([]int64, error) {
	var otherIds []int64
	tx := t.db.WithContext(ctx).Model(&model.TeamModel{}).Select("id").
		Where("delete_time = 0").
		Where("create_user_id != ?", userId).
		Where("id IN ?", ids)
	err := tx.Find(&otherIds).Error
	if err != nil {
		return nil, err
	}
	return otherIds, nil
}

func (team *teamManager) Update(ctx context.Context, obj *model.TeamModel) error {
	return team.db.WithContext(ctx).Updates(obj).Error
}

func (team *teamManager) Delete(ctx context.Context, teamId int64) error {
	return team.db.WithContext(ctx).Model(&model.TeamModel{}).Where("id = ?", teamId).Update("delete_time", time.Now().UnixMilli()).Error
	//return app.db.WithContext(ctx).Where("id = ?", appId).Delete(&model.ApplicationModel{}).Error
}
