package db

import (
	"context"
	"math"
	"jarvis_api/pkg/model"
	"jarvis_api/pkg/vo"
	"time"

	"gorm.io/gorm"
)

type chatHistoryManager struct {
	db *gorm.DB
}

func (c *chatHistoryManager) GetChatHistoryById(ctx context.Context, chatHistoryId int64) (*model.ChatHistoryModel, error) {
	chatHistory := model.ChatHistoryModel{}
	err := c.db.WithContext(ctx).Where("id = ?", chatHistoryId).First(&chatHistory).Error
	if err != nil {
		return nil, err
	}
	return &chatHistory, nil
}
func (c *chatHistoryManager) ListUserAppChatHistory(ctx context.Context, userId int64,
	listChatHistoryReq *vo.ListChatHistoryReq, isBaseApp bool) ([]model.ChatHistoryModel, error) {

	chatHistoryList := []model.ChatHistoryModel{}
	lastId := int64(math.MaxInt64)
	if listChatHistoryReq.LastId > 0 {
		lastId = listChatHistoryReq.LastId
	}

	db := c.db.WithContext(ctx).Where("user_id = ? and app_id != 1815947774144315392 and llm_id = 0 and delete_time = 0 and id < ?", userId, lastId)
	if isBaseApp {
		db = db.Where("base_app = 1")
	} else if listChatHistoryReq.AppId > 0 {
		db = db.Where("app_id = ?", listChatHistoryReq.AppId)
	}
	pageSize := 10
	if listChatHistoryReq.PageSize > 0 {
		pageSize = listChatHistoryReq.PageSize
	}

	err := db.Order("id desc").Limit(pageSize).Find(&chatHistoryList).Error
	if err != nil {
		return nil, err
	}

	return chatHistoryList, nil
}

func (c *chatHistoryManager) CreateChatHistory(ctx context.Context, chatHistory *model.ChatHistoryModel) error {
	err := c.db.WithContext(ctx).Create(chatHistory).Error
	if err != nil {
		return err
	}
	return nil
}

func (c *chatHistoryManager) DeleteChatHistory(ctx context.Context, deleteChatHistoryReq *vo.DeleteChatHistoryReq) error {
	return c.db.WithContext(ctx).Model(&model.ChatHistoryModel{}).Where("id = ?", deleteChatHistoryReq.ChatHistoryId).Update("delete_time", time.Now().UnixMilli()).Error
}

func (c *chatHistoryManager) RenameChatHistory(ctx context.Context, renameChatHistoryReq *vo.RenameChatHistoryReq) error {
	return c.db.WithContext(ctx).Model(&model.ChatHistoryModel{}).Where("id = ?", renameChatHistoryReq.ChatHistoryId).Update("name", renameChatHistoryReq.Name).Error
}
