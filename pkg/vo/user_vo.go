package vo

type UserRes struct {
	Id          int64  `json:"id,string"`
	CompanyId   int64  `json:"company_id,string"`
	AcsId       int64  `json:"acs_id,string"`
	Nid         string `json:"nid"`
	Username    string `json:"username"`
	RealName    string `json:"real_name"`
	Pinyin      string `json:"pinyin"`
	Email       string `json:"email"`
	DdUserId    string `json:"dd_user_id"`
	UpdatedTime int64  `json:"updated_time" gorm:"autoUpdateTime:milli"` // 使用时间戳毫秒数填充更新时间
	CreatedTime int64  `json:"created_time" gorm:"autoCreateTime"`       // 使用时间戳秒数填充创建时间
	DeleteTime  int64  `json:"delete_time" `                             // 使用时间戳秒数填充创建时间
}

type ApplyForUse struct {
	Reason string `json:"reason"`
}

type UserSimpleInfo struct {
	Id       int64  `json:"id,string"`
	RealName string `json:"real_name"`
	Email    string `json:"email"`
}
