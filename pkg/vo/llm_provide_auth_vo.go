package vo

import "jarvis_api/constant"

type CreateLlmProvideAuthReq struct {
	ServiceProvider          constant.ServiceProvider `json:"service_provider"`
	Description              string                   `json:"description"`
	AzureLlmProvideAuth      AzureLlmProvideAuth      `json:"azure_llm_provide_auth"`
	DeepseekLlmProvideAuth   DeepseekLlmProvideAuth   `json:"deepseek_llm_provide_auth"`
	AwsBedrockLlmProvideAuth AwsBedrockLlmProvideAuth `json:"aws_bedrock_llm_provide_auth"`
	GcpLlmProvideAuth        GcpLlmProvideAuth        `json:"gcp_llm_provide_auth"`
}

type AzureLlmProvideAuth struct {
	BaseUrl    string `json:"base_url"`
	Token      string `json:"token"`
	Model      string `json:"model"`
	ApiType    string `json:"api_type"`
	ApiVersion string `json:"api_version"`
}

type DeepseekLlmProvideAuth struct {
	BaseUrl string `json:"base_url"`
	Token   string `json:"token"`
}

type AwsBedrockLlmProvideAuth struct {
	AwsAccessKeyId     string `json:"aws_access_key_id"`
	AwsSecretAccessKey string `json:"aws_secret_access_key"`
	AwsRegion          string `json:"aws_region"`
}

type GcpLlmProvideAuth struct {
	GcpLocation     string `json:"gcp_location"`
	Project         string `json:"project"`
	CredentialsJSON string `json:"credentials_json"`
}
