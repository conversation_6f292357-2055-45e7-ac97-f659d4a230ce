package vo

import (
	"context"

	validation "github.com/go-ozzo/ozzo-validation/v4"
)

var _ Validation = CreatePythonNodeReq{}

type CreatePythonNodeReq struct {
	Name       string      `json:"name"` // 应用名称
	Remark     string      `json:"remark"`
	Copyable   bool        `json:"copyable"` // 是否允许copy
	AppId      int64       `json:"app_id,string"`
	Code       string      `json:"code"`
	ResultVar  string      `json:"result_var"`
	PathTagVar string      `json:"path_tag_var"`
	MacroList  []TaskMacro `json:"macro_list"`
	Routers    []Router    `json:"routers"`
}

// Validation implements Validation.
func (c CreatePythonNodeReq) Validation(ctx context.Context) error {
	err := validation.ValidateStruct(&c,
		validation.Field(&c.Name, validation.Required, validation.Length(1, 100)),
		validation.Field(&c.AppId, validation.Required),
		validation.Field(&c.Code, validation.Required),
	)
	return err
}

type PythonNodeRes struct {
	Id int64 `json:"id,string"` // 应用名称
	CreatePythonNodeReq
	CreateUserId int64 `json:"create_user_id,string"` // 创建者ID
	UpdatedTime  int64 `json:"updated_time"`          // 修改时间
	CreatedTime  int64 `json:"created_time"`          // 创建时间
	DeleteTime   int64 `json:"delete_time"`           // 删除时间
}

type UpdatePythonNodeReq struct {
	Id         int64        `json:"id,string"`
	Name       *string      `json:"name"` // 应用名称
	Remark     *string      `json:"remark"`
	Copyable   *bool        `json:"copyable"` // 是否允许copy
	AppId      *int64       `json:"app_id,string"`
	Code       *string      `json:"code"`
	ResultVar  *string      `json:"result_var"`
	PathTagVar *string      `json:"path_tag_var"`
	MacroList  *[]TaskMacro `json:"macro_list"`
	Routers    *[]Router    `json:"routers"`
}

// Validation implements Validation.
func (u UpdatePythonNodeReq) Validation(ctx context.Context) error {
	return validation.ValidateStruct(&u,
		validation.Field(&u.Id, validation.Required),
		//validation.Field(&u.UpdateFields, validation.Required),
	)
}
