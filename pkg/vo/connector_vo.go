package vo

import (
	"context"
	"jarvis_api/constant"

	validation "github.com/go-ozzo/ozzo-validation/v4"
	"github.com/go-ozzo/ozzo-validation/v4/is"
)

var _ Validation = CreateConnectorReq{}

type CreateConnectorReq struct {
	Name            string      `json:"name"` // 应用名称
	RestfulUrl      string      `json:"restful_url"`
	Remark          string      `json:"remark"`
	Copyable        bool        `json:"copyable"` // 是否允许copy
	AppId           int64       `json:"app_id,string"`
	ResultParamsKey string      `json:"result_params_key"`
	MacroList       []TaskMacro `json:"macro_list"` // description内容中引用的宏列表
	Routers         []Router    `json:"routers"`
}

type Router struct {
	PathTag  string   `json:"path_tag"`
	NextNode NextNode `json:"next_node"`
}

type NextNode struct {
	Id           int64                 `json:"id,string"`
	NextNodeType constant.NextNodeType `json:"next_node_type"`
}

// Validation implements Validation.
func (c CreateConnectorReq) Validation(ctx context.Context) error {
	return validation.ValidateStruct(&c,
		validation.Field(&c.Name, validation.Required, validation.Length(1, 100)),
		validation.Field(&c.AppId, validation.Required),
		validation.Field(&c.RestfulUrl, validation.Required, is.URL),
	)
}

type ConnectorRes struct {
	Id              int64       `json:"id,string"`
	Name            string      `json:"name"` // 应用名称
	RestfulUrl      string      `json:"restful_url"`
	Remark          string      `json:"remark"`
	Copyable        bool        `json:"copyable"` // 是否允许copy
	AppId           int64       `json:"app_id,string"`
	ResultParamsKey string      `json:"result_params_key"`
	MacroList       []TaskMacro `json:"macro_list"` // description内容中引用的宏列表
	CreateUserId    int64       `json:"create_user_id"`
	Routers         []Router    `json:"routers"`
}

var _ Validation = UpdateConnectorReq{}

type UpdateConnectorReq struct {
	Name            string      `json:"name"` // 应用名称
	RestfulUrl      string      `json:"restful_url"`
	Remark          string      `json:"remark"`
	Copyable        bool        `json:"copyable"` // 是否允许copy
	AppId           int64       `json:"app_id,string"`
	ResultParamsKey string      `json:"result_params_key"`
	MacroList       []TaskMacro `json:"macro_list"` // description内容中引用的宏列表
	Routers         []Router    `json:"routers"`
}

// Validation implements Validation.
func (c UpdateConnectorReq) Validation(ctx context.Context) error {
	return validation.ValidateStruct(&c,
		validation.Field(&c.Name, validation.Required, validation.Length(1, 100)),
		validation.Field(&c.AppId, validation.Required),
		validation.Field(&c.RestfulUrl, validation.Required, is.URL),
	)
}
