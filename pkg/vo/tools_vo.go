package vo

import (
	"context"

	validation "github.com/go-ozzo/ozzo-validation/v4"
	"github.com/go-ozzo/ozzo-validation/v4/is"
)

var _ Validation = CreateToolReq{}

type CreateToolReq struct {
	Name      string `json:"name"`
	Remark    string `json:"remark"`
	IsPublic  bool   `json:"is_public"`
	ToolType  string `json:"tool_type"`
	Url       string `json:"url"`
	Transport string `json:"transport"`
}

// Validation implements Validation.
func (c CreateToolReq) Validation(ctx context.Context) error {
	return validation.ValidateStruct(&c,
		validation.Field(&c.Name, validation.Required, validation.Length(1, 100)),
		validation.Field(&c.ToolType, validation.Required, validation.In("mcp")),
		validation.Field(&c.Url, validation.Required, validation.Length(1, 500), is.URL),
		validation.Field(&c.Transport, validation.Required, validation.In("streamable-http", "sse")),
	)
}

type ToolRes struct {
	Id           int64  `json:"id,string"` // 应用名称
	Name         string `json:"name"`
	ToolKey      string `json:"-"`
	Remark       string `json:"remark"`
	CreateUserId int64  `json:"create_user_id,string"` // 创建者ID
	IsPublic     bool   `json:"is_public"`
	ToolType     string `json:"tool_type"`
	Url          string `json:"url"`
	Transport    string `json:"transport"`
}

var _ Validation = UpdateToolReq{}

type UpdateToolReq struct {
	Id        int64   `json:"id,string"`
	Name      *string `json:"name"`
	Remark    *string `json:"remark"`
	IsPublic  *bool   `json:"is_public"`
	ToolType  *string `json:"tool_type"`
	Url       *string `json:"url"`
	Transport *string `json:"transport"`
}

func (u UpdateToolReq) Validation(ctx context.Context) error {
	return validation.ValidateStruct(&u,
		validation.Field(&u.Id, validation.Required),
		validation.Field(&u.Name, validation.When(u.Name != nil, validation.Required, validation.Length(1, 100))),
		validation.Field(&u.ToolType, validation.When(u.ToolType != nil, validation.Required, validation.In("mcp"))),
		validation.Field(&u.Url, validation.When(u.Url != nil, validation.Required, validation.Length(1, 500), is.URL)),
	)
}
