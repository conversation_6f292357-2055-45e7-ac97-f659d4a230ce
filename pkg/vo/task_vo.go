package vo

import (
	"context"

	validation "github.com/go-ozzo/ozzo-validation/v4"
)

var _ Validation = CreateTaskReq{}

type CreateTaskReq struct {
	Name           string      `json:"name"` // 应用名称
	Remark         string      `json:"remark"`
	Copyable       bool        `json:"copyable"` // 是否允许copy
	AppId          int64       `json:"app_id,string"`
	AgentTeamId    int64       `json:"agent_team_id,string"`
	AgentId        int64       `json:"agent_id,string"`
	AsyncExecution bool        `json:"async_execution"` // 是否异步执行
	DependentTasks []string    `json:"dependent_tasks"` // 依赖的task列表
	Description    string      `json:"description"`
	ExpectedOutput string      `json:"expected_output"` // 期望输出结果(crewai)
	OutputFile     string      `json:"output_file"`     // 结果存储到文件，指定文件路径(crewai)
	ExtInfo        string      `json:"ext_info"`        // 扩展信息 使用json存储
	MacroList      []TaskMacro `json:"macro_list"`      // description内容中引用的宏列表
	MemorySpace    string      `json:"memory_space"`    // 记忆空间
	MemoryCategory string      `json:"memory_category"` // 记忆类型
	ToolIds        string      `json:"tool_ids"`        // json
	OutputStruct   string      `json:"output_struct"`   // task 结果的结构体名称
}

// Validation implements Validation.
func (c CreateTaskReq) Validation(ctx context.Context) error {
	return validation.ValidateStruct(&c,
		//validation.Field(&c.Icon, validation.Required),
		validation.Field(&c.Name, validation.Required, validation.Length(1, 100)),
		//validation.Field(&c.Type, validation.Required),
		//validation.Field(&c.LlmId, validation.Required),
		//validation.Field(&c.SystemPrompy, validation.Required, validation.Length(1, 50000)),
		validation.Field(&c.MemorySpace, validation.When(c.MemoryCategory != "", validation.Required)),
		validation.Field(&c.MemoryCategory, validation.When(c.MemorySpace != "", validation.Required)),
	)
}

type TaskRes struct {
	Id int64 `json:"id,string"` // 应用名称
	CreateTaskReq
	CreateUserId int64 `json:"create_user_id,string"` // 创建者ID
	UpdatedTime  int64 `json:"updated_time"`          // 修改时间
	CreatedTime  int64 `json:"created_time"`          // 创建时间
	DeleteTime   int64 `json:"delete_time"`           // 删除时间
}

type UpdateTaskReq struct {
	Id       int64   `json:"id,string"`
	Name     *string `json:"name"` // 应用名称
	Remark   *string `json:"remark"`
	Copyable *bool   `json:"copyable"` // 是否允许copy

	AppId       *int64 `json:"app_id,string"`
	AgentTeamId *int64 `json:"agent_team_id,string"`
	AgentId     *int64 `json:"agent_id,string"`

	Description    *string      `json:"description"`
	ExpectedOutput *string      `json:"expected_output"` // 期望输出结果(crewai)
	OutputFile     *string      `json:"output_file"`     // 结果存储到文件，指定文件路径(crewai)
	ExtInfo        *string      `json:"ext_info"`        // 扩展信息 使用json存储
	MacroList      *[]TaskMacro `json:"macro_list"`      // description内容中引用的宏列表
	MemorySpace    *string      `json:"memory_space"`    // 记忆空间
	MemoryCategory *string      `json:"memory_category"` // 记忆类型
	ToolIds        *string      `json:"tool_ids"`        // json
	AsyncExecution *bool        `json:"async_execution"` // 是否异步执行
	DependentTasks *[]string    `json:"dependent_tasks"` // 依赖的task列表
	OutputStruct   *string      `json:"output_struct"`   // task 结果的结构体名称
}

// Validation implements Validation.
func (u UpdateTaskReq) Validation(ctx context.Context) error {
	return validation.ValidateStruct(&u,
		validation.Field(&u.Id, validation.Required),
		//validation.Field(&u.UpdateFields, validation.Required),
	)
}

var _ Validation = UpdateTaskReq{}

type DeleteTaskReq struct {
	Ids []int64 `json:"ids,string"`
}

// Validation implements Validation.
func (d DeleteTaskReq) Validation(ctx context.Context) error {
	return validation.ValidateStruct(&d,
		validation.Field(&d.Ids, validation.Required),
		//validation.Field(&u.UpdateFields, validation.Required),
	)
}

type CopyTaskReq struct {
	Id int64 `json:"id,string"`

	Name        string `json:"name"`
	AppId       int64  `json:"app_id,string"`
	AgentTeamId int64  `json:"agent_team_id,string"`

	ForceCopy bool `json:"force_copy"`
}

// Validation implements Validation.
func (u CopyTaskReq) Validation(ctx context.Context) error {
	return validation.ValidateStruct(&u,
		//validation.Field(&u.Id, validation.Required),
		validation.Field(&u.AppId, validation.Required),
		validation.Field(&u.AgentTeamId, validation.Required),
	)
}
