package vo

import (
	"context"
	"jarvis_api/constant"

	validation "github.com/go-ozzo/ozzo-validation/v4"
)

var _ Validation = CreateRunnerReq{}

type CreateRunnerReq struct {
	Remark        string              `json:"remark"`
	RunnerType    constant.RunnerType `json:"runner_type"`
	AppId         int64               `json:"app_id,string"`
	AgentTeamId   int64               `json:"agent_team_id,string"`
	DataProcessId int64               `json:"data_process_id,string"`
	ConnectorId   int64               `json:"connector_id,string"`
	PythonNodeId  int64               `json:"python_node_id,string"`
	TriggerParams string              `json:"trigger_params"` // json
	IsMultiTurn   bool                `json:"is_multi_turn"`
	PrimaryId     int64               `json:"primary_id,string"`
	AllowTeamId   int64               `json:"allow_team_id,string"`
	AllowTasks    []string            `json:"allow_tasks"`
}

type RemoteTriggerReq struct {
	Remark        string `json:"remark"`
	AppId         int64  `json:"app_id,string"`
	TriggerParams string `json:"trigger_params"` // json
}
type RemoteTriggerSyncReq struct {
	Remark        string `json:"remark"`
	AppId         int64  `json:"app_id,string"`
	TriggerParams string `json:"trigger_params"` // json
	TimeOutSecond int    `json:"time_out_second"`
	Coding        string `json:"coding"`
}

// Validation implements Validation.
func (c CreateRunnerReq) Validation(ctx context.Context) error {
	return validation.ValidateStruct(&c,
		//validation.Field(&c.Icon, validation.Required),
		validation.Field(&c.Remark, validation.Required, validation.Length(1, 100)),
		validation.Field(&c.RunnerType,
			validation.In(constant.RunnerTypeApp, constant.RunnerTypeAgentTeam, constant.RunnerTypeConnector, constant.RunnerTypeDataProcess, constant.RunnerTypePython)),
		validation.Field(&c.AppId, validation.Required),
		validation.Field(&c.AgentTeamId, validation.When(c.RunnerType == constant.RunnerTypeAgentTeam, validation.Required)),
		validation.Field(&c.DataProcessId, validation.When(c.RunnerType == constant.RunnerTypeDataProcess, validation.Required)),
		validation.Field(&c.ConnectorId, validation.When(c.RunnerType == constant.RunnerTypeConnector, validation.Required)),
		validation.Field(&c.PythonNodeId, validation.When(c.RunnerType == constant.RunnerTypePython, validation.Required)),
		//validation.Field(&c.TriggerMode,
		//	validation.In(constant.TriggerModeWeb, constant.TriggerModeTimer, constant.TriggerModeApi)),
		//validation.Field(&c.Type, validation.Required),
		//validation.Field(&c.LlmId, validation.Required),
		//validation.Field(&c.SystemPrompy, validation.Required, validation.Length(1, 50000)),
	)
}

type RunnerRes struct {
	Id int64 `json:"id,string"` // 应用名称
	CreateRunnerReq
	TriggerTime   int64                  `json:"trigger_time"`
	TriggerMode   string                 `json:"trigger_mode"`
	ExecuteStatus constant.ExecuteStatus `json:"execute_status"`
	ExecuteResult string                 `json:"execute_result"`
	ExecuteLog    string                 `json:"execute_log"`

	CreateUserId int64 `json:"create_user_id,string"` // 创建者ID
	UpdatedTime  int64 `json:"updated_time"`          // 修改时间
	CreatedTime  int64 `json:"created_time"`          // 创建时间
	DeleteTime   int64 `json:"delete_time"`           // 删除时间
}

type RunnerStatus struct {
	Id           int64  `json:"id,string"`
	Error        string `json:"error"`
	Phase        string `json:"phase"`
	WebServerUrl string `json:"web_server_url"`
}

var _ Validation = ListRunnerReq{}

type ListRunnerReq struct {
	RunnerType     constant.RunnerType    `json:"runner_type"`
	RunnerTargetId int64                  `json:"runner_target_id,string"`
	RunnerRecordId int64                  `json:"runner_record_id,string"`
	ExecuteStatus  constant.ExecuteStatus `json:"execute_status"`
	PageSize       int                    `json:"page_size,string"`
	LastId         int64                  `json:"last_id,string"`
	StartTime      int64                  `json:"start_time,string"`
	EndTime        int64                  `json:"end_time,string"`
}

func (c ListRunnerReq) Validation(ctx context.Context) error {
	return validation.ValidateStruct(&c,
		validation.Field(&c.RunnerType,
			validation.In(constant.RunnerTypeApp, constant.RunnerTypeAgentTeam, constant.RunnerTypeConnector, constant.RunnerTypeDataProcess, constant.RunnerTypePython)),
		validation.Field(&c.RunnerTargetId, validation.Required),
		validation.Field(&c.ExecuteStatus,
			validation.When(c.ExecuteStatus != "", validation.In(constant.ExecuteStatusInit, constant.ExecuteStatusRunning, constant.ExecuteStatusPending, constant.ExecuteStatusSuccess, constant.ExecuteStatusFailed)),
		),
		validation.Field(&c.PageSize, validation.Required, validation.Min(1)),
		validation.Field(&c.LastId, validation.Min(0)),
		validation.Field(&c.StartTime, validation.Min(0)),
		validation.Field(&c.EndTime, validation.Min(0)),
	)
}
