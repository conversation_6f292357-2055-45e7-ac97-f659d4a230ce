package vo

import (
	"context"

	validation "github.com/go-ozzo/ozzo-validation/v4"
)

var _ Validation = CreateAgentReq{}

type CreateAgentReq struct {
	Name     string `json:"name"` // 应用名称
	Remark   string `json:"remark"`
	Copyable bool   `json:"copyable"` // 是否允许copy

	AppId       int64 `json:"app_id,string"`
	AgentTeamId int64 `json:"agent_team_id,string"`

	Role            string      `json:"role"`      // 角色（crewai）
	Goal            string      `json:"goal"`      // 目标（crewai）
	Backstory       string      `json:"backstory"` // 背景信息（crewai）
	Verbose         bool        `json:"verbose"`
	AllowDelegation bool        `json:"allow_delegation"`
	Multimodal      bool        `json:"multimodal"`
	Memory          bool        `json:"memory"`
	MaxIter         int64       `json:"max_iter"`
	ToolIds         string      `json:"tool_ids"` // json
	LlmId           int64       `json:"llm_id,string"`
	Knowledges      []Knowledge `json:"knowledges"`
	ExtInfo         string      `json:"ext_info"`     // json
	MemorySpace     string      `json:"memory_space"` // 记忆空间
}

type Knowledge struct {
	KnowledgeType  string `json:"knowledge_type"`
	KnowledgeValue string `json:"knowledge_value"`
}

// Validation implements Validation.
func (c CreateAgentReq) Validation(ctx context.Context) error {
	return validation.ValidateStruct(&c,
		//validation.Field(&c.Icon, validation.Required),
		validation.Field(&c.Name, validation.Required, validation.Length(1, 100)),
		//validation.Field(&c.Type, validation.Required),
		//validation.Field(&c.LlmId, validation.Required),
		//validation.Field(&c.SystemPrompy, validation.Required, validation.Length(1, 50000)),
		validation.Field(&c.MemorySpace, validation.When(c.Memory, validation.Required)),
	)
}

type AgentRes struct {
	Id int64 `json:"id,string"` // 应用名称
	CreateAgentReq
	CreateUserId int64 `json:"create_user_id,string"` // 创建者ID
	UpdatedTime  int64 `json:"updated_time"`          // 修改时间
	CreatedTime  int64 `json:"created_time"`          // 创建时间
	DeleteTime   int64 `json:"delete_time"`           // 删除时间
}

type UpdateAgentReq struct {
	Id int64 `json:"id,string"`

	Name     *string `json:"name"` // 应用名称
	Remark   *string `json:"remark"`
	Copyable *bool   `json:"copyable"` // 是否允许copy

	AppId       *int64 `json:"app_id,string"`
	AgentTeamId *int64 `json:"agent_team_id,string"`

	Role            *string     `json:"role"`      // 角色（crewai）
	Goal            *string     `json:"goal"`      // 目标（crewai）
	Backstory       *string     `json:"backstory"` // 背景信息（crewai）
	Verbose         *bool       `json:"verbose"`
	AllowDelegation *bool       `json:"allow_delegation"`
	Multimodal      *bool       `json:"multimodal"`
	Memory          *bool       `json:"memory"`
	MaxIter         *int64      `json:"max_iter"`
	ToolIds         *string     `json:"tool_ids"` // json
	LlmId           *int64      `json:"llm_id,string"`
	Knowledges      []Knowledge `json:"knowledges"`
	ExtInfo         *string     `json:"ext_info"`     // json
	MemorySpace     *string     `json:"memory_space"` // 记忆空间
}

// Validation implements Validation.
func (u UpdateAgentReq) Validation(ctx context.Context) error {
	return validation.ValidateStruct(&u,
		validation.Field(&u.Id, validation.Required),
		//validation.Field(&u.UpdateFields, validation.Required),
	)
}

var _ Validation = UpdateAgentReq{}

type DeleteAgentReq struct {
	Ids []int64 `json:"ids,string"`
}

// Validation implements Validation.
func (d DeleteAgentReq) Validation(ctx context.Context) error {
	return validation.ValidateStruct(&d,
		validation.Field(&d.Ids, validation.Required),
		//validation.Field(&u.UpdateFields, validation.Required),
	)
}

type CopyAgentReq struct {
	Id int64 `json:"id,string"`

	Name        string `json:"name"` // 应用名称
	AppId       int64  `json:"app_id,string"`
	AgentTeamId int64  `json:"agent_team_id,string"`

	ForceCopy bool `json:"force_copy"`
}

// Validation implements Validation.
func (u CopyAgentReq) Validation(ctx context.Context) error {
	return validation.ValidateStruct(&u,
		//validation.Field(&u.Id, validation.Required),
		validation.Field(&u.AppId, validation.Required),
		validation.Field(&u.AgentTeamId, validation.Required),
	)
}
