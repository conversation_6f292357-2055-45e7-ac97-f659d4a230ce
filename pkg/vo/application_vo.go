package vo

import (
	"context"

	validation "github.com/go-ozzo/ozzo-validation/v4"
)

var _ Validation = CreateApplicationReq{}

type CreateApplicationReq struct {
	Name     string `json:"name"`
	Remark   string `json:"remark"`
	Copyable bool   `json:"copyable"`
	Sort     string `json:"sort"`
}

// Validation implements Validation.
func (c CreateApplicationReq) Validation(ctx context.Context) error {
	return validation.ValidateStruct(&c,
		//validation.Field(&c.Icon, validation.Required),
		validation.Field(&c.Name, validation.Required, validation.Length(1, 100)),
		//validation.Field(&c.Type, validation.Required),
		//validation.Field(&c.LlmId, validation.Required),
		//validation.Field(&c.SystemPrompy, validation.Required, validation.Length(1, 50000)),
	)
}

type ApplicationRes struct {
	Id              int64          `json:"id,string"` // 应用名称
	Name            string         `json:"name"`
	Remark          string         `json:"remark"`
	Copyable        bool           `json:"copyable"`
	Sort            string         `json:"sort"`
	CreateUserId    int64          `json:"create_user_id,string"` // 创建者ID
	CreateUser      UserSimpleInfo `json:"create_user"`
	IsCollaboration bool           `json:"is_collaboration"`
	UpdatedTime     int64          `json:"updated_time"` // 修改时间
	CreatedTime     int64          `json:"created_time"` // 创建时间
	DeleteTime      int64          `json:"delete_time"`  // 删除时间
}

type ApplicationResWithMacro struct {
	ApplicationRes
	MacroList     []TaskMacro      `json:"macro_list"` // 同名去重
	Collaboration []UserSimpleInfo `json:"collaboration"`
}

type UpdateApplicationReq struct {
	Id       int64   `json:"id,string"`
	Name     *string `json:"name"`
	Remark   *string `json:"remark"`
	Copyable *bool   `json:"copyable"`
	Sort     *string `json:"sort"`
}

// Validation implements Validation.
func (u UpdateApplicationReq) Validation(ctx context.Context) error {
	return validation.ValidateStruct(&u,
		validation.Field(&u.Id, validation.Required),
		//validation.Field(&u.UpdateFields, validation.Required),
	)
}

var _ Validation = UpdateApplicationReq{}

type DeleteApplicationReq struct {
	Id int64 `json:"id,string"`
}

// Validation implements Validation.
func (d DeleteApplicationReq) Validation(ctx context.Context) error {
	return validation.ValidateStruct(&d,
		validation.Field(&d.Id, validation.Required),
		//validation.Field(&u.UpdateFields, validation.Required),
	)
}

type ResetCollaborationReq struct {
	AppId            int64    `json:"app_id,string"`
	CollaborationIds []string `json:"collaboration_ids"`
}

// Validation implements Validation.
func (r ResetCollaborationReq) Validation(ctx context.Context) error {
	return validation.ValidateStruct(&r,
		validation.Field(&r.AppId, validation.Required),
	)
}
