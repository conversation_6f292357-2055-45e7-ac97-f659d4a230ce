package vo

import (
	validation "github.com/go-ozzo/ozzo-validation/v4"
)

type AcsLoginReq struct {
	Code        string `json:"code" form:"code"`
	RedirectUri string `json:"redirect_uri" form:"redirect_uri"`
}

type AcsLoginRes struct {
	Id        int64  `json:"id,string"`
	Token     string `json:"token"`
	Email     string `json:"email"`
	Name      string `json:"name"`
	Username  string `json:"username"`
	SecretKey string `json:"secret_key"`
}

type SsoLoginReq struct {
	Code string `json:"code" form:"code"`
}

func (s *SsoLoginReq) Validate() error {
	return validation.ValidateStruct(s,
		validation.Field(&s.Code, validation.Required),
	)
}

type SsoLoginRes struct {
	Id    int64  `json:"id,string"`
	Token string `json:"token"`
	Email string `json:"email"`
	Name  string `json:"name"`
}
