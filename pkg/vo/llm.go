package vo

import "jarvis_api/constant"

type CreateLlmReq struct {
	LlmProvideAuthId int64                `json:"llm_provide_auth_id,string"` // 大模型提供服务商认证信息表ID
	Name             string               `json:"name" `                      // 模型名称
	Llmkey           string               `json:"llmkey" `                    // 模型唯一key
	LlmType          constant.LLMType     `json:"llm_type" `                  //模型类型 gpt、claude
	LlmCategory      constant.LlmCategory `json:"llm_category"`               // 模型分类 chat、embeding...
	Description      string               `json:"description"`                // 模型描述
}

type LlmRes struct {
	Id     int64  `json:"id,string"`
	Name   string `json:"name" `   // 模型名称
	LlmKey string `json:"llmkey" ` // 模型唯一key
	Remark string `json:"remark"`  // 模型描述
}
