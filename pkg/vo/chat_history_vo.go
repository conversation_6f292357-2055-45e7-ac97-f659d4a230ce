package vo

import (
	"context"

	validation "github.com/go-ozzo/ozzo-validation/v4"
)

var _ Validation = ListChatHistoryReq{}

type ListChatHistoryReq struct {
	/*
		获取指定AppID 的聊天记录，如果为0获取所有聊天记录，
		如果App 是BaseApp 则获取Base相关的所有聊天记录
	*/
	AppId    int64 `form:"app_id,string" json:"app_id,string"`
	PageSize int   `form:"page_size" json:"page_size"`
	LastId   int64 `form:"last_id,string" json:"last_id,string"`
}

// Validation implements Validation.
func (c ListChatHistoryReq) Validation(ctx context.Context) error {
	return nil
}

type ChatHistoryRes struct {
	Id          int64  `json:"id,string"`
	Name        string `json:"name"`           // 会话名称
	UserId      int64  `json:"user_id,string"` // 用户ID
	AppId       int64  `json:"app_id,string"`  // 应用ID
	BaseApp     int    `json:"base_app"`       // 是否是基础模型应用
	UpdatedTime int64  `json:"updated_time"`   // 修改时间
	CreatedTime int64  `json:"created_time"`   // 创建时间
	DeleteTime  int64  `json:"delete_time"`    // 删除时间
}

// todo app object
type ListChatHistoryRes struct {
	Id          int64  `json:"id,string"`
	Name        string `json:"name"`           // 会话名称
	UserId      int64  `json:"user_id,string"` // 用户ID
	AppId       int64  `json:"app_id,string"`  // 应用ID
	BaseApp     int    `json:"base_app"`       // 是否是基础模型应用
	UpdatedTime int64  `json:"updated_time"`   // 修改时间
	CreatedTime int64  `json:"created_time"`   // 创建时间
	DeleteTime  int64  `json:"delete_time"`    // 删除时间
}

var _ Validation = ListChatHistoryContentReq{}

type ListChatHistoryContentReq struct {
	ChatHistoryId int64 `form:"chat_history_id,string" json:"chat_history_id,string"`
}

// Validation implements Validation.
func (l ListChatHistoryContentReq) Validation(ctx context.Context) error {
	return validation.ValidateStruct(&l,
		validation.Field(&l.ChatHistoryId, validation.Required),
	)
}

// todo app object
type ListChatHistoryContentRes struct {
	Id            int64  `json:"id,string"`
	ChatHistoryId int64  `json:"chat_history_id,string"` // 聊天记录ID
	AppId         int64  `json:"app_id,string"`          // 应用ID
	Role          string `json:"role"`                   // 角色 user、ai、tool、function
	Content       string `json:"content"`                // 聊天内容
	ImageUrl      string `json:"image_url"`              // 图片地址
	UpdatedTime   int64  `json:"updated_time"`           // 修改时间
	CreatedTime   int64  `json:"created_time"`           // 创建时间
	DeleteTime    int64  `json:"delete_time"`            // 删除时间
}

var _ Validation = DeleteChatHistoryReq{}

type DeleteChatHistoryReq struct {
	ChatHistoryId int64 `form:"chat_history_id,string" json:"chat_history_id,string"`
}

// Validation implements Validation.
func (d DeleteChatHistoryReq) Validation(ctx context.Context) error {
	return validation.ValidateStruct(&d,
		validation.Field(&d.ChatHistoryId, validation.Required),
	)
}

var _ Validation = RenameChatHistoryReq{}

type RenameChatHistoryReq struct {
	ChatHistoryId int64  `form:"chat_history_id,string" json:"chat_history_id,string"`
	Name          string `form:"name" json:"name"`
}

// Validation implements Validation.
func (r RenameChatHistoryReq) Validation(ctx context.Context) error {
	return validation.ValidateStruct(&r,
		validation.Field(&r.ChatHistoryId, validation.Required),
		validation.Field(&r.Name, validation.Required, validation.Length(1, 100)),
	)
}
