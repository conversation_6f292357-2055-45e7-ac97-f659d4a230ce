package vo

import (
	"context"
	"jarvis_api/constant"

	validation "github.com/go-ozzo/ozzo-validation/v4"
)

var _ Validation = CreateKnowledgeReq{}

type CreateKnowledgeReq struct {
	Name     string                 `json:"name"`
	IsDir    bool                   `json:"is_dir,string"`
	ParentId int64                  `json:"parent_id,string"`
	Remark   string                 `json:"remark"`
	Content  string                 `json:"content"`
	FileType constant.KnowledgeType `json:"file_type"`
	S3Key    string                 `json:"s3_key"`
	FileName string                 `json:"file_name"`
	IsData   bool                   `json:"is_data"`
}

// Validation implements Validation.
func (c CreateKnowledgeReq) Validation(ctx context.Context) error {
	return validation.ValidateStruct(&c,
		validation.Field(&c.Name, validation.Required, validation.Length(1, 100)),
		validation.Field(&c.IsDir, validation.Required),
		validation.Field(&c.<PERSON>, validation.Min(0)),
		validation.Field(&c.Remark, validation.Length(0, 100)),
		validation.Field(&c.Content, validation.Length(0, 1000000)),
		validation.Field(&c.FileType, validation.In(constant.KnowledgeTypeDoc, constant.KnowledgeTypePdf, constant.KnowledgeTypeTxt, constant.KnowledgeTypeText)),
		validation.Field(&c.S3Key, validation.When(c.FileType != constant.KnowledgeTypeText && !c.IsDir, validation.Required)),
		validation.Field(&c.FileName, validation.When(c.FileType != constant.KnowledgeTypeText && !c.IsDir, validation.Required)),
	)
}

type KnowledgeRes struct {
	Id           int64  `json:"id,string"`
	Name         string `json:"name"`
	IsDir        bool   `json:"is_dir"`
	ParentId     int64  `json:"parent_id,string"`
	FileName     string `json:"file_name"`
	Content      string `json:"content"`
	FileType     string `json:"file_type"`
	S3Key        string `json:"s3_key"`
	Remark       string `json:"remark"`
	IsData       bool   `json:"is_data"`
	CreateUserId int64  `json:"create_user_id,string"`
	UpdatedTime  int64  `json:"updated_time,string"`
	CreatedTime  int64  `json:"created_time,string"`
	DeleteTime   int64  `json:"delete_time,string"`
}

type UpdateKnowledgeReq struct {
	Name     *string                 `json:"name"`
	Remark   *string                 `json:"remark"`
	Content  *string                 `json:"content"`
	FileType *constant.KnowledgeType `json:"file_type"`
	S3Key    *string                 `json:"s3_key"`
	FileName *string                 `json:"file_name"`
}
