package vo

import (
	"context"
	"jarvis_api/constant"

	validation "github.com/go-ozzo/ozzo-validation/v4"
)

var _ Validation = AppChatReq{}

type AppChatReq struct {
	AppId         int64                 `json:"app_id,string"`
	ChatHistoryId int64                 `json:"chat_history_id,string"`
	LlmId         int64                 `json:"llm_id,string"` //LLM模型ID(只用于多模型测试场景会话)
	Query         string                `json:"query"`
	ImageUrl      string                `json:"image_url"`
	Temperature   float64               `json:"temperature"`
	ResponseMode  constant.ResponseMode `json:"response_mode"`
}

// Validation implements Validation.
func (a AppChatReq) Validation(ctx context.Context) error {
	// errQuery := validation.ValidateStruct(&a,
	// 	validation.Field(&a.Query, validation.Required))

	// errImageUrl := validation.ValidateStruct(&a,
	// 	validation.Field(&a.ImageUrl, validation.Required))

	// if errImageUrl != nil && errQuery != nil {
	// 	return fmt.Errorf("query:%v or image_url:%v is required", a.Query, a.ImageUrl)
	// }
	return validation.ValidateStruct(&a,
		validation.Field(&a.AppId, validation.Required),
		validation.Field(&a.Query, validation.Required),
		validation.Field(&a.ResponseMode,
			validation.In(
				constant.ResponseModeBlocking,
				constant.ResponseModeStreaming,
			)),
	)
}

type ChatEventType string

const (
	ChatEventTypeMessage  ChatEventType = "message"
	ChatEventTypeFunction ChatEventType = "function"
	ChatEventTypeEnd      ChatEventType = "end"
	ChatEventTypeError    ChatEventType = "error"
)

type AppChatResp struct {
	Event         ChatEventType `json:"event"`
	ChatHistoryId int64         `json:"chat_history_id,string"` // 会话ID chat_history表中的ID
	QuestionID    int64         `json:"question_id,string"`     // 用户问的问题在chat_history_content表中的ID
	AnswerID      int64         `json:"answer_id,string"`       // 当前这正在回复的内容在chat_history_content表中的ID
	AppId         int64         `json:"app_id,string"`          // 应用ID
	Content       string        `json:"content"`                // 消息内容
}
