package vo

import (
	"context"

	"github.com/aws/aws-sdk-go-v2/aws"
	validation "github.com/go-ozzo/ozzo-validation/v4"
)

var _ Validation = CreateTeamReq{}

type CreateTeamReq struct {
	Name            string `json:"name"` // 应用名称
	Remark          string `json:"remark"`
	Copyable        bool   `json:"copyable"` // 是否允许copy
	AppId           int64  `json:"app_id,string"`
	TaskSort        string `json:"task_sort"` // 排序关系
	ResultParamsKey string `json:"result_params_key"`
	IsPlanning      bool   `json:"is_planning"` // 是否开启 Planning
	PlanningLlmId   int64  `json:"planning_llm_id,string"`
	PythonStruct    string `json:"python_struct"` // 使用python定义结构task最终返回内容的格式
	ExtInfo         string `json:"ext_info"`      // 扩展信息 使用json存储
}

// Validation implements Validation.
func (c CreateTeamReq) Validation(ctx context.Context) error {
	return validation.ValidateStruct(&c,
		//validation.Field(&c.Icon, validation.Required),
		validation.Field(&c.Name, validation.Required, validation.Length(1, 100)),
		//validation.Field(&c.Type, validation.Required),
		//validation.Field(&c.LlmId, validation.Required),
		//validation.Field(&c.SystemPrompy, validation.Required, validation.Length(1, 50000)),
		validation.Field(&c.PlanningLlmId, validation.When(c.IsPlanning, validation.Required)),
	)
}

type TaskMacro struct {
	Name         string  `json:"name"`
	MacroType    string  `json:"macro_type"`
	DefaultValue *string `json:"default_value"`
	Desc         string  `json:"desc"`
	Ext          string  `json:"ext"`
}

type TeamRes struct {
	Id int64 `json:"id,string"` // 应用名称
	CreateTeamReq
	CreateUserId int64 `json:"create_user_id,string"` // 创建者ID
	UpdatedTime  int64 `json:"updated_time"`          // 修改时间
	CreatedTime  int64 `json:"created_time"`          // 创建时间
	DeleteTime   int64 `json:"delete_time"`           // 删除时间
}

type TeamResWithMacro struct {
	TeamRes
	MacroList []TaskMacro `json:"macro_list"` // 同名去重
}

type UpdateTeamReq struct {
	Id       int64   `json:"id,string"`
	Name     *string `json:"name"`
	Remark   *string `json:"remark"`
	Copyable *bool   `json:"copyable"`

	AppId           *int64  `json:"app_id,string"`
	TaskSort        *string `json:"task_sort"` // 排序关系
	ResultParamsKey *string `json:"result_params_key"`
	IsPlanning      *bool   `json:"is_planning"` // 是否开启 Planning
	PlanningLlmId   *int64  `json:"planning_llm_id,string"`
	PythonStruct    *string `json:"python_struct"` // 使用python定义结构task最终返回内容的格式
	ExtInfo         *string `json:"ext_info"`      // 扩展信息 使用json存储
}

// Validation implements Validation.
func (u UpdateTeamReq) Validation(ctx context.Context) error {
	return validation.ValidateStruct(&u,
		validation.Field(&u.Id, validation.Required),
		//validation.Field(&u.UpdateFields, validation.Required),
		validation.Field(&u.PlanningLlmId, validation.When(aws.ToBool(u.IsPlanning), validation.Required)),
	)
}

var _ Validation = UpdateTeamReq{}

type DeleteTeamReq struct {
	Id int64 `json:"id,string"`
}

// Validation implements Validation.
func (d DeleteTeamReq) Validation(ctx context.Context) error {
	return validation.ValidateStruct(&d,
		validation.Field(&d.Id, validation.Required),
		//validation.Field(&u.UpdateFields, validation.Required),
	)
}

type CopyTeamReq struct {
	Id    int64  `json:"id,string"`
	Name  string `json:"name"`
	AppId int64  `json:"app_id,string"`

	ForceCopy bool `json:"force_copy"`
}

// Validation implements Validation.
func (u CopyTeamReq) Validation(ctx context.Context) error {
	return validation.ValidateStruct(&u,
		//validation.Field(&u.Id, validation.Required),
		validation.Field(&u.AppId, validation.Required),
	)
}

type AutoCreateTeamReq struct {
	AppId        int64  `json:"app_id,string"`
	Name         string `json:"name"`
	Requirement  string `json:"requirement"`
	OpenChatMode bool   `json:"open_chat_mode"`
}

// Validation implements Validation.
func (u AutoCreateTeamReq) Validation(ctx context.Context) error {
	return validation.ValidateStruct(&u,
		validation.Field(&u.Requirement, validation.Required),
		validation.Field(&u.AppId, validation.Required),
	)
}
