package constant

type RunnerType string

const (
	RunnerTypeApp         = RunnerType("app")
	RunnerTypeAgentTeam   = RunnerType("agent_team")
	RunnerTypeConnector   = RunnerType("connector")
	RunnerTypePython      = RunnerType("python")
	RunnerTypeDataProcess = RunnerType("data_process")
)

type TriggerMode string

const (
	TriggerModeWeb   = TriggerMode("web")
	TriggerModeTimer = TriggerMode("timer")
	TriggerModeApi   = TriggerMode("api")
)

type ExecuteStatus string

const (
	ExecuteStatusInit    = ExecuteStatus("init")
	ExecuteStatusRunning = ExecuteStatus("running")
	ExecuteStatusPending = ExecuteStatus("pending")
	ExecuteStatusSuccess = ExecuteStatus("success")
	ExecuteStatusFailed  = ExecuteStatus("failed")
)

type KnowledgeType string

const (
	KnowledgeTypeText = KnowledgeType("text")
	KnowledgeTypePdf  = KnowledgeType("pdf")
	KnowledgeTypeDoc  = KnowledgeType("doc")
	KnowledgeTypeTxt  = KnowledgeType("txt")
)
