package constant

// aws、azure
type ServiceProvider string

const (
	ServiceProviderAws      = ServiceProvider("aws")
	ServiceProviderAzure    = ServiceProvider("azure")
	ServiceProviderDeepseek = ServiceProvider("deepseek")
	ServiceProviderGcp      = ServiceProvider("gcp") // google cloud platform
)

type LLMType string

const (
	LLMTypeGpt      = LLMType("gpt")
	LLMTypeDeepseek = LLMType("deepseek")
	LLMTypeClaude   = LLMType("claude")
	LLMTypeGemini   = LLMType("gemini")
)

type LlmCategory string

const (
	LlmCategoryChat      = LlmCategory("chat")
	LlmCategoryEmbedding = LlmCategory("embedding")
)

type ApplicationType string

const (
	ApplicationTypeChat  = ApplicationType("chat")
	ApplicationTypeText  = ApplicationType("text")
	ApplicationTypeAgent = ApplicationType("agent")
)

type ResponseMode string

const (
	ResponseModeStreaming = ResponseMode("streaming")
	ResponseModeBlocking  = ResponseMode("blocking")
)
