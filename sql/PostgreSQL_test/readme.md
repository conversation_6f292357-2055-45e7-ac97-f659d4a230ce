# install PostgreSQL

## 部署pv(这种方式安装失败，修改values.yaml 让heml自动安装pvc)
k apply -f pvc.yaml
## 部署 postgresql

helm repo add bitnami https://charts.bitnami.com/bitnami
helm repo update
helm install postgresql-dev -f values.yaml bitnami/postgresql

更新
helm upgrade --install postgresql-dev -f values.yaml bitnami/postgresql




## 安装结果
NAME: postgresql-dev
LAST DEPLOYED: Fri Apr 11 14:42:20 2025
NAMESPACE: maxagent-test
STATUS: deployed
REVISION: 1
TEST SUITE: None
NOTES:
CHART NAME: postgresql
CHART VERSION: 16.6.3
APP VERSION: 17.4.0

Did you know there are enterprise versions of the Bitnami catalog? For enhanced secure software supply chain features, unlimited pulls from Docker, LTS support, or application customization, see Bitnami Premium or Tanzu Application Catalog. See https://www.arrow.com/globalecs/na/vendors/bitnami for more information.

** Please be patient while the chart is being deployed **

PostgreSQL can be accessed via port 5432 on the following DNS names from within your cluster:

    postgresql-dev.maxagent-test.svc.cluster.local - Read/Write connection

To get the password for "postgres" run:

    export POSTGRES_ADMIN_PASSWORD=$(kubectl get secret --namespace maxagent-test postgresql-dev -o jsonpath="{.data.postgres-password}" | base64 -d)

To get the password for "jarvis" run:

    export POSTGRES_PASSWORD=$(kubectl get secret --namespace maxagent-test postgresql-dev -o jsonpath="{.data.password}" | base64 -d)

To connect to your database run the following command:

    kubectl run postgresql-dev-client --rm --tty -i --restart='Never' --namespace maxagent-test --image docker.io/bitnami/postgresql:17.4.0-debian-12-r15 --env="PGPASSWORD=$POSTGRES_PASSWORD" \
      --command -- psql --host postgresql-dev -U jarvis -d jarvis -p 5432

    > NOTE: If you access the container using bash, make sure that you execute "/opt/bitnami/scripts/postgresql/entrypoint.sh /bin/bash" in order to avoid the error "psql: local user with ID 1001} does not exist"

To connect to your database from outside the cluster execute the following commands:

    kubectl port-forward --namespace maxagent-test svc/postgresql-dev 5432:5432 &
    PGPASSWORD="$POSTGRES_PASSWORD" psql --host 127.0.0.1 -U jarvis -d jarvis -p 5432

WARNING: The configured password will be ignored on new installation in case when previous PostgreSQL release was deleted through the helm command. In that case, old PVC will have an old password, and setting it through helm won't take effect. Deleting persistent volumes (PVs) will solve the issue.

WARNING: There are "resources" sections in the chart not set. Using "resourcesPreset" is not recommended for production. For production installations, please set the following values according to your workload needs:
  - primary.resources
  - readReplicas.resources
+info https://kubernetes.io/docs/concepts/configuration/manage-resources-containers