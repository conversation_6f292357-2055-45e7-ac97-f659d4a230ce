# define default database user, name, and password for PostgreSQL deployment
auth:
  enablePostgresUser: true
  postgresPassword: "af0e7b1845abe3edb843181fbe51da2cafcfacda"
  username: "jarvis"
  password: "af0e7b1845abe3edb843181fbe51da2cafcfacda"
  database: "jarvis"

# The postgres helm chart deployment will be using PVC postgresql-data-claim
primary:
  persistence:
    enabled: true
    existingClaim: "postgresql-pv-claim"
    # 添加初始化容器来设置权限
  initContainers:
    - name: volume-permissions
      image: busybox
      command: ["sh", "-c", "mkdir -p /bitnami/postgresql/data && chmod -R 700 /bitnami/postgresql && chown -R 1001:1001 /bitnami/postgresql"]
      securityContext:
        runAsUser: 0
      volumeMounts:
        - name: data
          mountPath: /bitnami/postgresql