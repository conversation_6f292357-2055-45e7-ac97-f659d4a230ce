apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: postgresql-dev.spotmaxtech.com
  namespace: maxagent-test
  annotations:
    nginx.ingress.kubernetes.io/backend-protocol: "TCP"
    nginx.ingress.kubernetes.io/proxy-body-size: "0"
    nginx.ingress.kubernetes.io/proxy-connect-timeout: "3600"
    nginx.ingress.kubernetes.io/proxy-send-timeout: "3600"
    nginx.ingress.kubernetes.io/proxy-read-timeout: "3600"
spec:
  ingressClassName: nginx
  tls:
    - hosts:
        - postgresql-dev.spotmaxtech.com
      secretName: spotmaxtech-secret
  rules:
  - host: postgresql-dev.spotmaxtech.com
    http:
      paths:
      - pathType: Prefix
        path: /
        backend:
          service:
            name: postgresql-dev
            port:
              number: 5432