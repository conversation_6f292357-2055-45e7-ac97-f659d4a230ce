apiVersion: v1
kind: PersistentVolume
metadata:
  name: postgresql-pv-prod
  labels:
    type: local
spec:
  storageClassName: gp2
  capacity:
    storage: 10Gi
  accessModes:
    - ReadWriteOnce
  hostPath:
    path: "/mnt/data"
---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: postgresql-pv-prod-claim
  namespace: maxagent-prod
spec:
  storageClassName: gp2
  accessModes:
    - ReadWriteOnce
  resources:
    requests:
      storage: 10Gi
