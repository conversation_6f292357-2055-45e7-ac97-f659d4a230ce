# create database jarvis_prod default character set utf8mb4 collate utf8mb4_0900_as_cs;
#
# create database jarvis_test default character set utf8mb4 collate utf8mb4_0900_as_cs;
#
# use jarvis_test;
drop table if exists runner_record;

create table runner_record (
    id bigint unsigned not null primary key,
    runner_type varchar(200) default '' not null comment 'runner类型[app、agent_team、data_process]',
    app_id bigint default 0 not null comment 'app_id',
    agent_team_id bigint default 0 not null comment 'agent_team_id',
    data_process_id bigint default 0 not null comment 'data_process_id',
    create_user_id bigint default 0 not null comment 'create_user_id',
    trigger_time bigint default 0 not null comment '触发时间',
    trigger_mode varchar(200) default '' not null comment '触发方式[web、timer、api]',
    trigger_params json not null comment '触发参数',
    execute_status varchar(200) default '' not null comment '执行状态[init,running、pending,success、failed]',
    execute_result text not null comment '执行结果',
    execute_log varchar(200) default '' not null comment '执行日志',
    remark varchar(200) default '' not null comment '备注信息',
    updated_time bigint default 0 not null comment '修改时间',
    created_time bigint default 0 not null comment '创建时间',
    delete_time bigint default 0 not null comment '删除时间'
) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci comment 'runner记录表';

-- 索引1：（agent_team_id 分支）
CREATE INDEX idx_del_user_type_team_created ON runner_record (delete_time, create_user_id, runner_type, agent_team_id, created_time);

-- 索引2：（app_id 分支）
CREATE INDEX idx_del_user_type_app_created ON runner_record (delete_time, create_user_id, runner_type, app_id, created_time);

-- 索引3：添加联合索引
CREATE INDEX idx_id_delete_time ON runner_record (id, delete_time);

alter table runner_record add column connector_id bigint default 0 not null comment 'connector id' after data_process_id;

drop table if exists app;

create table app (
    id bigint unsigned not null primary key,
    name varchar(200) default '' not null comment 'app名称',
    create_user_id bigint default 0 not null comment '创建人',
    copyable bool default false not null comment '是否可复制',
    sort json not null comment '排序 json demo [{"process_type":"agent_team","id":1},{"process_type":"date_process","id":2}]',
    remark varchar(200) default '' not null comment '备注信息',
    created_time bigint default 0 not null comment '创建时间',
    updated_time bigint default 0 not null comment '修改时间',
    delete_time bigint default 0 not null comment '删除时间'
) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci comment 'app表';

-- 索引1：覆盖 create_user_id 分支
CREATE INDEX idx_delete_user_created ON app (delete_time, create_user_id, created_time);

-- 索引2：覆盖 copyable 分支
CREATE INDEX idx_delete_copy_created ON app (delete_time, copyable, created_time);

-- 索引3：添加联合索引
CREATE INDEX idx_id_delete_time ON app (id, delete_time);

drop table if exists agent_team;

create table agent_team (
    id bigint unsigned not null primary key,
    name varchar(200) default '' not null comment 'agent_team名称',
    app_id bigint default 0 not null comment '所属应用id',
    copyable bool default false not null comment '是否可复制',
    task_sort json not null comment '排序 json demo [1,2]',
    result_params_key varchar(200) default '' not null comment 'team 执行结束后的结果保存变量，用于作为下一个team的参数名',
    create_user_id bigint default 0 not null comment '创建人',
    remark varchar(200) default '' not null comment '备注信息',
    created_time bigint default 0 not null comment '创建时间',
    updated_time bigint default 0 not null comment '修改时间',
    delete_time bigint default 0 not null comment '删除时间'
) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci comment 'agent_team表';

-- 索引1：优化 create_user_id 分支 + 排序
CREATE INDEX idx_del_user_created ON agent_team (delete_time, create_user_id, created_time);

-- 索引2：优化 copyable 分支 + 排序
CREATE INDEX idx_del_copy_created ON agent_team (delete_time, copyable, created_time);

-- 索引3：添加联合索引
CREATE INDEX idx_id_delete_time ON agent_team (id, delete_time);

drop table if exists tasks;

create table tasks (
    id bigint unsigned not null primary key,
    name varchar(200) default '' not null comment '任务名称',
    description text not null comment '任务描述(crewai)',
    app_id bigint default 0 not null comment '所属应用id',
    agent_team_id bigint default 0 not null comment '所属的自动化工作流id',
    agent_id bigint default 0 not null comment 'task 使用的主agent(crewai)',
    expected_output text not null comment '期望输出结果(crewai)',
    output_file varchar(200) default '' not null comment '结果存储到文件，指定文件路径(crewai)',
    ext_info json not null comment '扩展信息 使用json存储',
    copyable bool default false not null comment '是否可复制',
    create_user_id bigint default 0 not null comment '创建人',
    remark varchar(200) default '' not null comment '备注信息',
    macro_list json not null comment 'description内容中引用的宏列表 使用json存储',
    created_time bigint default 0 not null comment '创建时间',
    updated_time bigint default 0 not null comment '修改时间',
    delete_time bigint default 0 not null comment '删除时间'
) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci comment 'task表';

-- 索引1：优化 create_user_id 分支
CREATE INDEX idx_del_user_created ON tasks (delete_time, create_user_id, created_time);

-- 索引2：优化 copyable 分支
CREATE INDEX idx_del_copy_created ON tasks (delete_time, copyable, created_time);

-- 索引3：添加联合索引
CREATE INDEX idx_id_delete_time ON tasks (id, delete_time);

drop table if exists agents;

create table agents (
    id bigint unsigned not null primary key,
    name varchar(200) default '' not null comment '别名',
    app_id bigint default 0 not null comment '所属应用id',
    agent_team_id bigint default 0 not null comment '所属的自动化工作流id',
    role varchar(200) default '' not null comment '角色（crewai）',
    goal text not null comment '目标（crewai）',
    backstory text not null comment '背景信息（crewai）',
    verbose bool default false not null comment '详细思考日志（crewai）',
    allow_delegation bool default false not null comment '是否允许委托给其他agent（crewai）',
    memory bool default false not null comment '是否使用记忆（crewai）',
    max_iter bigint default 2 not null comment '最大迭代次数（crewai）',
    tool_ids json not null comment '工具列表[数组]',
    llm_id bigint default 0 not null comment '模型',
    ext_info json not null comment '扩展信息 json存储',
    copyable bool default false not null comment '是否可复制',
    create_user_id bigint default 0 not null comment '创建人',
    remark varchar(200) default '' not null comment '备注信息',
    created_time bigint default 0 not null comment '创建时间',
    updated_time bigint default 0 not null comment '修改时间',
    delete_time bigint default 0 not null comment '删除时间'
) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci comment 'agent表';

-- 索引1：优化 create_user_id 分支
CREATE INDEX idx_del_user_created ON agents (delete_time, create_user_id, created_time);

-- 索引2：优化 copyable 分支
CREATE INDEX idx_del_copy_created ON agents (delete_time, copyable, created_time);

-- 索引3：添加联合索引
CREATE INDEX idx_id_delete_time ON agents (id, delete_time);

drop table if exists llms;

create table llms (
    id bigint unsigned not null primary key,
    name varchar(200) default '' not null comment '名称',
    llm_key varchar(200) default '' not null comment 'key',
    create_user_id bigint default 0 not null comment '创建人',
    remark varchar(200) default '' not null comment '备注信息',
    created_time bigint default 0 not null comment '创建时间',
    updated_time bigint default 0 not null comment '修改时间',
    delete_time bigint default 0 not null comment '删除时间'
) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci comment 'llm表';

INSERT INTO llms (id, name, llm_key, create_user_id, remark) VALUES (1, 'Gemini_v2_FlashExp', 'gemini_2_flash_exp', 14592, 'Google Gemini V2 Flash exp');
INSERT INTO llms (id, name, llm_key, create_user_id, remark) VALUES (2, 'Gpt4.1', 'gpt-4.1', 14592, 'OpenAI GPT4.1');
INSERT INTO llms (id, name, llm_key, create_user_id, remark) VALUES (3, 'Gpt4o', 'gpt-4o', 14592, 'OpenAI GPT4o');
INSERT INTO llms (id, name, llm_key, create_user_id, remark) VALUES (4, 'Gpt4oMini', 'gpt-4o-mini', 14592, 'OpenAI GPT4o Mini');
INSERT INTO llms (id, name, llm_key, create_user_id, remark) VALUES (5, 'Gemini_v2_ProExp', 'gemini_2_exp', 14592, 'Google Gemini V2 Pro exp');
INSERT INTO llms (id, name, llm_key, create_user_id, remark) VALUES (6, 'Claude35_v2', 'claude_35_v2', 14592, 'Claude3.5 v2');


drop table if exists tools;

create table tools (
    id bigint unsigned not null primary key,
    name varchar(200) default '' not null comment '名称',
    tool_key varchar(200) default '' not null comment 'key',
    create_user_id bigint default 0 not null comment '创建人',
    remark varchar(200) default '' not null comment '备注信息',
    created_time bigint default 0 not null comment '创建时间',
    updated_time bigint default 0 not null comment '修改时间',
    delete_time bigint default 0 not null comment '删除时间'
) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci comment 'tool表';

INSERT INTO tools (id, name, tool_key, create_user_id, remark) VALUES (1, 'Google Search', 'google_search', 14592, 'Google Search');
INSERT INTO tools (id, name, tool_key, create_user_id, remark) VALUES (2, 'AskNews', 'asknews', 14592, 'AskNews是一款新闻增强工具,每日处理30万篇文章,提供自然语言查询接口。它翻译、总结、提取实体,索引到向量数据库,覆盖多国多语言,注重透明度。');
INSERT INTO tools (id, name, tool_key, create_user_id, remark) VALUES (3, 'Wikipedia', 'wikipedia', 14592, '维基百科 是一个多语言的自由在线百科全书，由志愿者社区（称为维基人）通过开放协作和使用名为 MediaWiki 的 wiki 编辑系统编写和维护。 Wikipedia 是历史上规模最大、阅读量最多的参考作品。');
INSERT INTO tools (id, name, tool_key, create_user_id, remark) VALUES (4, 'Arxiv', 'arxiv', 14592, 'arXiv是一个收集物理学、数学、计算机科学、生物学与数理经济学的论文预印本的网站，由美国康奈尔大学维护。');
INSERT INTO tools (id, name, tool_key, create_user_id, remark) VALUES (5, 'HumanFeedBack', 'human_feed_back', 14592, '接收用户反馈');
INSERT INTO tools (id, name, tool_key, create_user_id, remark) VALUES (6, 'ScrapeWebsite', 'scrape_website_tool', 14592, 'Read website content');
INSERT INTO tools (id, name, tool_key, create_user_id, remark) VALUES (7, 'Serper', 'serper_dev_tool', 14592, 'Search the internet with Serper');
INSERT INTO tools (id, name, tool_key, create_user_id, remark) VALUES (8, 'PythonCodeInterpreter', 'python_code_interpreter', 14592, 'python 代码解释器');
INSERT INTO tools (id, name, tool_key, create_user_id, remark) VALUES (9, 'finddefinition', 'finddefinition', 14592, '');
INSERT INTO tools (id, name, tool_key, create_user_id, remark) VALUES (10, 'findimplementation', 'findimplementation', 14592, '');
INSERT INTO tools (id, name, tool_key, create_user_id, remark) VALUES (11, 'findreferences', 'findreferences', 14592, '');
INSERT INTO tools (id, name, tool_key, create_user_id, remark) VALUES (12, 'findrelated', 'findrelated', 14592, '');
INSERT INTO tools (id, name, tool_key, create_user_id, remark) VALUES (13, 'findspans', 'findspans', 14592, '');
INSERT INTO tools (id, name, tool_key, create_user_id, remark) VALUES (14, 'readcode', 'readcode', 14592, '');
INSERT INTO tools (id, name, tool_key, create_user_id, remark) VALUES (15, 'readdirectorytree', 'readdirectorytree', 14592, '');
INSERT INTO tools (id, name, tool_key, create_user_id, remark) VALUES (16, 'readfile', 'readfile', 14592, '');



drop table if exists sys_config;
create table sys_config
(
    id           int auto_increment
        primary key,
    company_id   int          default 0  not null comment '公司ID',
    name         varchar(100) default '' not null comment '配置名称',
    `key`        varchar(100) default '' not null comment '配置key',
    value        text                    not null comment '配置值',
    `desc`       varchar(200) default '' not null comment '描述',
    updated_time bigint       default 0  not null comment '修改时间',
    created_time bigint       default 0  not null comment '创建时间',
    delete_time  bigint       default 0  not null comment '删除时间'
) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci comment '系统配置表';


drop table if exists connectors;
create table connectors
(
    id                bigint primary key,
    name              varchar(200) default ''    not null comment '名称',
    restful_url       varchar(500) default ''    not null comment 'restful url',
    remark            varchar(200) default ''    not null comment '备注',
    copyable          bool         default false not null comment '是否可复制',
    app_id            bigint       default 0     not null comment '所属应用id',
    result_params_key varchar(200) default ''    not null comment '结果参数key',
    macro_list        text                       not null comment '宏列表',
    create_user_id    bigint       default 0     not null comment '创建人',
    updated_time      bigint       default 0     not null comment '修改时间',
    created_time      bigint       default 0     not null comment '创建时间',
    delete_time       bigint       default 0     not null comment '删除时间'
) CHARACTER SET utf8mb4
  COLLATE utf8mb4_0900_ai_ci comment 'connector表';


drop table if exists knowledges;
create table knowledges
(
    id bigint primary key ,
    name varchar(200) default '' not null comment '名称',
    is_dir bool default false not null comment '是否是目录',
    parent_id bigint default 0 not null comment '父目录id',
    file_name varchar(200) default '' not null comment '文件原始名称',
    content text not null comment '内容',
    file_type varchar(200) default '' not null comment '文件类型【text、txt、pdf、doc】',
    s3_key varchar(200) default '' not null comment 'oss key',
    remark varchar(200) default '' not null comment '备注',
    create_user_id bigint default 0 not null comment '创建人',
    updated_time bigint default 0 not null comment '修改时间',
    created_time bigint default 0 not null comment '创建时间',
    delete_time bigint default 0 not null comment '删除时间'

)CHARACTER SET utf8mb4
  COLLATE utf8mb4_0900_ai_ci comment '知识库';

alter table agents
    add knowledges text null comment '知识';


alter table tasks add column memory_space varchar(200) default '' not null comment '记忆空间';
alter table tasks add column memory_category varchar(200) default '' not null comment '记忆类型';
alter table agents add column memory_space varchar(200) default '' not null comment '记忆空间';



-- auto-generated definition
drop table if exists user;
create table user
(
    id           bigint unsigned                                            not null
        primary key,
    company_id   bigint unsigned default '0'                                not null comment '公司ID',
    acs_id       bigint          default 0                                  not null comment '用户中心id',
    nid          varchar(200)    default ''                                 not null comment '用户中心唯一id',
    username     varchar(200)    default ''                                 not null comment '用户名',
    real_name    varchar(200)    default ''                                 not null comment '真实姓名',
    email        varchar(200)    default ''                                 not null comment '邮箱地址',
    dd_user_id   varchar(200)    default ''                                 not null comment '钉钉id',
    updated_time bigint          default 0                                  not null comment '修改时间',
    created_time bigint          default 0                                  not null comment '创建时间',
    delete_time  bigint          default 0                                  not null comment '删除时间',
    secret_key   varchar(200)    default '3edd46c3cd5b870d303a11fabb3d0f16' not null comment '密钥',
    white_list   int             default 0                                  not null comment '白名单，如果为1说明可以使用'
)CHARACTER SET utf8mb4
  COLLATE utf8mb4_0900_ai_ci comment '用户表';
