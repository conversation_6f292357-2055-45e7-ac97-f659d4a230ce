drop table if exists python_nodes;
create table python_nodes (
    id bigint unsigned not null primary key,
    name var<PERSON><PERSON>(200) default '' not null comment '名称',
    remark varchar(200) default '' not null comment '备注信息',
    copyable bool default false not null comment '是否可复制',
    app_id bigint default 0 not null comment '所属应用id',
    code text not null comment 'python代码',
    result_var varchar(200) default '' not null comment '结果变量名',
    path_tag_var varchar(200) default '' not null comment '路径变量名',
    macro_list text not null comment '宏列表',
    create_user_id bigint default 0 not null comment '创建人',
    updated_time bigint default 0 not null comment '修改时间',
    created_time bigint default 0 not null comment '创建时间',
    delete_time bigint default 0 not null comment '删除时间'
) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci comment 'python节点表';

alter table runner_record add column python_node_id bigint default 0 not null comment 'python节点id' after connector_id;