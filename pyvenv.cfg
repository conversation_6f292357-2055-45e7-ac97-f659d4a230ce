home = /opt/homebrew/Cellar/python@3.11/3.11.9/bin
implementation = CPython
version_info = 3.11.9.final.0
virtualenv = 20.30.0
include-system-site-packages = false
base-prefix = /opt/homebrew/Cellar/python@3.11/3.11.9/Frameworks/Python.framework/Versions/3.11
base-exec-prefix = /opt/homebrew/Cellar/python@3.11/3.11.9/Frameworks/Python.framework/Versions/3.11
base-executable = /opt/homebrew/Cellar/python@3.11/3.11.9/Frameworks/Python.framework/Versions/3.11/bin/python3.11
