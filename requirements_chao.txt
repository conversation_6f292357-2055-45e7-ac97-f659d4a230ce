aggdraw==1.3.18.post0
aiobotocore @ file:///private/var/folders/nz/j6p8yfhx1mv_0grj5xl4650h0000gp/T/abs_71xswk40o_/croot/aiobotocore_1682537536268/work
aiofiles==24.1.0
aiohappyeyeballs==2.4.4
aiohttp==3.11.12
aioitertools @ file:///tmp/build/80754af9/aioitertools_1607109665762/work
aiolimiter==1.1.0
aiosignal @ file:///tmp/build/80754af9/aiosignal_1637843061372/work
aiosqlite @ file:///private/var/folders/nz/j6p8yfhx1mv_0grj5xl4650h0000gp/T/abs_3d75lecab1/croot/aiosqlite_1683773918307/work
alabaster @ file:///home/<USER>/src/ci/alabaster_1611921544520/work
alembic==1.13.1
altair==5.1.1
anaconda-catalogs @ file:///private/var/folders/nz/j6p8yfhx1mv_0grj5xl4650h0000gp/T/abs_e8tmw882qa/croot/anaconda-catalogs_1685727305051/work
anaconda-client==1.12.0
anaconda-navigator==2.4.2
anaconda-project @ file:///Users/<USER>/work/recipes/ci_py311/anaconda-project_1677964558977/work
annotated-types==0.7.0
anthropic==0.49.0
antlr4-python3-runtime==4.9.3
anyio==4.9.0
anytree==2.12.1
appdirs==1.4.4
applaunchservices @ file:///Users/<USER>/work/recipes/ci_py311/applaunchservices_1677955996025/work
appnope @ file:///Users/<USER>/work/recipes/ci_py311/appnope_1677917710869/work
appscript @ file:///Users/<USER>/work/recipes/ci_py311/appscript_1677956964648/work
argon2-cffi @ file:///opt/conda/conda-bld/argon2-cffi_1645000214183/work
argon2-cffi-bindings @ file:///Users/<USER>/work/recipes/ci_py311/argon2-cffi-bindings_1677915727169/work
arrow @ file:///Users/<USER>/work/recipes/ci_py311/arrow_1677931434012/work
asgiref==3.7.2
astroid @ file:///Users/<USER>/work/recipes/ci_py311/astroid_1677926110661/work
astropy @ file:///Users/<USER>/work/recipes/ci_py311_2/astropy_1678994125362/work
asttokens @ file:///opt/conda/conda-bld/asttokens_1646925590279/work
async-timeout @ file:///Users/<USER>/work/recipes/ci_py311/async-timeout_1677925030615/work
atomicwrites==1.4.0
attrs==23.2.0
auth0-python==4.8.0
Authlib==1.3.1
autograd==1.6.2
Automat @ file:///tmp/build/80754af9/automat_1600298431173/work
autopep8 @ file:///opt/conda/conda-bld/autopep8_1650463822033/work
azure-common==1.1.28
azure-core==1.30.2
azure-identity==1.17.1
azure-search-documents==11.4.0
azure-storage-blob==12.20.0
Babel @ file:///Users/<USER>/work/recipes/ci_py311/babel_1677920677615/work
backcall @ file:///home/<USER>/src/ci/backcall_1611930011877/work
backoff==2.2.1
backports.functools-lru-cache @ file:///tmp/build/80754af9/backports.functools_lru_cache_1618170165463/work
backports.tempfile @ file:///home/<USER>/recipes/ci/backports.tempfile_1610991236607/work
backports.weakref==1.0.post1
bcrypt==4.0.1
beartype==0.18.5
beautifulsoup4==4.12.3
binaryornot @ file:///tmp/build/80754af9/binaryornot_1617751525010/work
black==25.1.0
bleach @ file:///opt/conda/conda-bld/bleach_1641577558959/work
blinker==1.9.0
blosc2==2.0.0
bokeh @ file:///private/var/folders/k1/30mswbxs7r1g6zwn8y4fyt500000gp/T/abs_7552pi2mmm/croot/bokeh_1690546093040/work
boltons @ file:///Users/<USER>/work/recipes/ci_py311/boltons_1677965141748/work
boto3==1.37.37
botocore==1.37.37
Bottleneck @ file:///Users/<USER>/work/recipes/ci_py311/bottleneck_1677925122241/work
Brotli==1.1.0
brotlipy==0.7.0
build==1.2.2.post1
cachetools==5.3.3
certifi==2025.1.31
cffi @ file:///Users/<USER>/work/recipes/ci_py311/cffi_1677903595907/work
chardet @ file:///Users/<USER>/work/recipes/ci_py311/chardet_1677931647221/work
chargebee==2.38.1
charset-normalizer @ file:///tmp/build/80754af9/charset-normalizer_1630003229654/work
chroma-hnswlib==0.7.6
chromadb==0.5.23
click==8.1.7
cloudpickle @ file:///private/var/folders/nz/j6p8yfhx1mv_0grj5xl4650h0000gp/T/abs_da31odypvn/croot/cloudpickle_1683040013858/work
clyent==1.2.2
cohere==5.13.12
colorama @ file:///Users/<USER>/work/recipes/ci_py311/colorama_1677925183444/work
colorcet @ file:///Users/<USER>/work/recipes/ci_py311/colorcet_1677936559489/work
coloredlogs==15.0.1
comm @ file:///Users/<USER>/work/recipes/ci_py311/comm_1677919149446/work
conda @ file:///private/var/folders/nz/j6p8yfhx1mv_0grj5xl4650h0000gp/T/abs_4eoq6pno4r/croot/conda_1692724504574/work
conda-build @ file:///private/var/folders/nz/j6p8yfhx1mv_0grj5xl4650h0000gp/T/abs_f467bc822c/croot/conda-build_1690381801495/work
conda-content-trust @ file:///tmp/build/80754af9/conda-content-trust_1617045594566/work
conda-libmamba-solver @ file:///private/var/folders/nz/j6p8yfhx1mv_0grj5xl4650h0000gp/T/abs_48qgqxi1ys/croot/conda-libmamba-solver_1685032355439/work/src
conda-pack @ file:///tmp/build/80754af9/conda-pack_1611163042455/work
conda-package-handling @ file:///private/var/folders/k1/30mswbxs7r1g6zwn8y4fyt500000gp/T/abs_fc4cx8vjhj/croot/conda-package-handling_1690999937094/work
conda-repo-cli==1.0.41
conda-token @ file:///Users/<USER>/miniconda3/envs/c3i/conda-bld/conda-token_1662660369760/work
conda-verify==3.4.2
conda_index @ file:///Users/<USER>/work/recipes/ci_py311/conda-index_1677970042779/work
conda_package_streaming @ file:///private/var/folders/k1/30mswbxs7r1g6zwn8y4fyt500000gp/T/abs_aecpaup22q/croot/conda-package-streaming_1690987978274/work
constantly==15.1.0
contourpy @ file:///Users/<USER>/work/recipes/ci_py311/contourpy_1677925208456/work
cookiecutter @ file:///opt/conda/conda-bld/cookiecutter_1649151442564/work
cramjam==2.8.3
crewai==0.114.0
crewai-adapters==0.1.2
crewai-tools==0.17.0
cryptography==44.0.0
cssselect==1.1.0
cycler @ file:///tmp/build/80754af9/cycler_1637851556182/work
Cython==3.0.12
cytoolz @ file:///Users/<USER>/work/recipes/ci_py311/cytoolz_1677931761799/work
dask @ file:///private/var/folders/nz/j6p8yfhx1mv_0grj5xl4650h0000gp/T/abs_f3jad8spw3/croot/dask-core_1686782923467/work
dataclasses-json==0.5.14
datasets @ file:///private/var/folders/nz/j6p8yfhx1mv_0grj5xl4650h0000gp/T/abs_5eqct1blor/croot/datasets_1684482935720/work
datashader @ file:///private/var/folders/nz/j6p8yfhx1mv_0grj5xl4650h0000gp/T/abs_8f3fxewux9/croot/datashader_1689587619365/work
datashape==0.5.4
datashaper==0.0.49
debugpy @ file:///private/var/folders/k1/30mswbxs7r1g6zwn8y4fyt500000gp/T/abs_563_nwtkoc/croot/debugpy_1690905063850/work
decorator==4.4.2
defusedxml @ file:///tmp/build/80754af9/defusedxml_1615228127516/work
Deprecated==1.2.14
deprecation==2.1.0
devtools==0.12.2
diff-match-patch @ file:///Users/<USER>/demo/mc3/conda-bld/diff-match-patch_1630511840874/work
dill==0.3.8
diskcache==5.6.3
distributed @ file:///private/var/folders/nz/j6p8yfhx1mv_0grj5xl4650h0000gp/T/abs_46b2z6ud_6/croot/distributed_1686866053959/work
distro==1.8.0
dnspython==2.4.2
docker==7.1.0
docling==2.30.0
docling-core==2.27.0
docling-ibm-models==3.4.1
docling-parse==4.0.0
docopt==0.6.2
docstring-to-markdown @ file:///Users/<USER>/work/recipes/ci_py311/docstring-to-markdown_1677931891483/work
docstring_parser==0.16
docutils==0.21.2
docx==0.2.4
docx2txt==0.8
dydantic==0.0.8
easyocr==1.7.2
embedchain==0.1.128
emoji==2.8.0
entrypoints @ file:///Users/<USER>/work/recipes/ci_py311/entrypoints_1677911798787/work
environs==11.0.0
et-xmlfile==1.1.0
executing==2.0.1
faiss==1.7.4
fastapi==0.115.9
fastavro==1.10.0
fastjsonschema @ file:///Users/<USER>/work/recipes/ci_py311_2/python-fastjsonschema_1678996913062/work
fastparquet==2024.5.0
ffmpy==0.3.1
filelock @ file:///Users/<USER>/work/recipes/ci_py311/filelock_1677909105322/work
filetype==1.2.0
flake8 @ file:///Users/<USER>/work/recipes/ci_py311/flake8_1677931981927/work
Flask @ file:///Users/<USER>/work/recipes/ci_py311/flask_1677937489551/work
flatbuffers==23.5.26
fonttools==4.25.0
frozenlist @ file:///Users/<USER>/work/recipes/ci_py311/frozenlist_1677923867353/work
fsspec==2025.2.0
future @ file:///Users/<USER>/work/recipes/ci_py311_2/future_1678994664110/work
gensim==4.3.2
gitdb==4.0.11
GitPython==3.1.43
glob2 @ file:///home/<USER>/recipes/ci/glob2_1610991677669/work
gmpy2 @ file:///Users/<USER>/work/recipes/ci_py311/gmpy2_1677937751357/work
google-api-core==2.24.2
google-api-python-client==2.119.0
google-auth==2.28.1
google-auth-httplib2==0.2.0
google-cloud-aiplatform==1.89.0
google-cloud-bigquery==3.23.1
google-cloud-core==2.4.1
google-cloud-resource-manager==1.14.2
google-cloud-storage==2.19.0
google-crc32c==1.5.0
google-pasta==0.2.0
google-resumable-media==2.7.2
googleapis-common-protos==1.70.0
googlemaps==4.10.0
gptcache==0.1.43
gradio==3.42.0
gradio_client==0.5.0
graphrag==0.1.1
graspologic==3.4.1
graspologic-native==1.2.1
greenlet @ file:///Users/<USER>/work/recipes/ci_py311/greenlet_1677926210411/work
grpc-google-iam-v1==0.14.2
grpcio==1.71.0
grpcio-health-checking==1.71.0
grpcio-status==1.62.2
grpcio-tools==1.71.0
h11==0.14.0
h2==4.2.0
h5py @ file:///Users/<USER>/work/recipes/ci_py311/h5py_1677937901660/work
HeapDict @ file:///Users/<USER>/demo/mc3/conda-bld/heapdict_1630598515714/work
holoviews @ file:///private/var/folders/nz/j6p8yfhx1mv_0grj5xl4650h0000gp/T/abs_f0kn6h75hh/croot/holoviews_1690477580363/work
hpack==4.1.0
html2text==2020.1.16
httpcore==1.0.7
httplib2==0.22.0
httptools==0.6.0
httpx==0.28.1
httpx-sse==0.4.0
huggingface-hub==0.30.1
humanfriendly==10.0
hvplot @ file:///private/var/folders/nz/j6p8yfhx1mv_0grj5xl4650h0000gp/T/abs_30omxxom8t/croot/hvplot_1686021066828/work
hyperframe==6.1.0
hyperlink @ file:///tmp/build/80754af9/hyperlink_1610130746837/work
hyppo==0.4.0
id==1.5.0
idna @ file:///Users/<USER>/work/recipes/ci_py311/idna_1677906072337/work
ijson==3.3.0
imagecodecs @ file:///Users/<USER>/work/recipes/ci_py311_2/imagecodecs_1678994839331/work
imageio @ file:///private/var/folders/nz/j6p8yfhx1mv_0grj5xl4650h0000gp/T/abs_c1sh4q1483/croot/imageio_1687264958491/work
imageio-ffmpeg==0.5.1
imagesize @ file:///Users/<USER>/work/recipes/ci_py311/imagesize_1677932611633/work
imbalanced-learn @ file:///private/var/folders/nz/j6p8yfhx1mv_0grj5xl4650h0000gp/T/abs_26_yvb9nba/croot/imbalanced-learn_1685020915768/work
importlib-metadata==6.11.0
importlib-resources==6.0.1
incremental @ file:///tmp/build/80754af9/incremental_1636629750599/work
inflection==0.5.1
iniconfig @ file:///home/<USER>/recipes/ci/iniconfig_1610983019677/work
instructor==1.7.2
intake @ file:///Users/<USER>/work/recipes/ci_py311_2/intake_1678994948878/work
intervaltree @ file:///Users/<USER>/demo/mc3/conda-bld/intervaltree_1630511889664/work
ipykernel @ file:///Users/<USER>/work/recipes/ci_py311/ipykernel_1677921035781/work
ipython==8.12.3
ipython-genutils @ file:///tmp/build/80754af9/ipython_genutils_1606773439826/work
ipywidgets @ file:///private/var/folders/nz/j6p8yfhx1mv_0grj5xl4650h0000gp/T/abs_f07ugy1hvo/croot/ipywidgets_1679394821999/work
isodate==0.6.1
isort @ file:///tmp/build/80754af9/isort_1628603791788/work
itemadapter @ file:///tmp/build/80754af9/itemadapter_1626442940632/work
itemloaders @ file:///opt/conda/conda-bld/itemloaders_1646805235997/work
itsdangerous @ file:///tmp/build/80754af9/itsdangerous_1621432558163/work
jaraco.classes @ file:///tmp/build/80754af9/jaraco.classes_1620983179379/work
jaraco.context==4.3.0
jarvis-tools @ file:///Users/<USER>/git/ads-placement-agents/jarvis/jarvis_tools-0.1-py3-none-any.whl#sha256=9926288240cef0a9c84f38a0a92a38b6514ff0b7ce1cc516bcdcba6a3ccedc1b
jedi @ file:///Users/<USER>/work/recipes/ci_py311_2/jedi_1678994967789/work
jellyfish @ file:///Users/<USER>/work/recipes/ci_py311/jellyfish_1677959705446/work
Jinja2==3.1.5
jinja2-time @ file:///opt/conda/conda-bld/jinja2-time_1649251842261/work
jiter==0.8.2
jmespath @ file:///Users/<USER>/demo/mc3/conda-bld/jmespath_1630583964805/work
joblib==1.4.2
json5==0.12.0
json_repair==0.35.0
jsonlines==3.1.0
jsonpatch==1.33
jsonpickle==4.0.1
jsonpointer==2.1
jsonref==1.1.0
jsonschema==4.23.0
jsonschema-spec==0.2.4
jsonschema-specifications==2023.7.1
jupyter @ file:///Users/<USER>/work/recipes/ci_py311/jupyter_1677932849424/work
jupyter-console @ file:///private/var/folders/nz/j6p8yfhx1mv_0grj5xl4650h0000gp/T/abs_62liw5pns2/croot/jupyter_console_1679999641189/work
jupyter-events @ file:///private/var/folders/nz/j6p8yfhx1mv_0grj5xl4650h0000gp/T/abs_23dk3_r5iy/croot/jupyter_events_1684268066707/work
jupyter-server @ file:///Users/<USER>/work/recipes/ci_py311/jupyter_server_1677919944873/work
jupyter-ydoc @ file:///private/var/folders/nz/j6p8yfhx1mv_0grj5xl4650h0000gp/T/abs_1djmqkjwof/croot/jupyter_ydoc_1683747243427/work
jupyter_client @ file:///Users/<USER>/work/recipes/ci_py311/jupyter_client_1677914181590/work
jupyter_core @ file:///private/var/folders/nz/j6p8yfhx1mv_0grj5xl4650h0000gp/T/abs_d1sy1hlz9t/croot/jupyter_core_1679906585151/work
jupyter_server_fileid @ file:///private/var/folders/nz/j6p8yfhx1mv_0grj5xl4650h0000gp/T/abs_a5j1mo_1cs/croot/jupyter_server_fileid_1684273608144/work
jupyter_server_ydoc @ file:///private/var/folders/nz/j6p8yfhx1mv_0grj5xl4650h0000gp/T/abs_47q6o0w705/croot/jupyter_server_ydoc_1686767400324/work
jupyterlab @ file:///private/var/folders/nz/j6p8yfhx1mv_0grj5xl4650h0000gp/T/abs_a5omaxlzc0/croot/jupyterlab_1686179674589/work
jupyterlab-pygments @ file:///tmp/build/80754af9/jupyterlab_pygments_1601490720602/work
jupyterlab-widgets @ file:///private/var/folders/nz/j6p8yfhx1mv_0grj5xl4650h0000gp/T/abs_65wwn9cwab/croot/jupyterlab_widgets_1679055283460/work
jupyterlab_server @ file:///private/var/folders/nz/j6p8yfhx1mv_0grj5xl4650h0000gp/T/abs_0fmfqwbrd7/croot/jupyterlab_server_1680792517631/work
keyring @ file:///private/var/folders/nz/j6p8yfhx1mv_0grj5xl4650h0000gp/T/abs_8bd22k84zo/croot/keyring_1678999224442/work
kiwisolver @ file:///Users/<USER>/work/recipes/ci_py311/kiwisolver_1677925326358/work
kubernetes==29.0.0
lancedb==0.9.0
langchain==0.3.20
langchain-anthropic==0.3.10
langchain-cohere==0.3.5
langchain-community==0.3.16
langchain-core==0.3.54
langchain-experimental==0.3.4
langchain-google-vertexai==2.0.20
langchain-mcp-adapters==0.0.7
langchain-openai==0.2.14
langchain-text-splitters==0.3.6
langgraph==0.3.31
langgraph-checkpoint==2.0.23
langgraph-checkpoint-postgres==2.0.19
langgraph-prebuilt==0.1.8
langgraph-sdk==0.1.53
langmem==0.0.24
langsmith==0.3.32
latex2mathml==3.77.0
lazy-object-proxy==1.9.0
lazy_loader @ file:///private/var/folders/nz/j6p8yfhx1mv_0grj5xl4650h0000gp/T/abs_9c4sl77tg1/croot/lazy_loader_1687264096938/work
libarchive-c @ file:///tmp/build/80754af9/python-libarchive-c_1617780486945/work
libmambapy @ file:///private/var/folders/nz/j6p8yfhx1mv_0grj5xl4650h0000gp/T/abs_ba7rom_bbt/croot/mamba-split_1680092817644/work/libmambapy
linkify-it-py @ file:///Users/<USER>/work/recipes/ci_py311/linkify-it-py_1677973036983/work
linkup-sdk==0.2.3
litellm==1.67.0
llvmlite==0.43.0
lmdb @ file:///private/var/folders/nz/j6p8yfhx1mv_0grj5xl4650h0000gp/T/abs_6fumkuh_c0/croot/python-lmdb_1682522347231/work
locket @ file:///Users/<USER>/work/recipes/ci_py311/locket_1677925419801/work
lxml @ file:///private/var/folders/nz/j6p8yfhx1mv_0grj5xl4650h0000gp/T/abs_bbjv2ox7t8/croot/lxml_1679646469466/work
lz4 @ file:///private/var/folders/nz/j6p8yfhx1mv_0grj5xl4650h0000gp/T/abs_f0mtitgo6y/croot/lz4_1686063770247/work
Mako==1.3.5
Markdown @ file:///Users/<USER>/work/recipes/ci_py311/markdown_1677932925449/work
markdown-it-py @ file:///private/var/folders/nz/j6p8yfhx1mv_0grj5xl4650h0000gp/T/abs_43l_4ajkho/croot/markdown-it-py_1684279912406/work
marko==2.1.3
MarkupSafe @ file:///Users/<USER>/work/recipes/ci_py311/markupsafe_1677914270710/work
marshmallow==3.20.1
matplotlib==3.9.2
matplotlib-inline @ file:///Users/<USER>/work/recipes/ci_py311/matplotlib-inline_1677918241899/work
mccabe @ file:///opt/conda/conda-bld/mccabe_1644221741721/work
mcp==1.6.0
mcpadapt==0.0.19
mdit-py-plugins @ file:///Users/<USER>/work/recipes/ci_py311/mdit-py-plugins_1677995322132/work
mdurl @ file:///Users/<USER>/work/recipes/ci_py311/mdurl_1677942260967/work
mem0ai==0.1.93
mistune==3.1.2
mmh3==4.1.0
mock==4.0.3
monotonic==1.6
more-itertools @ file:///tmp/build/80754af9/more-itertools_1637733554872/work
motor==3.3.1
moviepy==1.0.3
mpire==2.10.2
mpmath @ file:///private/var/folders/k1/30mswbxs7r1g6zwn8y4fyt500000gp/T/abs_17iu6a8a3m/croot/mpmath_1690848269369/work
msal==1.29.0
msal-extensions==1.2.0
msgpack==1.1.0
multidict @ file:///Users/<USER>/work/recipes/ci_py311/multidict_1677923908690/work
multipledispatch @ file:///Users/<USER>/work/recipes/ci_py311/multipledispatch_1677960800437/work
multiprocess==0.70.16
munkres==1.1.4
mutagen==1.47.0
mypy-extensions==0.4.3
navigator-updater==0.4.0
nbclassic @ file:///private/var/folders/nz/j6p8yfhx1mv_0grj5xl4650h0000gp/T/abs_d6oy9w0m3l/croot/nbclassic_1681756176477/work
nbclient @ file:///Users/<USER>/work/recipes/ci_py311/nbclient_1677916908988/work
nbconvert==7.16.6
nbformat @ file:///Users/<USER>/work/recipes/ci_py311/nbformat_1677914501406/work
nest-asyncio @ file:///Users/<USER>/work/recipes/ci_py311/nest-asyncio_1677912430289/work
networkx @ file:///private/var/folders/nz/j6p8yfhx1mv_0grj5xl4650h0000gp/T/abs_b9af3smw_7/croot/networkx_1690562010704/work
nh3==0.2.21
ninja==********
nltk @ file:///private/var/folders/nz/j6p8yfhx1mv_0grj5xl4650h0000gp/T/abs_ebiuq9880w/croot/nltk_1688114154971/work
nodeenv==1.8.0
notebook @ file:///private/var/folders/k1/30mswbxs7r1g6zwn8y4fyt500000gp/T/abs_37zamp3vxi/croot/notebook_1690984825122/work
notebook_shim @ file:///Users/<USER>/work/recipes/ci_py311/notebook-shim_1677921216909/work
numba==0.60.0
numexpr @ file:///private/var/folders/nz/j6p8yfhx1mv_0grj5xl4650h0000gp/T/abs_76yyu1p9jk/croot/numexpr_1683221830860/work
numpy==1.26.4
numpydoc @ file:///Users/<USER>/work/recipes/ci_py311/numpydoc_1677960919550/work
oauthlib==3.2.2
omegaconf==2.3.0
onnxruntime==1.15.1
openai==1.68.2
openapi-core==0.18.1
openapi-schema-validator==0.6.2
openapi-spec-validator==0.6.0
opencv-python==*********
opencv-python-headless==*********
openpyxl==3.1.5
opentelemetry-api==1.32.1
opentelemetry-exporter-otlp-proto-common==1.32.1
opentelemetry-exporter-otlp-proto-grpc==1.32.1
opentelemetry-exporter-otlp-proto-http==1.32.1
opentelemetry-instrumentation==0.53b1
opentelemetry-instrumentation-asgi==0.53b1
opentelemetry-instrumentation-fastapi==0.53b1
opentelemetry-proto==1.32.1
opentelemetry-sdk==1.32.1
opentelemetry-semantic-conventions==0.53b1
opentelemetry-util-http==0.53b1
ordered-set==4.1.0
orjson==3.10.16
ormsgpack==1.9.0
outcome==1.3.0.post0
overrides==7.4.0
packaging==24.2
pandas==2.2.2
pandocfilters @ file:///opt/conda/conda-bld/pandocfilters_1643405455980/work
panel @ file:///private/var/folders/k1/30mswbxs7r1g6zwn8y4fyt500000gp/T/abs_fckdqsngkv/croot/panel_1690822225257/work
param @ file:///private/var/folders/nz/j6p8yfhx1mv_0grj5xl4650h0000gp/T/abs_cfp588uo3i/croot/param_1684915323490/work
parse==1.19.1
parsel @ file:///Users/<USER>/work/recipes/ci_py311/parsel_1678068232273/work
parso @ file:///opt/conda/conda-bld/parso_1641458642106/work
partd @ file:///opt/conda/conda-bld/partd_1647245470509/work
pathable==0.4.3
pathlib @ file:///Users/<USER>/demo/mc3/conda-bld/pathlib_1629713961906/work
pathos==0.3.2
pathspec @ file:///Users/<USER>/work/recipes/ci_py311_2/pathspec_1678995598596/work
patsy==0.5.6
pdfminer.six==20231228
pdfplumber==0.11.5
pep8==1.7.1
pexpect @ file:///tmp/build/80754af9/pexpect_1605563209008/work
pickleshare @ file:///tmp/build/80754af9/pickleshare_1606932040724/work
pillow==10.4.0
pipreqs==0.5.0
pkginfo @ file:///private/var/folders/nz/j6p8yfhx1mv_0grj5xl4650h0000gp/T/abs_d1oq9rhye6/croot/pkginfo_1679431178842/work
platformdirs==4.3.7
plotly @ file:///Users/<USER>/work/recipes/ci_py311/plotly_1677953301864/work
pluggy==1.5.0
ply==3.11
pooch @ file:///tmp/build/80754af9/pooch_1623324770023/work
portalocker==2.10.0
posthog==3.11.0
POT==0.9.4
pox==0.3.4
poyo @ file:///tmp/build/80754af9/poyo_1617751526755/work
ppft==*******
prance==*********
proglog==0.1.10
prometheus-client @ file:///Users/<USER>/work/recipes/ci_py311_2/prometheus_client_1678996808082/work
prompt-toolkit @ file:///Users/<USER>/work/recipes/ci_py311/prompt-toolkit_1677918689663/work
propcache==0.2.1
Protego @ file:///tmp/build/80754af9/protego_1598657180827/work
proto-plus==1.26.1
protobuf==5.29.4
psd-tools==1.9.34
psutil==6.0.0
psycopg==3.2.6
psycopg-binary==3.2.6
psycopg-pool==3.2.6
psycopg2-binary==2.9.10
ptyprocess @ file:///tmp/build/80754af9/ptyprocess_1609355006118/work/dist/ptyprocess-0.7.0-py2.py3-none-any.whl
pulsar-client==3.2.0
pure-eval @ file:///opt/conda/conda-bld/pure_eval_1646925070566/work
py==1.11.0
py-cpuinfo @ file:///Users/<USER>/demo/mc3/conda-bld/py-cpuinfo_1629480366017/work
pyaml-env==1.2.1
pyarmor==9.1.6
pyarmor.cli.core==7.6.6
pyarrow==15.0.0
pyasn1 @ file:///Users/<USER>/demo/mc3/conda-bld/pyasn1_1629708007385/work
pyasn1-modules==0.2.8
pyclipper==1.3.0.post6
pycocotools==2.0.8
pycodestyle @ file:///Users/<USER>/work/recipes/ci_py311/pycodestyle_1677927047034/work
pycosat @ file:///Users/<USER>/work/recipes/ci_py311/pycosat_1677933552468/work
pycparser @ file:///tmp/build/80754af9/pycparser_1636541352034/work
pycryptodomex==3.20.0
pyct @ file:///Users/<USER>/work/recipes/ci_py311/pyct_1677933596803/work
pycurl==7.45.2
pydantic==2.10.6
pydantic-settings==2.7.1
pydantic_core==2.27.2
PyDispatcher==2.0.5
pydocstyle @ file:///Users/<USER>/work/recipes/ci_py311/pydocstyle_1677933616104/work
pydub==0.25.1
pyerfa @ file:///Users/<USER>/work/recipes/ci_py311/pyerfa_1677933632816/work
pyflakes @ file:///Users/<USER>/work/recipes/ci_py311/pyflakes_1677927066386/work
pygame==2.6.0
PyGithub==1.59.1
Pygments @ file:///private/var/folders/nz/j6p8yfhx1mv_0grj5xl4650h0000gp/T/abs_29bs9f_dh9/croot/pygments_1684279974747/work
PyJWT==2.10.1
pylance==0.13.0
pylatexenc==2.10
pylint @ file:///Users/<USER>/work/recipes/ci_py311/pylint_1677933699245/work
pylint-venv @ file:///Users/<USER>/work/recipes/ci_py311/pylint-venv_1677961443839/work
pyls-spyder==0.4.0
pymongo==4.6.0
PyNaCl==1.5.0
pynndescent==0.5.13
pyobjc-core @ file:///Users/<USER>/work/recipes/ci_py311/pyobjc-core_1678112643033/work
pyobjc-framework-Cocoa @ file:///Users/<USER>/work/recipes/ci_py311/pyobjc-framework-cocoa_1678112805655/work
pyobjc-framework-CoreServices @ file:///Users/<USER>/work/recipes/ci_py311/pyobjc-framework-coreservices_1678113537167/work
pyobjc-framework-FSEvents @ file:///Users/<USER>/work/recipes/ci_py311/pyobjc-framework-fsevents_1678112996782/work
pyodbc @ file:///Users/<USER>/work/recipes/ci_py311/pyodbc_1678001241548/work
pyOpenSSL @ file:///private/var/folders/k1/30mswbxs7r1g6zwn8y4fyt500000gp/T/abs_b8whqav6qm/croot/pyopenssl_1690223428943/work
pyparsing @ file:///Users/<USER>/work/recipes/ci_py311/pyparsing_1677910832141/work
pypdf==5.2.0
PyPDF2==3.0.1
pypdfium2==4.30.1
PyPika==0.48.9
pyproject_hooks==1.1.0
PyQt5==5.15.11
PyQt5-Qt5==5.15.14
PyQt5_sip==12.15.0
PyQtWebEngine==5.15.7
PyQtWebEngine-Qt5==5.15.14
pyright==1.1.363
pyrsistent @ file:///Users/<USER>/work/recipes/ci_py311/pyrsistent_1677909782145/work
pysbd==0.3.4
PySocks @ file:///Users/<USER>/work/recipes/ci_py311/pysocks_1677906386870/work
pytest==8.2.1
python-bidi==0.6.6
python-dateutil @ file:///tmp/build/80754af9/python-dateutil_1626374649649/work
python-docx==1.1.2
python-dotenv==1.0.1
python-json-logger @ file:///private/var/folders/nz/j6p8yfhx1mv_0grj5xl4650h0000gp/T/abs_c3baq2ko4j/croot/python-json-logger_1683823815343/work
python-lsp-black==2.0.0
python-lsp-jsonrpc==1.0.0
python-lsp-server @ file:///private/var/folders/nz/j6p8yfhx1mv_0grj5xl4650h0000gp/T/abs_73tk9oa5lj/croot/python-lsp-server_1681930403042/work
python-magic==0.4.27
python-multipart==0.0.6
python-pptx==1.0.2
python-slugify @ file:///tmp/build/80754af9/python-slugify_1620405669636/work
python-snappy @ file:///Users/<USER>/work/recipes/ci_py311/python-snappy_1677954153933/work
pytoolconfig @ file:///Users/<USER>/work/recipes/ci_py311/pytoolconfig_1677952331058/work
pytube==15.0.0
pytz==2024.2
pyvis==0.3.2
pyviz-comms @ file:///private/var/folders/nz/j6p8yfhx1mv_0grj5xl4650h0000gp/T/abs_54a2e0jluq/croot/pyviz_comms_1685030719530/work
PyWavelets @ file:///Users/<USER>/work/recipes/ci_py311/pywavelets_1677934749412/work
PyYAML==6.0.1
pyzmq @ file:///Users/<USER>/work/recipes/ci_py311/pyzmq_1677912759914/work
QDarkStyle @ file:///tmp/build/80754af9/qdarkstyle_1617386714626/work
qdrant-client==1.13.2
qstylizer @ file:///Users/<USER>/work/recipes/ci_py311/qstylizer_1678072198813/work/dist/qstylizer-0.2.2-py2.py3-none-any.whl
QtAwesome @ file:///Users/<USER>/work/recipes/ci_py311/qtawesome_1677961781784/work
qtconsole @ file:///private/var/folders/nz/j6p8yfhx1mv_0grj5xl4650h0000gp/T/abs_86g4aht18r/croot/qtconsole_1681394233851/work
QtPy @ file:///Users/<USER>/work/recipes/ci_py311/qtpy_1677925820115/work
queuelib==1.5.0
ratelimiter==1.2.0.post0
readme_renderer==44.0
referencing==0.30.2
regex==2024.11.6
requests==2.32.3
requests-file @ file:///Users/<USER>/demo/mc3/conda-bld/requests-file_1629455781986/work
requests-oauthlib==2.0.0
requests-toolbelt @ file:///private/var/folders/k1/30mswbxs7r1g6zwn8y4fyt500000gp/T/abs_3fee1fr2ex/croot/requests-toolbelt_1690874011813/work
responses @ file:///tmp/build/80754af9/responses_1619800270522/work
retry==0.9.2
rfc3339-validator @ file:///private/var/folders/nz/j6p8yfhx1mv_0grj5xl4650h0000gp/T/abs_76ae5cu30h/croot/rfc3339-validator_1683077051957/work
rfc3986==2.0.0
rfc3986-validator @ file:///private/var/folders/nz/j6p8yfhx1mv_0grj5xl4650h0000gp/T/abs_d0l5zd97kt/croot/rfc3986-validator_1683058998431/work
rich==13.7.1
rope @ file:///Users/<USER>/work/recipes/ci_py311/rope_1677934821109/work
rpds-py==0.10.4
rsa==4.9
rtree==1.4.0
ruamel-yaml-conda @ file:///Users/<USER>/work/recipes/ci_py311/ruamel_yaml_1677961911260/work
ruamel.yaml @ file:///Users/<USER>/work/recipes/ci_py311/ruamel.yaml_1677934845850/work
s3fs @ file:///private/var/folders/nz/j6p8yfhx1mv_0grj5xl4650h0000gp/T/abs_94qu8izf0w/croot/s3fs_1682551484893/work
s3transfer==0.11.5
sacremoses @ file:///tmp/build/80754af9/sacremoses_1633107328213/work
safetensors==0.5.3
sagemaker==2.243.2
sagemaker-core==1.0.29
schema==0.7.7
scikit-image @ file:///private/var/folders/nz/j6p8yfhx1mv_0grj5xl4650h0000gp/T/abs_12rg0hdzgk/croot/scikit-image_1682528304529/work
scikit-learn==1.5.1
scipy==1.12.0
scrapegraph_py==1.12.0
Scrapy @ file:///Users/<USER>/work/recipes/ci_py311/scrapy_1678002824834/work
seaborn==0.13.2
selenium==4.21.0
semantic-version==2.10.0
semchunk==2.2.2
semver==3.0.2
Send2Trash @ file:///tmp/build/80754af9/send2trash_1632406701022/work
serpapi==0.1.5
service-identity @ file:///Users/<USER>/demo/mc3/conda-bld/service_identity_1629460757137/work
shapely==2.0.4
shellingham==1.5.4
sip @ file:///Users/<USER>/work/recipes/ci_py311/sip_1677923661665/work
six @ file:///tmp/build/80754af9/six_1644875935023/work
smart-open @ file:///Users/<USER>/work/recipes/ci_py311/smart_open_1677955621457/work
smdebug-rulesconfig==1.0.1
smmap==5.0.1
sniffio==1.3.1
snowballstemmer @ file:///tmp/build/80754af9/snowballstemmer_1637937080595/work
sortedcontainers @ file:///tmp/build/80754af9/sortedcontainers_1623949099177/work
soupsieve @ file:///private/var/folders/nz/j6p8yfhx1mv_0grj5xl4650h0000gp/T/abs_cfrx9hmp3g/croot/soupsieve_1680518480034/work
Sphinx @ file:///Users/<USER>/work/recipes/ci_py311/sphinx_1677955655588/work
sphinxcontrib-applehelp @ file:///home/<USER>/src/ci/sphinxcontrib-applehelp_1611920841464/work
sphinxcontrib-devhelp @ file:///home/<USER>/src/ci/sphinxcontrib-devhelp_1611920923094/work
sphinxcontrib-htmlhelp @ file:///tmp/build/80754af9/sphinxcontrib-htmlhelp_1623945626792/work
sphinxcontrib-jsmath @ file:///home/<USER>/src/ci/sphinxcontrib-jsmath_1611920942228/work
sphinxcontrib-qthelp @ file:///home/<USER>/src/ci/sphinxcontrib-qthelp_1611921055322/work
sphinxcontrib-serializinghtml @ file:///tmp/build/80754af9/sphinxcontrib-serializinghtml_1624451540180/work
spider-client==0.1.26
spyder @ file:///private/var/folders/nz/j6p8yfhx1mv_0grj5xl4650h0000gp/T/abs_f02k_edsgq/croot/spyder_1681934090757/work
spyder-kernels @ file:///private/var/folders/nz/j6p8yfhx1mv_0grj5xl4650h0000gp/T/abs_18_uk_uzcm/croot/spyder-kernels_1681309301436/work
SQLAlchemy==2.0.38
sqlite-vec==0.1.6
sse-starlette==2.2.1
stack-data @ file:///opt/conda/conda-bld/stack_data_1646927590127/work
starlette==0.45.3
statsmodels==0.14.2
svgpathtools==1.6.1
svgwrite==1.4.3
swifter==1.4.0
sympy==1.13.1
tables @ file:///private/var/folders/nz/j6p8yfhx1mv_0grj5xl4650h0000gp/T/abs_2d3fzevm9d/croot/pytables_1685123231215/work
tabulate==0.9.0
TBB==0.2
tblib @ file:///Users/<USER>/demo/mc3/conda-bld/tblib_1629402031467/work
tenacity==9.0.0
termcolor==2.4.0
terminado @ file:///Users/<USER>/work/recipes/ci_py311/terminado_1677918849903/work
text-unidecode @ file:///Users/<USER>/demo/mc3/conda-bld/text-unidecode_1629401354553/work
textdistance @ file:///tmp/build/80754af9/textdistance_1612461398012/work
textual==0.70.0
threadpoolctl==3.5.0
three-merge @ file:///tmp/build/80754af9/three-merge_1607553261110/work
tifffile @ file:///tmp/build/80754af9/tifffile_1627275862826/work
tiktoken==0.7.0
tinycss2 @ file:///Users/<USER>/work/recipes/ci_py311/tinycss2_1677917352983/work
tldextract @ file:///opt/conda/conda-bld/tldextract_1646638314385/work
tokenizers==0.21.1
toml @ file:///tmp/build/80754af9/toml_1616166611790/work
tomli==2.2.1
tomli_w==1.2.0
tomlkit @ file:///Users/<USER>/work/recipes/ci_py311/tomlkit_1677911131859/work
toolz @ file:///Users/<USER>/work/recipes/ci_py311/toolz_1677925870232/work
torch==2.6.0
torchvision==0.21.0
tornado @ file:///private/var/folders/k1/30mswbxs7r1g6zwn8y4fyt500000gp/T/abs_28d93aezp2/croot/tornado_1690848278715/work
tqdm @ file:///private/var/folders/nz/j6p8yfhx1mv_0grj5xl4650h0000gp/T/abs_ac7zic_tin/croot/tqdm_1679561870178/work
traitlets @ file:///Users/<USER>/work/recipes/ci_py311/traitlets_1677911650502/work
transformers==4.51.1
trio==0.25.1
trio-websocket==0.11.1
trustcall==0.0.39
twine==6.1.0
Twisted @ file:///private/var/folders/nz/j6p8yfhx1mv_0grj5xl4650h0000gp/T/abs_3c_lnc4s5c/croot/twisted_1683796895946/work
typer==0.12.5
types-requests==2.32.0.20241016
typing-inspect==0.9.0
typing_extensions==4.12.2
tzdata==2024.1
uc-micro-py @ file:///Users/<USER>/work/recipes/ci_py311/uc-micro-py_1677963537430/work
ujson @ file:///Users/<USER>/work/recipes/ci_py311/ujson_1677927397272/work
umap-learn==0.5.6
Unidecode @ file:///tmp/build/80754af9/unidecode_1614712377438/work
unstructured==0.10.12
uritemplate==4.1.1
urllib3==2.3.0
uv==0.5.29
uvicorn==0.23.2
uvloop==0.19.0
validators==0.34.0
w3lib @ file:///Users/<USER>/demo/mc3/conda-bld/w3lib_1629359764703/work
watchdog @ file:///Users/<USER>/work/recipes/ci_py311/watchdog_1677963700938/work
watchfiles==0.19.0
wcwidth @ file:///Users/<USER>/demo/mc3/conda-bld/wcwidth_1629357192024/work
weaviate-client==4.13.2
webencodings==0.5.1
websocket-client @ file:///Users/<USER>/work/recipes/ci_py311/websocket-client_1677918996745/work
websockets==12.0
Werkzeug @ file:///private/var/folders/nz/j6p8yfhx1mv_0grj5xl4650h0000gp/T/abs_fc9kcczuwd/croot/werkzeug_1679489745296/work
whatthepatch @ file:///Users/<USER>/work/recipes/ci_py311/whatthepatch_1677934976505/work
widgetsnbextension @ file:///private/var/folders/nz/j6p8yfhx1mv_0grj5xl4650h0000gp/T/abs_cd_nvw5x1_/croot/widgetsnbextension_1679313872684/work
wolframalpha==5.0.0
wrapt @ file:///Users/<USER>/work/recipes/ci_py311/wrapt_1677925966862/work
wsproto==1.2.0
wurlitzer @ file:///Users/<USER>/work/recipes/ci_py311/wurlitzer_1677955854875/work
xarray @ file:///private/var/folders/nz/j6p8yfhx1mv_0grj5xl4650h0000gp/T/abs_a14bvvrzzp/croot/xarray_1689041477812/work
XlsxWriter==3.2.2
xlwings @ file:///Users/<USER>/work/recipes/ci_py311_2/xlwings_1678996173448/work
xmltodict==0.13.0
xxhash==3.5.0
xyzservices @ file:///Users/<USER>/work/recipes/ci_py311/xyzservices_1677927443768/work
y-py @ file:///private/var/folders/nz/j6p8yfhx1mv_0grj5xl4650h0000gp/T/abs_58d555zc6u/croot/y-py_1683555409055/work
yapf @ file:///tmp/build/80754af9/yapf_1615749224965/work
yarg==0.1.9
yarl==1.18.3
youtube-transcript-api==0.6.2
ypy-websocket @ file:///private/var/folders/nz/j6p8yfhx1mv_0grj5xl4650h0000gp/T/abs_e638ipunz1/croot/ypy-websocket_1684192343550/work
yt-dlp==2023.12.30
zict @ file:///private/var/folders/nz/j6p8yfhx1mv_0grj5xl4650h0000gp/T/abs_06f3lix4ji/croot/zict_1682698748419/work
zipp @ file:///Users/<USER>/work/recipes/ci_py311/zipp_1677907997878/work
zope.interface @ file:///Users/<USER>/work/recipes/ci_py311/zope.interface_1678055276546/work
zstandard==0.23.0
