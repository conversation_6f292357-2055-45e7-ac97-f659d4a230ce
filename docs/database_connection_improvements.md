# 数据库连接断开问题解决方案

## 问题描述

长时间没有请求导致 `get_sql_conn()` 获取到的数据库连接断开，造成应用程序无法正常工作。

## 根本原因

1. **没有连接池管理**：每次调用都创建新连接，没有复用机制
2. **缺乏连接健康检查**：无法检测连接是否仍然有效
3. **没有自动重连机制**：连接断开后无法自动恢复
4. **缺少连接超时配置**：没有合适的超时和重连参数

## 解决方案

### 1. Python 端改进

#### 1.1 连接管理器 (`src/common/mysql_helper.py`)

创建了 `DatabaseConnectionManager` 类，提供以下功能：

- **连接池管理**：复用数据库连接，避免频繁创建/销毁
- **健康检查**：定期使用 `ping()` 检查连接状态
- **自动重连**：连接断开时自动重新建立
- **线程安全**：使用锁保证多线程环境下的安全性

```python
# 关键配置参数
_ping_interval = 300  # 5分钟检查一次连接
_connection_timeout = 28800  # 8小时连接超时
connect_timeout = 60  # 连接超时60秒
read_timeout = 30  # 读取超时30秒
write_timeout = 30  # 写入超时30秒
```

#### 1.2 数据库服务基类 (`src/common/database_service.py`)

创建了 `DatabaseService` 基类，提供：

- **自动重试机制**：数据库操作失败时自动重试
- **错误处理**：统一的错误处理和日志记录
- **便捷方法**：`fetch_one()`, `fetch_all()`, `execute_update()` 等

#### 1.3 改进的初始化函数 (`src/main_online.py`)

在 `init_runner()` 函数中添加：

- **连接健康检查**：启动时验证现有连接是否可用
- **重试机制**：连接失败时使用指数退避重试
- **错误恢复**：连接异常时重新初始化

### 2. Go 端改进

#### 2.1 连接池配置 (`pkg/db/init_db.go`)

添加了 GORM 连接池配置：

```go
// 设置最大打开连接数
sqlDB.SetMaxOpenConns(100)
// 设置最大空闲连接数
sqlDB.SetMaxIdleConns(10)
// 设置连接的最大生存时间
sqlDB.SetConnMaxLifetime(time.Hour)
// 设置连接的最大空闲时间
sqlDB.SetConnMaxIdleTime(time.Minute * 30)
```

#### 2.2 连接字符串优化

在 DSN 中添加超时参数：

```go
dbConnection := fmt.Sprintf("%s:%s@tcp(%s:%d)/%s?charset=utf8mb4&collation=utf8mb4_unicode_ci&parseTime=true&loc=Local&time_zone=%s&timeout=30s&readTimeout=30s&writeTimeout=30s&maxAllowedPacket=16777216",
    mysql.UserName, mysql.Password, mysql.Host, mysql.Port, mysql.DbName, url.QueryEscape(timezone))
```

### 3. 服务层改进

#### 3.1 ConnectorService 重构

将 `ConnectorService` 改为继承 `DatabaseService`：

- **自动重连**：数据库操作自动处理连接断开
- **参数化查询**：防止 SQL 注入，提高安全性
- **错误处理**：统一的错误处理和日志记录
- **CRUD 操作**：完整的增删改查功能

## 使用方法

### 1. 基本使用

```python
from common.mysql_helper import get_sql_conn

# 获取连接（自动管理连接池和重连）
cursor = get_sql_conn()
cursor.execute("SELECT 1")
result = cursor.fetchone()
```

### 2. 使用数据库服务基类

```python
from common.database_service import DatabaseService

class MyService(DatabaseService):
    def get_user(self, user_id):
        return self.fetch_one("SELECT * FROM users WHERE id = %s", (user_id,))
```

### 3. 应用关闭时清理

```python
from common.mysql_helper import close_db_connection

# 应用关闭时调用
close_db_connection()
```

## 测试验证

创建了测试脚本 `src/test/test_db_connection.py`，包含：

- **基本连接测试**：验证连接功能
- **连接复用测试**：验证连接池工作
- **健康检查测试**：验证自动检查机制
- **并发连接测试**：验证多线程安全性
- **错误恢复测试**：验证异常处理

运行测试：

```bash
cd src/test
python test_db_connection.py
```

## 配置参数

### Python 配置

可以通过环境变量调整：

```bash
# 数据库名称
DATABASE_NAME=jarvis_test

# AWS 凭证
AWS_DB_ACCESS_KEY_ID=your_access_key
AWS_DB_SECRET_ACCESS_KEY=your_secret_key
```

### Go 配置

可以通过环境变量调整：

```bash
# 密钥名称
SECRET_NAME=spotmax-maxcloud-rds-hk-admin

# AWS 区域
SECRET_REGION=ap-east-1
```

## 监控和日志

系统会输出以下日志信息：

- 连接创建成功/失败
- 连接健康检查结果
- 自动重连事件
- 数据库操作错误

## 注意事项

1. **连接池大小**：根据应用负载调整连接池参数
2. **超时设置**：根据网络环境调整超时时间
3. **监控告警**：建议添加数据库连接监控告警
4. **定期维护**：定期检查数据库连接状态和性能

## 总结

通过以上改进，解决了数据库连接断开的问题：

✅ **连接池管理**：避免频繁创建连接  
✅ **自动重连**：连接断开时自动恢复  
✅ **健康检查**：定期检查连接状态  
✅ **错误处理**：统一的异常处理机制  
✅ **线程安全**：支持多线程并发访问  
✅ **配置灵活**：可通过参数调整行为  

这些改进大大提高了应用程序的稳定性和可靠性。
